import { Injectable } from '@angular/core';
import go from 'gojs';
import { AttributeType } from 'src/app/shared/model/attribute';
import { GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import {
  CreatedEnumeration,
  Enumeration,
  EnumerationDTO,
  TemplateEnumeration,
  TemplateEnumerationCreation,
} from 'src/app/shared/model/enumeration';
import {
  GojsDiagramEnumerationNode,
  GojsFolderNode,
  GojsNodeCategory,
  UpdateProperties,
} from 'src/app/shared/model/gojs';
import { ProjectDetails } from 'src/app/shared/model/project';
import { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';
import { SharedService } from 'src/app/shared/services/shared.service';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { DataFormatService } from '../../data-format/data-format.service';
import { EnumerationService } from '../../enumeration/enumeration.service';
import { ProjectService } from '../../project/project.service';
import { PropertyService } from '../../property/property.service';
import { TreeNodeService } from '../../treeNode/tree-node.service';
import { GojsCommonService } from '../gojsCommon/gojs-common.service';
import { GojsLiteralService } from '../gojsLiteral/gojs-literal.service';
@Injectable({
  providedIn: 'root',
})
export class GojsEnumerationService {
  private currentDiagram!: Diagram;
  private _gojsDiagram!: go.Diagram;
  private _currentProject!: ProjectDetails;
  constructor(
    private enumerationService: EnumerationService,
    private diagramUtils: DiagramUtils,
    private goJsCommonService: GojsCommonService,
    private goJsLiteralService: GojsLiteralService,
    private sharedService: SharedService,
    private treeNodeService: TreeNodeService,
    private dataFormatService: DataFormatService,
    private propertyService: PropertyService,
    private projectService: ProjectService
  ) {
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });
    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this._gojsDiagram = diagram;
    });
    this.projectService.currentProjectChanges().subscribe((project) => {
      if (project) this._currentProject = project;
    });
  }

  handleEnumCreationOrUpdate(
    enumData: GojsDiagramEnumerationNode,
    diagram: go.Diagram,
    isFromLibrary: boolean,
    event?: go.InputEvent
  ) {
    if (!enumData?.['id'])
      this.createEnum(enumData, diagram, isFromLibrary, event);
    else this.updateExistingEnum(enumData, isFromLibrary);
  }

  private createEnum(
    enumData: GojsDiagramEnumerationNode,
    diagram: go.Diagram,
    isFromLibrary: boolean,
    event?: go.InputEvent
  ) {
    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();
    let enumObj: Enumeration;
    if (
      parentNode &&
      parentNode.category == GojsNodeCategory.Folder &&
      !isFromLibrary
    ) {
      enumObj = {
        ...this.mapEnumDataToDTO(enumData, isFromLibrary),
        idFolder: (parentNode['data'] as GojsFolderNode).idFolder,
      };
    } else {
      const treeNodeData = this.treeNodeService.findNodeByTag(
        enumData.treeNodeTag
      );
      enumObj = {
        ...this.mapEnumDataToDTO(enumData, isFromLibrary),
        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag!),
      };
    }
    this.enumerationService.createEnumeration(enumObj).subscribe({
      next: (value) => {
        this.addEnumToDiagram(value, diagram, enumData, isFromLibrary);
        if (value.templateEnumeration)
          this.handleAdditionalEnumCreationLogic(
            value.templateEnumeration,
            isFromLibrary,
            parentNode!
          );
      },
      error: () => {
        event?.diagram.currentTool.doCancel();
      },
    });
  }

  private mapEnumDataToDTO(
    enumData: GojsDiagramEnumerationNode,
    isFromLibrary: boolean
  ): CreatedEnumeration {
    const enumObj: CreatedEnumeration = {
      name: enumData['name'],
      key: enumData['key'],
      idDiagram: this.currentDiagram.id!,
      property: {
        position: enumData['position']!,
        height: enumData['size'].height,
        width: enumData['size'].width,
        icon: enumData['icon'],
        color: enumData['color'],
      },
      description: enumData['description'],
      tag: enumData['tag'],
      volumetry: enumData['volumetry'],
    };
    return isFromLibrary
      ? {
          ...enumObj,
          idTemplateEnumeration: enumData.idTemplateEnumeration,
          key: +this.sharedService.generateUniqueKey(),
        }
      : enumObj;
  }

  private addEnumToDiagram(
    value: CreatedEnumeration,
    gojsDiagram: go.Diagram,
    enumData: go.ObjectData,
    isFromLibrary: boolean
  ) {
    if (isFromLibrary) {
      gojsDiagram.model.addNodeData(enumData);
    }
    const diagramData = this.diagramUtils.getObjectDataByKey(
      gojsDiagram,
      enumData['key']
    );
    if (diagramData) {
      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {
        id: value.id,
        idTemplateEnumeration: value.templateEnumeration?.id,
        treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${value.templateEnumeration?.id}`,
      });
    }
  }

  private handleAdditionalEnumCreationLogic(
    tempEnum: TemplateEnumeration,
    isFromLibrary: boolean,
    parentNode?: TreeNode
  ) {
    if (!isFromLibrary) {
      this.treeNodeService.addGroupNodeInTree({
        name: tempEnum.name,
        category: GojsNodeCategory.Enumeration,
        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,
        children: [],
        icon: GoJsNodeIcon.Enumeration,
        data: this.dataFormatService.formatDiagramEnumData(tempEnum),
        parentTag:
          parentNode?.category !== GojsNodeCategory.Project
            ? parentNode?.tag
            : this.treeNodeService.getWrapperParentTag(
                TreeNodeTag.EnumerationWrapper
              ),
        isDraggable: true,
        supportingNodes: [GojsNodeCategory.EnumerationLiteral],
      });
      this.diagramUtils.addAttributeTypes({
        id: tempEnum.id?.toString()!,
        name: tempEnum?.name,
        isEnumeration: true,
      });
    }
  }

  updateExistingEnum(
    enumData: GojsDiagramEnumerationNode,
    isFromLibrary: boolean
  ): void {
    this.enumerationService.updateEnumeration({
      ...this.mapEnumDataToDTO(enumData, isFromLibrary),
      id: enumData['id'],
    });
  }

  updateEnumerationFromDiagram(
    enumNodeData: GojsDiagramEnumerationNode,
    goJsDiagram: go.Diagram
  ) {
    const templateEnumObj = {
      id: enumNodeData.idTemplateEnumeration,
      name: enumNodeData.name,
      key: enumNodeData.key,
      color: enumNodeData.color,
      icon: enumNodeData.icon,
      description: enumNodeData.description,
      tag: enumNodeData.tag,
      volumetry: enumNodeData.volumetry,
    };
    this.enumerationService
      .updateTempEnumeration(
        templateEnumObj as TemplateEnumeration,
        this.currentDiagram.id!
      )
      .subscribe((updatedTempEnum) => {
        this.diagramUtils.updateEnumerationType(
          updatedTempEnum.id?.toString()!,
          updatedTempEnum.name,
          goJsDiagram
        );
        this.goJsCommonService.updateNodeDataProperties(
          goJsDiagram.model,
          (node) => node['idTemplateEnumeration'] == updatedTempEnum.id,
          {
            name: updatedTempEnum.name,
          }
        );

        const treeNode = this.treeNodeService.findNodeByTag(
          enumNodeData.treeNodeTag
        );
        if (treeNode)
          this.treeNodeService.editGroupTreeNode({
            ...treeNode,
            name: updatedTempEnum.name,
            data: enumNodeData,
          });
      });
  }

  deleteTempEnumeration(enumData: GojsDiagramEnumerationNode[]) {
    this.enumerationService.deleteTemplateEnums(
      enumData.map((enumObj) => enumObj.idTemplateEnumeration)
    );
    enumData.forEach((enumObj) => {
      this.diagramUtils.removeAttributeType(
        enumObj.idTemplateEnumeration.toString()
      );
      this.goJsCommonService.removeGroupNodeWithItems(
        enumObj?.idTemplateEnumeration,
        enumObj.category,
        this._gojsDiagram
      );
      this.goJsCommonService.updateNodeDataItemsProperties(
        this._gojsDiagram.model,
        (item) => item.dataType == enumObj.idTemplateEnumeration.toString(),
        { dataType: `0_${AttributeType[AttributeType.Undefined]}` }
      );
    });
  }

  formatDiagramEnumerationData(
    enumerations: EnumerationDTO[],
    hasEditAccess: boolean
  ): GojsDiagramEnumerationNode[] {
    const nodeDataArray = enumerations.map((enumObj) => ({
      idTemplateEnumeration: enumObj.idTemplateEnumeration!,
      key: enumObj.key,
      id: enumObj.id,
      color: enumObj.property?.color,
      icon: enumObj.property?.icon,
      name: enumObj.name,
      category: GojsNodeCategory.Enumeration,
      size: enumObj.property
        ? new go.Size(enumObj.property.width, enumObj.property.height)
        : new go.Size(150, 100),
      isGroup: true,
      position: enumObj.property?.position,
      supportingLevels: [GojsNodeCategory.EnumerationLiteral],
      isHighlighted: true,
      allowTopLevelDrops: true,
      editable: hasEditAccess,
      showTablePanel: true,
      description: enumObj.description,
      tag: enumObj.tag,
      volumetry: enumObj.volumetry,
      items: this.goJsLiteralService.formatLiteralData(
        enumObj.enumerationLiterals,
        enumObj.id!
      ),
      treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${enumObj.idTemplateEnumeration}`,
    }));
    return nodeDataArray;
  }

  updateEnumNode(
    updatedNode: GojsDiagramEnumerationNode,
    gojsDiagram: go.Diagram,
    treeNode: TreeNode
  ) {
    let diagramNode: go.ObjectData[];
    diagramNode = this._gojsDiagram.model.nodeDataArray.filter(
      (item) =>
        item['idTemplateEnumeration'] == updatedNode.idTemplateEnumeration
    );
    this.treeNodeService.editGroupTreeNode({
      ...treeNode,
      name: updatedNode.name,
      data: { ...updatedNode },
      tag: updatedNode.treeNodeTag ?? treeNode.tag,
    });
    if (updatedNode && updatedNode.id && diagramNode) {
      const properties: UpdateProperties = {
        name: updatedNode.name,
        color: updatedNode.color,
        description: updatedNode.description,
        tag: updatedNode.tag,
        volumetry: updatedNode.volumetry,
      };
      this.goJsCommonService.commitGroupNodeData(
        diagramNode,
        properties,
        gojsDiagram
      );
    }
  }

  /**
   * Handles the renaming of an enumeration in the library.
   *
   * This method updates the name of an enumeration node in both the main diagram and the palette.
   * It first sends an update request to the backend to modify the enumeration's name and other properties.
   * Once the update is successful, it propagates the changes to the relevant nodes in the diagram and palette.
   *
   * @param enumNode - The GoJS node representing the enumeration to be renamed.
   * @param enumName - The new name for the enumeration.
   * @param diagram - The main GoJS diagram where the enumeration is used.
   * @param palette - The GoJS palette diagram where the enumeration node resides.
   */
  handleEditEnumNameInLibrary(
    enumNode: GojsDiagramEnumerationNode,
    treeNode: TreeNode
  ): void {
    this.enumerationService
      .updateTempEnumeration(
        {
          id: enumNode.idTemplateEnumeration,
          key: enumNode.key?.toString().split('_')[0],
          icon: enumNode.icon,
          color: enumNode.color,
          name: treeNode.name,
        },
        this.currentDiagram.id!
      )
      .subscribe((updatedTempEnum) => {
        this.diagramUtils.updateEnumerationType(
          updatedTempEnum.id?.toString()!,
          updatedTempEnum.name,
          this._gojsDiagram
        );
        // Update the name in the main diagram if the enumeration is used.
        this.goJsCommonService.updateNodeDataProperties(
          this._gojsDiagram.model,
          (node) =>
            node['idTemplateEnumeration'] == enumNode['idTemplateEnumeration'],
          {
            name: treeNode.name,
          }
        );

        // Update the name in the palette.
        this.treeNodeService.editGroupTreeNode({
          ...treeNode,
          data: {
            ...treeNode.data,
            name: treeNode.name,
          } as GojsDiagramEnumerationNode,
        });
        this.propertyService.setPropertyData({
          ...enumNode,
          name: treeNode.name,
        });
      });
  }

  handleEnumerationCreationFromLibrary(
    enumName: string,
    wrapperNode: TreeNode
  ): void {
    const parentNode = this.treeNodeService.findNodeByTag(
      wrapperNode.category === GojsNodeCategory.Folder
        ? wrapperNode.tag!
        : wrapperNode.parentTag!
    );

    const enumObj: TemplateEnumerationCreation = {
      name: enumName,
      key: 0,
      icon: GoJsNodeIcon.Enumeration,
      color: 'rgba(128,128,128,0.5)',
      idProject: this._currentProject.id,
      description: '',
      tag: '',
      volumetry: '',
      idFolder:
        parentNode && parentNode.category == GojsNodeCategory.Folder
          ? (parentNode.data as GojsFolderNode)?.idFolder
          : 0,
    };

    this.enumerationService
      .createNewTemplateEnumeration(enumObj)
      .subscribe((createdEnum) => {
        this.treeNodeService.addGroupNodeInTree({
          name: createdEnum.name,
          children: [],
          category: GojsNodeCategory.Enumeration,
          icon: createdEnum.icon,
          tag: `atTag${GojsNodeCategory.Enumeration}_${createdEnum.id}`,
          parentTag:
            parentNode && parentNode.category == GojsNodeCategory.Folder
              ? parentNode.tag
              : `${TreeNodeTag.EnumerationWrapper}_${TreeNodeTag.Project}`,
          data: this.dataFormatService.formatTempEnumData(createdEnum),
          isDraggable: true,
          supportingNodes: [GojsNodeCategory.EnumerationLiteral],
        });

        this.diagramUtils.addAttributeTypes({
          id: createdEnum.id?.toString()!,
          name: createdEnum.name,
          isEnumeration: true,
        });
      });
  }
}
