import { EventEmitter, Injectable } from '@angular/core';
import * as go from 'gojs';
import { BehaviorSubject, Observable } from 'rxjs';
import { FolderDTO } from 'src/app/shared/model/class';
import { ExportDiagram } from 'src/app/shared/model/common';
import {
  Diagram,
  DiagramDetails,
  DiagramUpdateDTO,
} from 'src/app/shared/model/diagram';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType, ProjectDetails } from 'src/app/shared/model/project';
import { TreeNode } from 'src/app/shared/model/treeNode';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../access/access.service';
import { DiagramApiService } from '../api/diagram-api.service';
import { GojsCommentService } from '../gojs/gojs-comment/gojs-comment.service';
import { GojsService } from '../gojs/gojs.service';
import { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';
import { GojsCardinalityService } from '../gojs/gojsCardinality/gojs-cardinality.service';
import { GojsClassService } from '../gojs/gojsClass/gojs-class.service';
import { GojsCommonService } from '../gojs/gojsCommon/gojs-common.service';
import { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';
import { GojsFolderService } from '../gojs/gojsFolder/gojs-folder.service';
import { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';
import { PdfExportService } from '../pdf-export/pdf-export.service';
import { ProjectService } from '../project/project.service';
import { PropertyService } from '../property/property.service';
import { SnackBarService } from '../snackbar/snack-bar.service';
import { TreeNodeService } from '../treeNode/tree-node.service';
import { VersionHistoryService } from '../versionHistory/version-history.service';
@Injectable({
  providedIn: 'root',
})
export class DiagramService {
  private diagram!: go.Diagram;
  private hasEditAccessOnly: boolean = false;
  currentDiagram!: Diagram;
  propertyData:
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsFolderNode
    | GojsLinkNode
    | null = null;
  project!: ProjectDetails;
  diagrams: Diagram[] = [];
  downloadDiagramEvent = new EventEmitter<void>();
  downloadAllDiagramEvent = new EventEmitter<boolean>();
  deleteDiagramEvent = new EventEmitter<void>();

  private colorSelectionSubject = new BehaviorSubject<boolean>(false);
  constructor(
    private diagramApiService: DiagramApiService,
    private accessService: AccessService,
    private projectService: ProjectService,
    private propertyService: PropertyService,
    private diagramUtils: DiagramUtils,
    private gojsService: GojsService,
    private gojsClassService: GojsClassService,
    private goJsEnumService: GojsEnumerationService,
    private goJsFolderService: GojsFolderService,
    private gojsCommentService: GojsCommentService,
    private gojsCommonService: GojsCommonService,
    private gojsAttributeService: GojsAttributeService,
    private gojsLiteralService: GojsLiteralService,
    private gojsCardinalityService: GojsCardinalityService,
    private snackBarService: SnackBarService,
    private pdfExportService: PdfExportService,
    private treeNodeService: TreeNodeService,
    private versionHistoryService: VersionHistoryService
  ) {
    this.accessService.accessTypeChanges().subscribe((response) => {
      if (response != AccessType.Viewer) {
        this.hasEditAccessOnly = true;
      } else {
        this.hasEditAccessOnly = false;
      }
    });
    this.projectService.currentProjectChanges().subscribe((project) => {
      if (project) {
        this.project = project;
        this.diagrams = project.diagrams;
        const folderDiagrams = this.getDiagramsFromFolders(
          this.project.folders
        );
        this.diagrams.push(...folderDiagrams);

        // Don't automatically set the first diagram as active here
        // This will be handled by the ProjectService when opening a project
        // with a specific diagram ID, or by the DiagramEditorComponent

        // Just set the current project diagrams
        this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);

        // If there are no diagrams, ensure the active diagram is cleared
        if (this.diagrams.length === 0) {
          this.diagramUtils.setActiveDiagram(null);
        }
      }
    });

    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });

    this.propertyService.propertyDataChanges().subscribe((propertyData) => {
      this.propertyData = propertyData;
    });
    this.gojsCommonService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this.diagram = diagram;
    });
  }

  getDiagramsFromFolders(folders: FolderDTO[]): Diagram[] {
    let diagrams: Diagram[] = [];
    for (const folder of folders) {
      if (folder.diagrams && folder.diagrams.length > 0) {
        diagrams.push(...folder.diagrams);
      }
      if (folder.childFolders && folder.childFolders.length > 0) {
        diagrams.push(...this.getDiagramsFromFolders(folder.childFolders));
      }
    }
    return diagrams;
  }

  setColorSelection(isClicked: boolean) {
    this.colorSelectionSubject.next(isClicked);
  }

  getColorSelection() {
    return this.colorSelectionSubject.asObservable();
  }
  triggerCurrentDiagramDownload() {
    this.downloadDiagramEvent.emit();
  }

  triggerAllDiagramDownload(isForOnlyImage: boolean) {
    this.downloadAllDiagramEvent.emit(isForOnlyImage);
  }

  triggerDelete() {
    this.deleteDiagramEvent.emit();
  }

  getUpdatedProperties(
    updatedNode:
      | GojsDiagramClassNode
      | GojsDiagramEnumerationNode
      | GojsDiagramAttributeNode
      | GojsDiagramLiteralNode
      | GojsLinkNode
      | GojsFolderNode,
    treeNode: TreeNode | null
  ): void {
    if (updatedNode && updatedNode.id && updatedNode.category) {
      if (this.gojsCommonService.isGojsDiagramClassNode(updatedNode)) {
        if (treeNode == null)
          treeNode = this.treeNodeService.findNodeByTag(
            updatedNode.treeNodeTag
          );
        if (treeNode)
          this.gojsClassService.handleClassUpdateInProperty(
            updatedNode,
            this.diagram,
            treeNode
          );
      } else if (
        this.gojsCommonService.isGojsDiagramAttributeNode(updatedNode)
      ) {
        this.gojsAttributeService.handleAttributeUpdateInProperty(
          updatedNode,
          this.diagram,
          treeNode
        );
      } else if (
        this.gojsCommonService.isGojsDiagramEnumerationNode(updatedNode)
      ) {
        if (treeNode == null)
          treeNode = this.treeNodeService.findNodeByTag(
            updatedNode.treeNodeTag
          );
        if (treeNode)
          this.goJsEnumService.updateEnumNode(
            updatedNode,
            this.diagram,
            treeNode
          );
      } else if (this.gojsCommonService.isGojsDiagramLiteralNode(updatedNode)) {
        this.gojsLiteralService.handleLiteralUpdateInProperty(
          updatedNode,
          this.diagram,
          treeNode
        );
      } else if (
        this.gojsCommonService.isGojsPaletteFolderNode(updatedNode) &&
        treeNode
      ) {
        this.goJsFolderService.updateFolderNode(updatedNode, treeNode);
      } else if (this.gojsCommonService.isGojsLinkNode(updatedNode)) {
        this.gojsCardinalityService.updateLinkFromProperty(
          updatedNode,
          this.diagram
        );
      }
    }
  }

  /**
   * Get a Diagram by its id
   * @param {number} diagramId Id of the Diagram to get.
   * @returns {Observable<DiagramDetails>} A promise that resolves with the found Diagram or null if no Diagram was found for the given id.
   * @memberOf DiagramService
   */
  getDiagramById(diagramId: number): Observable<DiagramDetails> {
    return this.diagramApiService.getDiagramById(diagramId);
  }

  /**
   * Create a new Diagram and returns it.
   * @param {Diagram} diagram The Diagram to create.
   * @returns {Observable<Diagram>} The new Diagram.
   * @memberOf DiagramService
   */
  createDiagram(diagram: Diagram): Observable<Diagram> {
    return this.diagramApiService.createDiagram(diagram);
  }

  /**
   * Delete a diagram from the server.
   * @param {number} diagramId ID of the diagram to delete.
   * @return {Observable<Diagram>}
   * @memberof DiagramService
   */
  deleteDiagram(diagramId: number): void {
    this.diagramApiService.deleteDiagram(diagramId).subscribe(() => {
      this.snackBarService.openSnackbar('snackBar.diagramDeleteMsg');
    });
  }

  /**
   *  Update an existing diagram on the server with the provided data.
   * @param {*} diagram  Data for updating the diagram.
   * @returns {Observable<any>}  A promise that resolves when the update is complete.
   * @memberOf DiagramService
   */
  updateDiagram(diagram: DiagramUpdateDTO): Observable<DiagramUpdateDTO> {
    return this.diagramApiService.updateDiagram(diagram);
  }

  exportAllDiagrams(projectId: number): Observable<DiagramDetails[]> {
    return this.diagramApiService.exportAllDiagrams(projectId);
  }
  /**
   *  Get the diagram data  from the server and initialize the model of the Diagram content.
   * @param {number} diagramId is  the id of the selected diagram in the Project detail page
   * @memberof DiagramEditorComponent
   */
  public getDiagramDetails(diagramId: number) {
    if (this.versionHistoryService.selectedVersion() != null) {
      const versionHistory = this.versionHistoryService.selectedVersion();
      if (versionHistory && versionHistory.data != null) {
        const diagrams: DiagramDetails[] = JSON.parse(
          versionHistory.data
        ).diagrams;
        const currentDiagram = diagrams.find(
          (dia: DiagramDetails) => dia.id == diagramId
        );
        if (currentDiagram) this.getCurrentDiagram(currentDiagram);
      }
    } else {
      this.getDiagramById(diagramId).subscribe((diagram) => {
        this.getCurrentDiagram(diagram);
      });
    }
  }

  private async getCurrentDiagram(diagramDetails: DiagramDetails) {
    if (Object.keys(diagramDetails).length > 0) {
      const diagramData = await this.constructDiagramData(diagramDetails);
      this.diagramUtils.setActiveDiagramDetails(diagramData);
      this.diagramUtils.initializeDiagramModelData(
        this.diagram,
        diagramData.nodeDataArray,
        diagramData.linkDataArray
      );
      this.diagram.centerRect(this.diagram.documentBounds);
    }
  }

  async constructDiagramData(diagram: DiagramDetails) {
    const formattedClassData = await this.gojsClassService.formatClassData(
      diagram.classes,
      diagram.linkHistories,
      diagram.id!
    );

    const diagramEnumNodeData =
      this.goJsEnumService.formatDiagramEnumerationData(
        diagram.enumerations,
        this.hasEditAccessOnly
      );
    const diagramCommentNodeData = this.gojsCommentService.formatCommentData(
      diagram.comments,
      this.hasEditAccessOnly
    );

    return {
      nodeDataArray: [
        ...formattedClassData.nodeDataArray,
        ...formattedClassData.linkLabelData,
        ...diagramEnumNodeData,
        ...diagramCommentNodeData,
      ],
      linkDataArray: formattedClassData.linkDataArray,
    };
  }

  getPaletteDiagramDetails() {
    this.gojsService.initPaletteDiagram();
  }

  /**
   * Initiates the download of the current diagram as a PNG image.
   * @memberOf DiagramEditorComponent
   */
  initiateDiagramDownload(isForCurrentDiagram: boolean, isForOnlyImg: boolean) {
    try {
      // Generate image data from the diagram
      if (isForCurrentDiagram) {
        if (this.diagram.model.nodeDataArray.length > 0) {
          this.diagram.commandHandler.zoomToFit();
          this.diagram.makeImageData({
            background: 'white',
            returnType: 'blob',
            callback: (blob: Blob) => {
              this.pdfExportService.downloadFile(
                blob,
                this.currentDiagram.name,
                'png'
              );
            },
          });
        } else {
          this.snackBarService.info('diagram.downloadNotAllowedMsg');
        }
      } else {
        this.exportAllDiagrams(this.project.id!).subscribe((diagrams) => {
          this.downloadAllDiagrams(this.project.id!, diagrams, isForOnlyImg);
        });
      }
    } catch (error) {
      console.error('Failed to initiate diagram download:', error);
    }
  }

  private async downloadAllDiagrams(
    projectId: number,
    diagrams: DiagramDetails[],
    isForOnlyImage: boolean
  ): Promise<void> {
    const treeNodes = this.treeNodeService.descendantTreeNodes;
    if (!treeNodes || treeNodes.length === 0) {
      this.snackBarService.info('diagram.downloadNotAllowedMsg');
      return;
    }

    const diagramBlobs = (
      await Promise.all(
        treeNodes
          .filter((node) => node.category === GojsNodeCategory.Diagram)
          .map(async (node) => {
            const diagram = diagrams.find((dia) => dia.id === node.data?.id);
            if (
              diagram &&
              (diagram.classes.length > 0 ||
                diagram.enumerations.length > 0 ||
                diagram.comments.length > 0)
            ) {
              const imageData = await this.generateImageFromDiagram(diagram);
              return {
                diagramId: diagram.id!,
                image: imageData,
                name: diagram.name,
              };
            }
            return null;
          })
      )
    ).filter((blob): blob is ExportDiagram => blob !== null);

    if (diagramBlobs.length > 0) {
      this.pdfExportService.downloadPdf(
        projectId,
        this.project.name,
        {
          projectId,
          diagrams: diagramBlobs,
        },
        isForOnlyImage
      );
      const diagram = diagrams.find(
        (diagram) => diagram.id == this.currentDiagram.id
      );
      if (diagram) {
        const diagramData = await this.constructDiagramData(diagram);
        this.diagramUtils.initializeDiagramModelData(
          this.diagram,
          diagramData.nodeDataArray,
          diagramData.linkDataArray
        );
      }
    } else {
      this.snackBarService.info('diagram.downloadNotAllowedMsg');
    }
  }

  private async generateImageFromDiagram(
    diagram: DiagramDetails
  ): Promise<string> {
    const diagramData = await this.constructDiagramData(diagram);

    return new Promise((resolve) => {
      const div = document.getElementById('diagramDiv');
      if (!div) return;

      const tempDiagram = go.Diagram.fromDiv(div);
      if (!tempDiagram) return;

      this.diagramUtils.initializeDiagramModelData(
        tempDiagram,
        diagramData.nodeDataArray,
        diagramData.linkDataArray
      );
      tempDiagram.commandHandler.zoomToFit();

      tempDiagram.makeImageData({
        background: 'white',
        callback: (blob) => {
          if (blob) {
            resolve(blob);
          }
        },
        returnType: 'file',
      });
    });
  }
}
