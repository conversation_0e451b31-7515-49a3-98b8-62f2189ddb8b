import { Injectable } from '@angular/core';
import * as go from 'gojs';
import { DeletedLink, LinkToLink } from 'src/app/shared/model/cardinality';
import {
  ClassEntity,
  ClassEntityDTO,
  CreatedClassEntityDTO,
  TemplateClass,
  TemplateClassCreation,
  TemplateDTO,
} from 'src/app/shared/model/class';
import { FormattedClassData, GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import {
  GojsDiagramClassNode,
  GojsFolderNode,
  GojsNodeCategory,
  UpdateProperties,
} from 'src/app/shared/model/gojs';
import { AccessType, ProjectDetails } from 'src/app/shared/model/project';
import { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';
import { SharedService } from 'src/app/shared/services/shared.service';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../../access/access.service';
import { ClassService } from '../../class/class.service';
import { DataFormatService } from '../../data-format/data-format.service';
import { ProjectService } from '../../project/project.service';
import { PropertyService } from '../../property/property.service';
import { TreeNodeService } from '../../treeNode/tree-node.service';
import { GojsAttributeService } from '../gojsAttribute/gojs-attribute.service';
import { GojsCardinalityService } from '../gojsCardinality/gojs-cardinality.service';
import { GojsCommonService } from '../gojsCommon/gojs-common.service';
@Injectable({
  providedIn: 'root',
})
export class GojsClassService {
  private currentDiagram!: Diagram;
  private _gojsDiagram!: go.Diagram;
  private _currentProject!: ProjectDetails;
  private _hasEditAccessOnly: boolean = false;
  constructor(
    private classService: ClassService,
    private diagramUtils: DiagramUtils,
    private goJsCommonService: GojsCommonService,
    private propertyService: PropertyService,
    private sharedService: SharedService,
    private goJsCardinalityService: GojsCardinalityService,
    private goJsAttributeService: GojsAttributeService,
    private treeNodeService: TreeNodeService,
    private dataFormatService: DataFormatService,
    private projectService: ProjectService,
    private accessService: AccessService
  ) {
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });
    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this._gojsDiagram = diagram;
    });
    this.projectService.currentProjectChanges().subscribe((project) => {
      if (project) this._currentProject = project;
    });
    this.accessService.accessTypeChanges().subscribe((response) => {
      this._hasEditAccessOnly = response != AccessType.Viewer;
    });
  }

  /**
   * For drop the class and handle create or update class
   * @param {GojsDiagramClassNode} classData
   * @param {go.InputEvent} event
   * @param {go.Diagram} diagram
   * @memberof GojsClassService
   */
  handleClassCreationOrUpdate(
    classData: GojsDiagramClassNode,
    diagram: go.Diagram,
    isFromLibrary: boolean,
    event?: go.InputEvent
  ) {
    if (!classData?.id)
      this.createClass(classData, diagram, isFromLibrary, event);
    else this.updateExistingClass(classData, isFromLibrary);
  }

  /**
   * Creates a new class on the diagram.
   * @param classData - The data of the class to be created.
   * @param event - The GoJS input event.
   */
  private createClass(
    classData: GojsDiagramClassNode,
    diagram: go.Diagram,
    isFromLibrary: boolean,
    event?: go.InputEvent
  ) {
    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();
    const isAssociative =
      classData.category == GojsNodeCategory.AssociativeClass ? true : false;
    let classObj: ClassEntity;
    if (
      parentNode &&
      parentNode.category == GojsNodeCategory.Folder &&
      !isFromLibrary
    ) {
      classObj = {
        ...this.mapClassData(classData, isFromLibrary),
        idFolder: (parentNode['data'] as GojsFolderNode).idFolder,
        isAssociative,
      };
    } else {
      const treeNodeData = this.treeNodeService.findNodeByTag(
        classData.treeNodeTag
      );
      classObj = {
        ...this.mapClassData(classData, isFromLibrary),
        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag!),
        isAssociative,
      };
    }
    this.classService.createNewClass(classObj).subscribe({
      next: (value) => {
        this.addClassToDiagram(value, diagram, classData, isFromLibrary);
        this.handleAdditionalClassCreationLogic(
          {
            attributes: [],
            ...value.templateClass,
            links: [],
            isAssociative,
          },
          classData,
          diagram,
          isFromLibrary,
          parentNode!
        );
      },
      error: () => {
        event?.diagram.currentTool.doCancel();
      },
    });
  }

  /**
   * Maps class data to the DTO required by the class service.
   * @param classData - The data of the class.
   * @returns The mapped class DTO.
   */
  private mapClassData(
    classData: GojsDiagramClassNode,
    isFromLibrary: boolean
  ): ClassEntity {
    const classObj: ClassEntity = {
      name: classData.name,
      key: classData.key,
      idDiagram: this.currentDiagram.id!,
      property: {
        position: classData.position!,
        height: classData.size.height,
        width: classData.size.width,
        icon: classData.icon,
        color: classData.color,
      },
      description: classData.description,
      tag: classData.tag,
      volumetry: classData.volumetry,
      isAssociative: classData.isAssociative,
    };
    return isFromLibrary
      ? {
          ...classObj,
          idTemplateClass: classData.idTemplateClass,
          key: +this.sharedService.generateUniqueKey(),
        }
      : classObj;
  }

  private formatDiagramClassNode(classObj: ClassEntityDTO) {
    return {
      idTemplateClass: classObj.idTemplateClass!,
      idDiagram: classObj.idDiagram,
      key: `${classObj.key}`,
      id: classObj.id,
      color: classObj.property?.color,
      icon: classObj.property?.icon,
      name: classObj.name,
      category: classObj.isAssociative
        ? GojsNodeCategory.AssociativeClass
        : GojsNodeCategory.Class,
      size: classObj.property
        ? new go.Size(classObj.property.width, classObj.property.height)
        : new go.Size(150, 100),
      isGroup: true,
      position: classObj.property?.position,
      supportingLevels: [
        GojsNodeCategory.Attribute,
        GojsNodeCategory.Operation,
      ],
      isHighlighted: true,
      allowTopLevelDrops: true,
      editable: this._hasEditAccessOnly,
      description: classObj.description,
      tag: classObj.tag,
      volumetry: classObj.volumetry,
      items:
        this.goJsAttributeService.formatAttribute(
          classObj.attributes,
          classObj.id!,
          this._hasEditAccessOnly
        ) ?? [],
      showTablePanel: true,
      treeNodeTag: `atTag${GojsNodeCategory.Class}_${classObj.idTemplateClass}`,
      isAssociative: classObj.isAssociative,
    };
  }

  /**
   * Adds the newly created class to the diagram.
   * @param value - The data of the created class.
   * @param classData - The data of the class.
   */
  private addClassToDiagram(
    value: CreatedClassEntityDTO,
    gojsDiagram: go.Diagram,
    classData: GojsDiagramClassNode,
    isFromLibrary: boolean
  ) {
    if (isFromLibrary) {
      gojsDiagram.model.addNodeData(classData);
    }
    const diagramData = this.diagramUtils.getObjectDataByKey(
      gojsDiagram,
      classData.key
    );
    if (diagramData) {
      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {
        id: value.id,
        idTemplateClass: value.templateClass.id,
        isAssociative: value.isAssociative,
        treeNodeTag: `atTag${GojsNodeCategory.Class}_${value.templateClass.id}`,
      });
    }
  }

  /**
   * Handles additional logic after creating a class.
   * @param value - The data of the created class.
   * @param classData - The data of the class.
   */
  private handleAdditionalClassCreationLogic(
    tempCls: TemplateClass,
    classData: GojsDiagramClassNode,
    diagram: go.Diagram,
    isFromLibrary: boolean,
    parentNode?: TreeNode
  ) {
    if (!isFromLibrary) {
      this.treeNodeService.addGroupNodeInTree({
        name: tempCls.name,
        category: tempCls.isAssociative
          ? GojsNodeCategory.AssociativeClass
          : GojsNodeCategory.Class,
        tag: `atTag${GojsNodeCategory.Class}_${tempCls.id}`,
        children: [],
        icon: tempCls.isAssociative
          ? GoJsNodeIcon.Associative
          : GoJsNodeIcon.Class,
        data: this.dataFormatService.formatDiagramClassNode(tempCls),
        parentTag:
          parentNode?.category !== GojsNodeCategory.Project
            ? parentNode?.tag
            : this.treeNodeService.getWrapperParentTag(
                TreeNodeTag.ClassWrapper
              ),
        isDraggable: true,
        supportingNodes: [
          GojsNodeCategory.Attribute,
          GojsNodeCategory.Operation,
        ],
      });
    } else {
      this.goJsCardinalityService.createLinksForPaletteClass(
        classData.idTemplateClass,
        diagram
      );
    }
  }

  addAssociativeClassAutomatically(
    linkToLink: LinkToLink,
    diagram: go.Diagram,
    isFromLibrary: boolean
  ) {
    if (linkToLink) {
      const treeNodeData = this.treeNodeService.findNodeByTag(
        `atTag${GojsNodeCategory.Class}_${linkToLink.idAssociativeClass}`
      );

      if (treeNodeData && treeNodeData.data) {
        this.createClass(
          treeNodeData.data as GojsDiagramClassNode,
          diagram,
          isFromLibrary
        );
      }
    }
  }

  /**
   * Updates an existing class on the diagram.
   * @param classData - The data of the class to be updated.
   */
  private updateExistingClass(
    classData: GojsDiagramClassNode,
    isFromLibrary: boolean
  ): void {
    this.classService.updateClass({
      ...this.mapClassData(classData, isFromLibrary),
      id: classData.id,
    });
  }

  updateTemplateClass(
    classNodeData: GojsDiagramClassNode,
    goJsDiagram: go.Diagram
  ) {
    const templateClass: TemplateDTO = {
      id: classNodeData.idTemplateClass,
      name: classNodeData.name,
      key: classNodeData.key,
      color: classNodeData.color,
      icon: classNodeData.icon,
      description: classNodeData.description,
      tag: classNodeData.tag,
      volumetry: classNodeData.volumetry,
      isAssociative:
        classNodeData.category == GojsNodeCategory.AssociativeClass
          ? true
          : false,
    };
    this.classService
      .updateTemplateClass(templateClass, this.currentDiagram.id!)
      .subscribe((cls) => {
        this.goJsCommonService.updateNodeDataProperties(
          goJsDiagram.model,
          (node) => node['idTemplateClass'] === templateClass.id,
          { name: cls.name }
        );
        const treeNode = this.treeNodeService.findNodeByTag(
          classNodeData.treeNodeTag
        );
        if (treeNode)
          this.treeNodeService.editGroupTreeNode({
            ...treeNode,
            name: cls.name,
            data: {
              ...classNodeData,
              items: [],
            },
          });
      });
  }

  deleteTempClass(classData: GojsDiagramClassNode[]) {
    this.classService.deleteTemplateClasses(
      classData.map((classObj) => classObj.idTemplateClass)
    );
    classData.forEach((cls) => {
      this.goJsCommonService.removeGroupNodeWithItems(
        cls.idTemplateClass,
        cls.category,
        this._gojsDiagram
      );
      if (cls.category == GojsNodeCategory.AssociativeClass) {
        this.goJsCommonService.removeLinkToLink(
          this._gojsDiagram,
          (link) =>
            link.idAssociativeClass === cls.idTemplateClass &&
            link.category == GojsNodeCategory.LinkToLink
        );
      }
      this.goJsCommonService.removeAssociationLink(this._gojsDiagram, cls);
    });
  }

  /**
   *  Format the Class Data to match the structure required by GoJS for displaying the elements .
   * @param {IClassDTO[]} classes  are the list of Classes returned
   * @return {*}
   * @memberof DiagramEditorComponent
   */
  async formatClassData(
    classes: ClassEntityDTO[],
    linkHistories: DeletedLink[],
    diagramId: number
  ): Promise<FormattedClassData> {
    const nodeDataArray: GojsDiagramClassNode[] = [];
    const formattedLinkData = this.goJsCardinalityService.formatLinkData(
      classes,
      linkHistories,
      diagramId
    );
    const formatLinkLabelData = formattedLinkData.map((link) => ({
      key: `${link.id}_${GojsNodeCategory.LinkLabel}`,
      category: GojsNodeCategory.LinkLabel,
      idLink: link.id,
      editable: link.editable,
    }));
    const linkToLinkNodes =
      await this.goJsCardinalityService.generateLinkToLinkNodes(
        formattedLinkData,
        classes
      );
    classes.forEach((classObj) => {
      nodeDataArray.push(this.formatDiagramClassNode(classObj));
    });
    return {
      nodeDataArray,
      linkLabelData: formatLinkLabelData,
      linkDataArray: [...formattedLinkData, ...linkToLinkNodes],
    };
  }

  /**
   *
   * For updating the class in library tree
   * @param {GojsDiagramClassNode} response
   * @param {go.Diagram} gojsDiagram
   * @param {TreeNode} treeNode
   * @memberof GojsClassService
   */
  handleClassUpdateInProperty(
    response: GojsDiagramClassNode,
    gojsDiagram: go.Diagram,
    treeNode: TreeNode
  ): void {
    let diagramNode: go.ObjectData[];
    diagramNode = gojsDiagram.model.nodeDataArray.filter(
      (item) => item['idTemplateClass'] == response.idTemplateClass
    );
    this.treeNodeService.editGroupTreeNode({
      ...treeNode,
      name: response.name,
      data: { ...response, id: response.idTemplateClass },
      tag: response.treeNodeTag ?? treeNode.tag,
    });
    if (response && response.id && diagramNode) {
      const properties: UpdateProperties = {
        name: response.name,
        color: response.color,
        description: response.description,
        tag: response.tag,
        volumetry: response.volumetry,
      };
      this.goJsCommonService.commitGroupNodeData(
        diagramNode,
        properties,
        gojsDiagram
      );
    }
  }

  /**
   * Handles the editing of a class name within the library.
   * Updates the name of the class in both the main diagram and the palette.
   * @param classNode - The class node being edited.
   * @param TreeNode - The node for library.
   */
  handleEditClassNameInLibrary(
    classNode: GojsDiagramClassNode,
    treeNode: TreeNode
  ): void {
    this.classService
      .updateTemplateClass(
        {
          id: classNode.idTemplateClass!,
          key: classNode.key?.toString().split('_')[0],
          icon: classNode.icon,
          color: classNode.color,
          name: treeNode.name,
          isAssociative: classNode.isAssociative,
        },
        this.currentDiagram.id!
      )
      .subscribe(() => {
        this.goJsCommonService.updateNodeDataProperties(
          this._gojsDiagram.model,
          (node) => node['idTemplateClass'] == classNode['idTemplateClass'],
          { name: treeNode.name }
        );
        this.treeNodeService.editGroupTreeNode({
          ...treeNode,
          data: {
            ...treeNode.data,
            name: treeNode.name,
          } as GojsDiagramClassNode,
        });
        this.propertyService.setPropertyData({
          ...classNode,
          name: treeNode.data?.name!,
        });
      });
  }

  handleClassCreationFromLibrary(
    className: string,
    classWrapperNode: TreeNode,
    isAssociative: boolean
  ) {
    const parentNode = this.treeNodeService.findNodeByTag(
      classWrapperNode.category === GojsNodeCategory.Folder
        ? classWrapperNode.tag!
        : classWrapperNode.parentTag!
    );
    const classObj: TemplateClassCreation = {
      name: className,
      key: 0,
      idProject: this._currentProject.id,
      icon: GoJsNodeIcon.Class,
      color: 'rgba(128,128,128,0.5)',
      description: '',
      tag: '',
      volumetry: '',
      idFolder:
        parentNode && parentNode.category == GojsNodeCategory.Folder
          ? (parentNode.data as GojsFolderNode)?.idFolder
          : 0,
      isAssociative: isAssociative,
    };
    this.classService
      .createNewTemplateClass(classObj)
      .subscribe((createdClass) => {
        this.treeNodeService.addGroupNodeInTree({
          name: createdClass.name,
          children: [],
          category: createdClass.isAssociative
            ? GojsNodeCategory.AssociativeClass
            : GojsNodeCategory.Class,
          icon: createdClass.isAssociative
            ? GoJsNodeIcon.Associative
            : GoJsNodeIcon.Class,
          tag: `atTag${GojsNodeCategory.Class}_${createdClass.id}`,
          parentTag:
            parentNode && parentNode.category == GojsNodeCategory.Folder
              ? parentNode.tag
              : `${TreeNodeTag.ClassWrapper}_${TreeNodeTag.Project}`,
          data: this.dataFormatService.formatDiagramClassNode(createdClass),
          isDraggable: true,
          supportingNodes: [
            GojsNodeCategory.Operation,
            GojsNodeCategory.Attribute,
          ],
        });
      });
  }
}
