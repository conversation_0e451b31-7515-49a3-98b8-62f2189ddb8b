import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  CardinalityCreate,
  CardinalityDetails,
  CardinalityDTO,
  CardinalityPatch,
  CreatedCardinality,
  DeletedLink,
  ILinkType,
  LinkPort,
  LinkPortPatch,
  LinkToLink,
} from 'src/app/shared/model/cardinality';
import { FolderDTO } from 'src/app/shared/model/class';
import { ProjectDetails } from 'src/app/shared/model/project';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { CardinalityApiService } from '../api/cardinality-api.service';

@Injectable({
  providedIn: 'root',
})
export class CardinalityService {
  private links: CardinalityDetails[] = [];
  private linkToLinks: LinkToLink[] = [];
  private linkTypes: Map<number, ILinkType> = new Map<number, ILinkType>();

  /**
   * Creates an instance of CardinalityService.
   * @param {CardinalityApiService} linkApiService
   *
   * @memberOf CardinalityService
   */
  constructor(
    private linkApiService: CardinalityApiService,
    private diagramUtils: DiagramUtils
  ) {}

  // Flag to track if link types have been loaded
  private linkTypesLoaded = false;

  /**
   * Initializes link types by fetching them from the API.
   * Caches the results to avoid unnecessary API calls.
   * @param {boolean} [forceRefresh=false] - If true, forces a refresh of the link types even if they've been loaded before
   */
  initLinkTypes(forceRefresh: boolean = false) {
    // Only fetch link types if they haven't been loaded yet or if a refresh is forced
    if (!this.linkTypesLoaded || forceRefresh) {
      this.getAllLinkTypes().subscribe((links) => {
        const choices: ILinkType[] = [
          { from: '0..1', to: '0..1' },
          { from: '0..1', to: '1' },
          { from: '0..1', to: '*' },
          { from: '0..1', to: '1..*' },
          { from: '1', to: '0..1' },
          { from: '1', to: '1' },
          { from: '1', to: '*' },
          { from: '1', to: '1..*' },
          { from: '*', to: '0..1' },
          { from: '*', to: '1' },
          { from: '*', to: '*' },
          { from: '*', to: '1..*' },
          { from: '1..*', to: '0..1' },
          { from: '1..*', to: '1' },
          { from: '1..*', to: '*' },
          { from: '1..*', to: '1..*' },
        ];
        links.forEach((linkType) => {
          this.linkTypes.set(linkType.id!, choices[linkType.linkTypeCode]);
        });
        this.linkTypesLoaded = true;
      });
    }
  }

  public getLinkTypes(): Map<number, ILinkType> {
    return this.linkTypes;
  }

  /**
   * Returns an array of CardinalityDetails objects representing the links.
   * @returns {CardinalityDetails[]} An array of CardinalityDetails objects.
   * @memberOf CardinalityService
   */
  public getLinks(): CardinalityDetails[] {
    return this.links;
  }

  public getLinkToLinks(): LinkToLink[] {
    return this.linkToLinks;
  }

  /**
   * Adds a new CardinalityDetails object to the links array.
   * @param {CardinalityDetails} link The new CardinalityDetails object to add.
   * @memberOf CardinalityService
   */
  public addLink(link: CardinalityDetails) {
    if (!this.links.find((l) => l.id === link.id)) this.links.push(link);
  }

  public addLinkToLink(linkToLink: LinkToLink) {
    if (!this.linkToLinks.find((l) => l.id === linkToLink.id))
      this.linkToLinks.push(linkToLink);
  }
  public removeLinkToLink(idLinkToLink: number) {
    this.linkToLinks = this.linkToLinks.filter(
      (link) => link.id != idLinkToLink
    );
  }

  /**
   * Modifies an existing CardinalityDetails object in the links array.
   * @param {CardinalityDetails} link The modified link.
   * @memberOf CardinalityService
   */
  public modifyLink(link: CardinalityDetails) {
    const index = this.links.findIndex((l) => l.id === link.id);
    this.links[index] = link;
  }
  public modifyLinkToLink(linkToLink: LinkToLink) {
    const index = this.linkToLinks.findIndex((l) => l.id === linkToLink.id);
    this.linkToLinks[index] = linkToLink;
  }

  public addNewLinkPort(linkId: number, linkPort: LinkPort) {
    const existingLink = this.links.find((l) => l.id === linkId);
    if (existingLink) {
      existingLink.linkPorts.push(linkPort);
    }
  }
  public removeExistingLinkPort(linkId: number, portId: number) {
    const existingLink = this.links.find((l) => l.id === linkId);
    if (existingLink) {
      const updatedPorts: LinkPort[] = existingLink.linkPorts.filter(
        (p) => p.id !== portId
      );
      existingLink.linkPorts = updatedPorts;
    }
  }
  /**
   * Returns the CardinalityDetails object from the links array with the specified id.
   *
   * @param {number} id The id of the link to retrieve.
   * @returns {CardinalityDetails} The link with the specified id.
   *
   * @memberOf CardinalityService
   */
  getLinkById(id: number): CardinalityDetails {
    return this.links.find((link) => link.id === id)!;
  }

  /**
   * Adds multiple CardinalityDetails objects to the links array.
   *
   * @param {CardinalityDetails[]} links The cardinality objects to add.
   *
   * @memberOf CardinalityService
   */
  public setLinks(links: CardinalityDetails[]) {
    this.links.push(...links);
  }
  public setLinkToLinks(links: LinkToLink[]) {
    this.linkToLinks.push(...links);
  }

  /**
   *  Removes all CardinalityDetails objects from the links array.
   *
   * @memberOf CardinalityService
   */
  public clearLinks() {
    this.links = [];
  }

  public clearLinkToLinks() {
    this.linkToLinks = [];
  }

  /**
   * Removes the CardinalityDetails object from the links array with the specified id.
   *
   * @param {number} idLink The id of the link to remove.
   *
   * @memberOf CardinalityService
   */
  public removeLink(idLink: number) {
    this.links = this.links.filter((link) => link.id !== idLink);
  }

  /**
   * Removes multiple CardinalityDetails objects from the links array.
   *
   * @param {CardinalityDetails[]} links The links to remove.
   *
   * @memberOf CardinalityService
   */
  public removeLinks(links: CardinalityDetails[]) {
    this.links = this.links.filter(
      (link) => !links.find((linkToRemove) => linkToRemove.id === link.id)
    );
  }

  public createDeletedLinkHistory(deletedLink: DeletedLink): void {
    this.linkApiService.createDeletedLinkHistory(deletedLink).subscribe({
      next: (response) => {
        this.diagramUtils.addDeletedLink(response);
      },
      error: () => {},
    });
  }

  public removeLinkHistory(idLinkHistory: number): void {
    this.linkApiService.removeDeletedLinkHistory(idLinkHistory).subscribe();
  }

  /**
   * Undo link deletion
   *
   * @param {number} idLink link id
   * @returns void
   *
   * @memberOf ClassService
   */
  undoLinkDeletion(idLink: number): void {
    this.linkApiService.undoLinkDelete(idLink).subscribe();
  }
  /**
   * Get all cardinality types
   *
   * @returns {Observable<CardinalityTypeDTO[]>}
   *
   * @memberOf CardinalityService
   */
  getAllLinkTypes(): Observable<CardinalityDTO[]> {
    return this.linkApiService.getAllLinkType();
  }

  /**
   * Get all cardinalities for a specific class
   *
   * @param {number} idClass The class id to retrieve the cardinalities
   * @returns {Observable<CardinalityDetails[]>}
   *
   * @memberOf CardinalityService
   */
  getAllLinks(idClass: number): Observable<CardinalityDetails[]> {
    return this.linkApiService.getAllLinks(idClass);
  }

  /**
   * Create new cardinality
   *
   * @param {CardinalityDetails} link A cardinality to create
   * @returns {Observable<CardinalityDetails>}
   *
   * @memberOf CardinalityService
   */
  createNewLink(link: CardinalityCreate): Observable<CreatedCardinality> {
    return this.linkApiService.createLink(link);
  }

  /**
   * Update existing link with updated data
   *
   * @param {CardinalityPatch} link The link to update
   * @returns {Observable<CardinalityPatch>}
   *
   * @memberOf CardinalityService
   */
  updateLink(link: CardinalityPatch): Observable<CardinalityPatch> {
    return this.linkApiService.updateLink(link);
  }

  /**
   * Delete a link from the database by its ID
   *
   * @param {number} idLink The ID of the link to delete
   *
   * @memberOf CardinalityService
   */
  delete(idLink: number): void {
    this.linkApiService.deleteLink(idLink).subscribe();
  }

  setProjectLinks(project: ProjectDetails): void {
    const links: CardinalityDetails[] = [];
    project.diagrams = project.diagrams.map((diagram) => {
      return { ...diagram, idProject: project.id! };
    });
    this.getLinkFromFolder(project.folders, links);
    project.templateClasses.forEach((templateClass) => {
      links.push(...templateClass.links);
    });
    const linkToLinks: LinkToLink[] = links
      .filter((link) => !!link.linkToLink)
      .map((link) => link.linkToLink!);
    this.setLinkToLinks(linkToLinks);
    this.setLinks(links);
  }

  /**
   * Recursively extracts links from a hierarchy of folders and appends them to the provided links array.
   *
   * @param folders - An array of FolderDTO objects representing the folder hierarchy.
   * @param links - An array of CardinalityDetails objects where the extracted links will be appended.
   */
  private getLinkFromFolder(
    folders: FolderDTO[],
    links: CardinalityDetails[]
  ): void {
    folders.forEach((folder) => {
      folder.templateClasses?.forEach((tempCls) => {
        links.push(...tempCls.links);
      });
      if (folder.childFolders.length > 0) {
        this.getLinkFromFolder(folder.childFolders, links);
      }
    });
  }

  createLinkPort(linkPort: LinkPort): Observable<LinkPort> {
    return this.linkApiService.createLinkPort(linkPort);
  }
  updateLinkPort(linkPort: LinkPortPatch): Observable<LinkPort> {
    return this.linkApiService.updateLinkPort(linkPort);
  }
  deleteLinkPort(idLinkPort: number): void {
    this.linkApiService.deleteLinkPort(idLinkPort).subscribe();
  }
  createLinkToLink(linkToLink: LinkToLink): Observable<LinkToLink> {
    return this.linkApiService.createLinkToLink(linkToLink);
  }
  updateLinkToLink(linkToLink: LinkToLink) {
    return this.linkApiService
      .updateLinkToLink(linkToLink)
      .subscribe((updatedLink) => {
        this.modifyLinkToLink(updatedLink);
      });
  }

  /**
   * Delete a linkToLink from the database by its ID
   *
   * @param {number} idLinkToLink The ID of the linkToLink to delete
   * @memberof CardinalityService
   */
  deleteLinkToLink(idLinkToLink: number): void {
    this.linkApiService.deleteLinkToLink(idLinkToLink).subscribe();
  }

  /**
   * Delete a linkToLink from the database by its ID
   *
   * @param {number} idLinkToLink The ID of the linkToLink to delete
   * @memberof CardinalityService
   */
  undoLinkToLinkDeletion(idLinkToLink: number): void {
    this.linkApiService.undoLinkToLinkDelete(idLinkToLink).subscribe();
  }
}
