{"ast": null, "code": "import { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { DiagramWrapperCategory } from 'src/app/shared/utils/constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../folder/folder.service\";\nimport * as i2 from \"../../snackbar/snack-bar.service\";\nimport * as i3 from \"../../treeNode/tree-node.service\";\nimport * as i4 from \"../gojsCommon/gojs-common.service\";\nimport * as i5 from \"../gojsCardinality/gojs-cardinality.service\";\nimport * as i6 from \"src/app/shared/utils/diagram-utils\";\nimport * as i7 from \"../../property/property.service\";\nexport class GojsFolderService {\n  constructor(folderService, snackBarService, treeNodeService, gojsCommonService, gojsCardinalityService, diagramUtils, propertyService) {\n    this.folderService = folderService;\n    this.snackBarService = snackBarService;\n    this.treeNodeService = treeNodeService;\n    this.gojsCommonService = gojsCommonService;\n    this.gojsCardinalityService = gojsCardinalityService;\n    this.diagramUtils = diagramUtils;\n    this.propertyService = propertyService;\n    this.diagrams = [];\n    this.gojsCommonService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this._gojsDiagram = diagram;\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      this.diagrams = diagrams;\n    });\n  }\n  /**\n   * Handles the creation of a new folder in the GoJS palette diagram.\n   *\n   * This method adds a new folder node to the GoJS palette diagram and makes a service call to create the folder in the backend.\n   * After the folder is created successfully, it updates the node with the new folder's ID and selects the node in the palette.\n   *\n   * @param idProject - The ID of the project to which the new folder will belong.\n   * @param goJsPaletteDiagram - The GoJS palette diagram where the new folder will be added.\n   * @param hasEditAccess - A boolean indicating whether the user has edit access to the folder.\n   */\n  onCreateNewFolder(name, idProject, hasEditAccess, parentTag) {\n    this.folderService.createNewFolder({\n      name: name,\n      icon: GoJsNodeIcon.Folder,\n      idProject: idProject\n    }).subscribe(response => {\n      const newNodeData = {\n        name: response.name,\n        icon: GoJsNodeIcon.Folder,\n        allowTopLevelDrops: true,\n        editable: hasEditAccess,\n        // isPalette: true,\n        isGroup: false,\n        isFolder: true,\n        category: GojsNodeCategory.Folder,\n        showTablePanel: false,\n        idFolder: response.id,\n        key: 0\n      };\n      this.treeNodeService.addGroupNodeInTree({\n        name: response.name,\n        category: GojsNodeCategory.Folder,\n        tag: `atTag${GojsNodeCategory.Folder}_${response.id}`,\n        children: [],\n        icon: GoJsNodeIcon.Folder,\n        data: newNodeData,\n        parentTag: parentTag ?? TreeNodeTag.Project,\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.Class, GojsNodeCategory.AssociativeClass, GojsNodeCategory.Enumeration, GojsNodeCategory.Folder, GojsNodeCategory.Diagram]\n      });\n    });\n  }\n  /**\n   * Deletes a selected folder and its associated data from both the GoJS palette and the main diagram.\n   *\n   * This method removes the folder from the palette and main diagram, and deletes the folder data from the backend.\n   *\n   * @param folderData - The folder node data to be deleted.\n   * @param goJsPaletteDiagram - The GoJS palette diagram from which the folder will be removed.\n   * @param goDiagram - The main GoJS diagram from which the folder and its children will be removed.\n   */\n  deleteSelectedFolder(nodeData) {\n    this.folderService.deleteFolders(nodeData.map(folder => folder['data']?.idFolder));\n    nodeData.forEach(folderData => {\n      if (folderData.data) {\n        this.removeDiagrams(folderData);\n        const children = this.treeNodeService.getClassesEnumsFromFolder(folderData);\n        const links = new Set();\n        children.forEach(childNode => {\n          if (childNode.data) {\n            const diagramChildNodes = this._gojsDiagram.model.nodeDataArray.filter(node => {\n              if (this.gojsCommonService.isGojsDiagramClassNode(childNode.data)) {\n                return node['category'] == childNode['category'] && node['idTemplateClass'] == childNode.data.idTemplateClass;\n              } else if (this.gojsCommonService.isGojsDiagramEnumerationNode(childNode.data)) {\n                return node['category'] == childNode['category'] && node['idTemplateEnumeration'] == childNode.data.idTemplateEnumeration;\n              } else {\n                return;\n              }\n            });\n            if (this.gojsCommonService.isGojsDiagramEnumerationNode(childNode.data)) this.diagramUtils.removeAttributeType(childNode.data.idTemplateEnumeration.toString());\n            this._gojsDiagram.model.removeNodeDataCollection(diagramChildNodes);\n          }\n          if (this.gojsCommonService.isGojsDiagramClassNode(childNode.data)) this._gojsDiagram.model.linkDataArray.forEach(linkData => {\n            if (linkData['idSourceTempClass'] == childNode.data.idTemplateClass || linkData['idDestinationTempClass'] == childNode.data.idTemplateClass) {\n              links.add(linkData);\n            }\n          });\n        });\n        links.forEach(linkData => {\n          this.gojsCardinalityService.removeLinkFromCurrentDiagram(linkData, this._gojsDiagram, true);\n        });\n        if (this.diagrams.length > 0) this.snackBarService.openSnackbar('snackBar.deleteFolderMsg');\n      }\n    });\n  }\n  removeDiagrams(node) {\n    // Helper function to collect and remove diagrams recursively\n    const collectAndRemoveDiagrams = node => {\n      // Check for diagram wrappers in the current node\n      const diagramWrapper = node.children.find(child => child.category === DiagramWrapperCategory);\n      // If a diagram wrapper exists, remove its diagrams from `this.diagrams`\n      if (diagramWrapper && Array.isArray(diagramWrapper.children)) {\n        const diagramsToRemove = diagramWrapper.children.map(child => child.data);\n        // Remove diagrams from `this.diagrams` during collection\n        this.diagrams = this.diagrams.filter(diagram => !diagramsToRemove.some(diagramToRemove => diagramToRemove.id === diagram.id));\n      }\n      // Recursively process folder nodes\n      for (const child of node.children) {\n        if (child.category === GojsNodeCategory.Folder) {\n          collectAndRemoveDiagrams(child);\n        }\n      }\n    };\n    // Start the collection and removal process\n    collectAndRemoveDiagrams(node);\n    this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n    debugger;\n    this.diagramUtils.setActiveDiagram(this.diagrams.length > 0 ? this.diagrams[0] : null);\n  }\n  /**\n   * Updates the folder node in the GoJS palette diagram with the new data.\n   * @param {GojsFolderNode} updatedNode The node to update\n   * @param {go.Palette} palette The GoJS palette diagram containing the node\n   * @memberOf GojsFolderService\n   */\n  updateFolderNode(updatedNode, treeNode) {\n    this.treeNodeService.editGroupTreeNode({\n      ...treeNode,\n      name: updatedNode.name,\n      data: updatedNode\n    });\n  }\n  handleFolderEdit(folderNode, treeNode) {\n    this.folderService.updateFolder({\n      id: folderNode.idFolder,\n      name: treeNode.name,\n      icon: folderNode.icon\n    }).subscribe(() => {\n      this.updateFolderNode({\n        ...folderNode,\n        name: treeNode.name\n      }, {\n        ...treeNode,\n        data: {\n          ...treeNode.data,\n          name: treeNode.name\n        }\n      });\n      this.propertyService.setPropertyData(folderNode);\n    });\n  }\n  static #_ = this.ɵfac = function GojsFolderService_Factory(t) {\n    return new (t || GojsFolderService)(i0.ɵɵinject(i1.FolderService), i0.ɵɵinject(i2.SnackBarService), i0.ɵɵinject(i3.TreeNodeService), i0.ɵɵinject(i4.GojsCommonService), i0.ɵɵinject(i5.GojsCardinalityService), i0.ɵɵinject(i6.DiagramUtils), i0.ɵɵinject(i7.PropertyService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsFolderService,\n    factory: GojsFolderService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["GoJsNodeIcon", "GojsNodeCategory", "TreeNodeTag", "DiagramWrapperCategory", "GojsFolderService", "constructor", "folderService", "snackBarService", "treeNodeService", "gojsCommonService", "gojsCardinalityService", "diagramUtils", "propertyService", "diagrams", "gojsDiagramChanges", "subscribe", "diagram", "_gojsDiagram", "currentProjectDiagramsChanges", "onCreateNewFolder", "name", "idProject", "hasEditAccess", "parentTag", "createNewFolder", "icon", "Folder", "response", "newNodeData", "allowTopLevelDrops", "editable", "isGroup", "isFolder", "category", "showTablePanel", "idFolder", "id", "key", "addGroupNodeInTree", "tag", "children", "data", "Project", "isDraggable", "supportingNodes", "Class", "AssociativeClass", "Enumeration", "Diagram", "deleteSelectedFolder", "nodeData", "deleteFolders", "map", "folder", "for<PERSON>ach", "folderData", "removeDiagrams", "getClassesEnumsFromFolder", "links", "Set", "childNode", "diagramChildNodes", "model", "nodeDataArray", "filter", "node", "isGojsDiagramClassNode", "idTemplateClass", "isGojsDiagramEnumerationNode", "idTemplateEnumeration", "removeAttributeType", "toString", "removeNodeDataCollection", "linkDataArray", "linkData", "add", "removeLinkFromCurrentDiagram", "length", "openSnackbar", "collectAndRemoveDiagrams", "diagramWrapper", "find", "child", "Array", "isArray", "diagramsToRemove", "some", "diagramToRemove", "setCurrentProjectDiagrams", "setActiveDiagram", "updateFolderNode", "updatedNode", "treeNode", "editGroupTreeNode", "handleFolderEdit", "folderNode", "updateFolder", "setPropertyData", "_", "i0", "ɵɵinject", "i1", "FolderService", "i2", "SnackBarService", "i3", "TreeNodeService", "i4", "GojsCommonService", "i5", "GojsCardinalityService", "i6", "DiagramUtils", "i7", "PropertyService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojsFolder\\gojs-folder.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { ProjectFolder } from 'src/app/shared/model/class';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport {\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport { DiagramWrapperCategory } from 'src/app/shared/utils/constants';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { FolderService } from '../../folder/folder.service';\r\nimport { PropertyService } from '../../property/property.service';\r\nimport { SnackBarService } from '../../snackbar/snack-bar.service';\r\nimport { TreeNodeService } from '../../treeNode/tree-node.service';\r\nimport { GojsCardinalityService } from '../gojsCardinality/gojs-cardinality.service';\r\nimport { GojsCommonService } from '../gojsCommon/gojs-common.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsFolderService {\r\n  private _gojsDiagram!: go.Diagram;\r\n  private diagrams: Diagram[] = [];\r\n  constructor(\r\n    private folderService: FolderService,\r\n    private snackBarService: SnackBarService,\r\n    private treeNodeService: TreeNodeService,\r\n    private gojsCommonService: GojsCommonService,\r\n    private gojsCardinalityService: GojsCardinalityService,\r\n    private diagramUtils: DiagramUtils,\r\n    private propertyService: PropertyService\r\n  ) {\r\n    this.gojsCommonService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this._gojsDiagram = diagram;\r\n    });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      this.diagrams = diagrams;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles the creation of a new folder in the GoJS palette diagram.\r\n   *\r\n   * This method adds a new folder node to the GoJS palette diagram and makes a service call to create the folder in the backend.\r\n   * After the folder is created successfully, it updates the node with the new folder's ID and selects the node in the palette.\r\n   *\r\n   * @param idProject - The ID of the project to which the new folder will belong.\r\n   * @param goJsPaletteDiagram - The GoJS palette diagram where the new folder will be added.\r\n   * @param hasEditAccess - A boolean indicating whether the user has edit access to the folder.\r\n   */\r\n  onCreateNewFolder(\r\n    name: string,\r\n    idProject: number,\r\n    hasEditAccess: boolean,\r\n    parentTag?: string\r\n  ) {\r\n    this.folderService\r\n      .createNewFolder({\r\n        name: name,\r\n        icon: GoJsNodeIcon.Folder,\r\n        idProject: idProject,\r\n      } as ProjectFolder)\r\n      .subscribe((response) => {\r\n        const newNodeData: GojsFolderNode = {\r\n          name: response.name,\r\n          icon: GoJsNodeIcon.Folder,\r\n          allowTopLevelDrops: true,\r\n          editable: hasEditAccess,\r\n          // isPalette: true,\r\n          isGroup: false,\r\n          isFolder: true,\r\n          category: GojsNodeCategory.Folder,\r\n          showTablePanel: false,\r\n          idFolder: response.id!,\r\n          key: 0,\r\n        };\r\n        this.treeNodeService.addGroupNodeInTree({\r\n          name: response.name,\r\n          category: GojsNodeCategory.Folder,\r\n          tag: `atTag${GojsNodeCategory.Folder}_${response.id}`,\r\n          children: [],\r\n          icon: GoJsNodeIcon.Folder,\r\n          data: newNodeData,\r\n          parentTag: parentTag ?? TreeNodeTag.Project,\r\n          isDraggable: true,\r\n          supportingNodes: [\r\n            GojsNodeCategory.Class,\r\n            GojsNodeCategory.AssociativeClass,\r\n            GojsNodeCategory.Enumeration,\r\n            GojsNodeCategory.Folder,\r\n            GojsNodeCategory.Diagram,\r\n          ],\r\n        });\r\n      });\r\n  }\r\n\r\n  /**\r\n   * Deletes a selected folder and its associated data from both the GoJS palette and the main diagram.\r\n   *\r\n   * This method removes the folder from the palette and main diagram, and deletes the folder data from the backend.\r\n   *\r\n   * @param folderData - The folder node data to be deleted.\r\n   * @param goJsPaletteDiagram - The GoJS palette diagram from which the folder will be removed.\r\n   * @param goDiagram - The main GoJS diagram from which the folder and its children will be removed.\r\n   */\r\n  deleteSelectedFolder(nodeData: TreeNode[]): void {\r\n    this.folderService.deleteFolders(\r\n      nodeData.map((folder) => (folder['data'] as GojsFolderNode)?.idFolder)\r\n    );\r\n    nodeData.forEach((folderData) => {\r\n      if (folderData.data) {\r\n        this.removeDiagrams(folderData);\r\n        const children =\r\n          this.treeNodeService.getClassesEnumsFromFolder(folderData);\r\n        const links = new Set<go.ObjectData>();\r\n        children.forEach((childNode) => {\r\n          if (childNode.data) {\r\n            const diagramChildNodes =\r\n              this._gojsDiagram.model.nodeDataArray.filter((node) => {\r\n                if (\r\n                  this.gojsCommonService.isGojsDiagramClassNode(childNode.data)\r\n                ) {\r\n                  return (\r\n                    node['category'] == childNode['category'] &&\r\n                    node['idTemplateClass'] ==\r\n                      (childNode.data as GojsDiagramClassNode).idTemplateClass\r\n                  );\r\n                } else if (\r\n                  this.gojsCommonService.isGojsDiagramEnumerationNode(\r\n                    childNode.data\r\n                  )\r\n                ) {\r\n                  return (\r\n                    node['category'] == childNode['category'] &&\r\n                    node['idTemplateEnumeration'] ==\r\n                      (childNode.data as GojsDiagramEnumerationNode)\r\n                        .idTemplateEnumeration\r\n                  );\r\n                } else {\r\n                  return;\r\n                }\r\n              });\r\n            if (\r\n              this.gojsCommonService.isGojsDiagramEnumerationNode(\r\n                childNode.data\r\n              )\r\n            )\r\n              this.diagramUtils.removeAttributeType(\r\n                (\r\n                  childNode.data as GojsDiagramEnumerationNode\r\n                ).idTemplateEnumeration.toString()\r\n              );\r\n            this._gojsDiagram.model.removeNodeDataCollection(diagramChildNodes);\r\n          }\r\n          if (this.gojsCommonService.isGojsDiagramClassNode(childNode.data))\r\n            (\r\n              this._gojsDiagram.model as go.GraphLinksModel\r\n            ).linkDataArray.forEach((linkData) => {\r\n              if (\r\n                linkData['idSourceTempClass'] ==\r\n                  (childNode.data as GojsDiagramClassNode).idTemplateClass ||\r\n                linkData['idDestinationTempClass'] ==\r\n                  (childNode.data as GojsDiagramClassNode).idTemplateClass\r\n              ) {\r\n                links.add(linkData);\r\n              }\r\n            });\r\n        });\r\n        links.forEach((linkData) => {\r\n          this.gojsCardinalityService.removeLinkFromCurrentDiagram(\r\n            linkData as GojsLinkNode,\r\n            this._gojsDiagram,\r\n            true\r\n          );\r\n        });\r\n        if (this.diagrams.length > 0)\r\n          this.snackBarService.openSnackbar('snackBar.deleteFolderMsg');\r\n      }\r\n    });\r\n  }\r\n\r\n  private removeDiagrams(node: TreeNode): void {\r\n    // Helper function to collect and remove diagrams recursively\r\n    const collectAndRemoveDiagrams = (node: TreeNode): void => {\r\n      // Check for diagram wrappers in the current node\r\n      const diagramWrapper = node.children.find(\r\n        (child) => child.category === DiagramWrapperCategory\r\n      );\r\n      // If a diagram wrapper exists, remove its diagrams from `this.diagrams`\r\n      if (diagramWrapper && Array.isArray(diagramWrapper.children)) {\r\n        const diagramsToRemove = diagramWrapper.children.map(\r\n          (child: TreeNode) => child.data\r\n        ) as Diagram[];\r\n\r\n        // Remove diagrams from `this.diagrams` during collection\r\n        this.diagrams = this.diagrams.filter(\r\n          (diagram) =>\r\n            !diagramsToRemove.some(\r\n              (diagramToRemove) => diagramToRemove.id === diagram.id\r\n            )\r\n        );\r\n      }\r\n      // Recursively process folder nodes\r\n      for (const child of node.children) {\r\n        if (child.category === GojsNodeCategory.Folder) {\r\n          collectAndRemoveDiagrams(child);\r\n        }\r\n      }\r\n    };\r\n\r\n    // Start the collection and removal process\r\n    collectAndRemoveDiagrams(node);\r\n    this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n    debugger;\r\n    this.diagramUtils.setActiveDiagram(\r\n      this.diagrams.length > 0 ? this.diagrams[0] : null\r\n    );\r\n  }\r\n  /**\r\n   * Updates the folder node in the GoJS palette diagram with the new data.\r\n   * @param {GojsFolderNode} updatedNode The node to update\r\n   * @param {go.Palette} palette The GoJS palette diagram containing the node\r\n   * @memberOf GojsFolderService\r\n   */\r\n  updateFolderNode(updatedNode: GojsFolderNode, treeNode: TreeNode) {\r\n    this.treeNodeService.editGroupTreeNode({\r\n      ...treeNode,\r\n      name: updatedNode.name,\r\n      data: updatedNode,\r\n    });\r\n  }\r\n  handleFolderEdit(folderNode: GojsFolderNode, treeNode: TreeNode) {\r\n    this.folderService\r\n      .updateFolder({\r\n        id: folderNode.idFolder,\r\n        name: treeNode.name,\r\n        icon: folderNode.icon,\r\n      })\r\n      .subscribe(() => {\r\n        this.updateFolderNode(\r\n          { ...folderNode, name: treeNode.name },\r\n          {\r\n            ...treeNode,\r\n            data: {\r\n              ...treeNode.data,\r\n              name: treeNode.name,\r\n            } as GojsFolderNode,\r\n          }\r\n        );\r\n        this.propertyService.setPropertyData(folderNode);\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,6BAA6B;AAE1D,SAKEC,gBAAgB,QACX,2BAA2B;AAClC,SAAmBC,WAAW,QAAQ,+BAA+B;AACrE,SAASC,sBAAsB,QAAQ,gCAAgC;;;;;;;;;AAWvE,OAAM,MAAOC,iBAAiB;EAG5BC,YACUC,aAA4B,EAC5BC,eAAgC,EAChCC,eAAgC,EAChCC,iBAAoC,EACpCC,sBAA8C,EAC9CC,YAA0B,EAC1BC,eAAgC;IANhC,KAAAN,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IARjB,KAAAC,QAAQ,GAAc,EAAE;IAU9B,IAAI,CAACJ,iBAAiB,CAACK,kBAAkB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACC,YAAY,GAAGD,OAAO;IAC1C,CAAC,CAAC;IACF,IAAI,CAACL,YAAY,CAACO,6BAA6B,EAAE,CAACH,SAAS,CAAEF,QAAQ,IAAI;MACvE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,CAAC;EACJ;EAEA;;;;;;;;;;EAUAM,iBAAiBA,CACfC,IAAY,EACZC,SAAiB,EACjBC,aAAsB,EACtBC,SAAkB;IAElB,IAAI,CAACjB,aAAa,CACfkB,eAAe,CAAC;MACfJ,IAAI,EAAEA,IAAI;MACVK,IAAI,EAAEzB,YAAY,CAAC0B,MAAM;MACzBL,SAAS,EAAEA;KACK,CAAC,CAClBN,SAAS,CAAEY,QAAQ,IAAI;MACtB,MAAMC,WAAW,GAAmB;QAClCR,IAAI,EAAEO,QAAQ,CAACP,IAAI;QACnBK,IAAI,EAAEzB,YAAY,CAAC0B,MAAM;QACzBG,kBAAkB,EAAE,IAAI;QACxBC,QAAQ,EAAER,aAAa;QACvB;QACAS,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAEhC,gBAAgB,CAACyB,MAAM;QACjCQ,cAAc,EAAE,KAAK;QACrBC,QAAQ,EAAER,QAAQ,CAACS,EAAG;QACtBC,GAAG,EAAE;OACN;MACD,IAAI,CAAC7B,eAAe,CAAC8B,kBAAkB,CAAC;QACtClB,IAAI,EAAEO,QAAQ,CAACP,IAAI;QACnBa,QAAQ,EAAEhC,gBAAgB,CAACyB,MAAM;QACjCa,GAAG,EAAE,QAAQtC,gBAAgB,CAACyB,MAAM,IAAIC,QAAQ,CAACS,EAAE,EAAE;QACrDI,QAAQ,EAAE,EAAE;QACZf,IAAI,EAAEzB,YAAY,CAAC0B,MAAM;QACzBe,IAAI,EAAEb,WAAW;QACjBL,SAAS,EAAEA,SAAS,IAAIrB,WAAW,CAACwC,OAAO;QAC3CC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,CACf3C,gBAAgB,CAAC4C,KAAK,EACtB5C,gBAAgB,CAAC6C,gBAAgB,EACjC7C,gBAAgB,CAAC8C,WAAW,EAC5B9C,gBAAgB,CAACyB,MAAM,EACvBzB,gBAAgB,CAAC+C,OAAO;OAE3B,CAAC;IACJ,CAAC,CAAC;EACN;EAEA;;;;;;;;;EASAC,oBAAoBA,CAACC,QAAoB;IACvC,IAAI,CAAC5C,aAAa,CAAC6C,aAAa,CAC9BD,QAAQ,CAACE,GAAG,CAAEC,MAAM,IAAMA,MAAM,CAAC,MAAM,CAAoB,EAAElB,QAAQ,CAAC,CACvE;IACDe,QAAQ,CAACI,OAAO,CAAEC,UAAU,IAAI;MAC9B,IAAIA,UAAU,CAACd,IAAI,EAAE;QACnB,IAAI,CAACe,cAAc,CAACD,UAAU,CAAC;QAC/B,MAAMf,QAAQ,GACZ,IAAI,CAAChC,eAAe,CAACiD,yBAAyB,CAACF,UAAU,CAAC;QAC5D,MAAMG,KAAK,GAAG,IAAIC,GAAG,EAAiB;QACtCnB,QAAQ,CAACc,OAAO,CAAEM,SAAS,IAAI;UAC7B,IAAIA,SAAS,CAACnB,IAAI,EAAE;YAClB,MAAMoB,iBAAiB,GACrB,IAAI,CAAC5C,YAAY,CAAC6C,KAAK,CAACC,aAAa,CAACC,MAAM,CAAEC,IAAI,IAAI;cACpD,IACE,IAAI,CAACxD,iBAAiB,CAACyD,sBAAsB,CAACN,SAAS,CAACnB,IAAI,CAAC,EAC7D;gBACA,OACEwB,IAAI,CAAC,UAAU,CAAC,IAAIL,SAAS,CAAC,UAAU,CAAC,IACzCK,IAAI,CAAC,iBAAiB,CAAC,IACpBL,SAAS,CAACnB,IAA6B,CAAC0B,eAAe;eAE7D,MAAM,IACL,IAAI,CAAC1D,iBAAiB,CAAC2D,4BAA4B,CACjDR,SAAS,CAACnB,IAAI,CACf,EACD;gBACA,OACEwB,IAAI,CAAC,UAAU,CAAC,IAAIL,SAAS,CAAC,UAAU,CAAC,IACzCK,IAAI,CAAC,uBAAuB,CAAC,IAC1BL,SAAS,CAACnB,IAAmC,CAC3C4B,qBAAqB;eAE7B,MAAM;gBACL;;YAEJ,CAAC,CAAC;YACJ,IACE,IAAI,CAAC5D,iBAAiB,CAAC2D,4BAA4B,CACjDR,SAAS,CAACnB,IAAI,CACf,EAED,IAAI,CAAC9B,YAAY,CAAC2D,mBAAmB,CAEjCV,SAAS,CAACnB,IACX,CAAC4B,qBAAqB,CAACE,QAAQ,EAAE,CACnC;YACH,IAAI,CAACtD,YAAY,CAAC6C,KAAK,CAACU,wBAAwB,CAACX,iBAAiB,CAAC;;UAErE,IAAI,IAAI,CAACpD,iBAAiB,CAACyD,sBAAsB,CAACN,SAAS,CAACnB,IAAI,CAAC,EAE7D,IAAI,CAACxB,YAAY,CAAC6C,KACnB,CAACW,aAAa,CAACnB,OAAO,CAAEoB,QAAQ,IAAI;YACnC,IACEA,QAAQ,CAAC,mBAAmB,CAAC,IAC1Bd,SAAS,CAACnB,IAA6B,CAAC0B,eAAe,IAC1DO,QAAQ,CAAC,wBAAwB,CAAC,IAC/Bd,SAAS,CAACnB,IAA6B,CAAC0B,eAAe,EAC1D;cACAT,KAAK,CAACiB,GAAG,CAACD,QAAQ,CAAC;;UAEvB,CAAC,CAAC;QACN,CAAC,CAAC;QACFhB,KAAK,CAACJ,OAAO,CAAEoB,QAAQ,IAAI;UACzB,IAAI,CAAChE,sBAAsB,CAACkE,4BAA4B,CACtDF,QAAwB,EACxB,IAAI,CAACzD,YAAY,EACjB,IAAI,CACL;QACH,CAAC,CAAC;QACF,IAAI,IAAI,CAACJ,QAAQ,CAACgE,MAAM,GAAG,CAAC,EAC1B,IAAI,CAACtE,eAAe,CAACuE,YAAY,CAAC,0BAA0B,CAAC;;IAEnE,CAAC,CAAC;EACJ;EAEQtB,cAAcA,CAACS,IAAc;IACnC;IACA,MAAMc,wBAAwB,GAAId,IAAc,IAAU;MACxD;MACA,MAAMe,cAAc,GAAGf,IAAI,CAACzB,QAAQ,CAACyC,IAAI,CACtCC,KAAK,IAAKA,KAAK,CAACjD,QAAQ,KAAK9B,sBAAsB,CACrD;MACD;MACA,IAAI6E,cAAc,IAAIG,KAAK,CAACC,OAAO,CAACJ,cAAc,CAACxC,QAAQ,CAAC,EAAE;QAC5D,MAAM6C,gBAAgB,GAAGL,cAAc,CAACxC,QAAQ,CAACY,GAAG,CACjD8B,KAAe,IAAKA,KAAK,CAACzC,IAAI,CACnB;QAEd;QACA,IAAI,CAAC5B,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACmD,MAAM,CACjChD,OAAO,IACN,CAACqE,gBAAgB,CAACC,IAAI,CACnBC,eAAe,IAAKA,eAAe,CAACnD,EAAE,KAAKpB,OAAO,CAACoB,EAAE,CACvD,CACJ;;MAEH;MACA,KAAK,MAAM8C,KAAK,IAAIjB,IAAI,CAACzB,QAAQ,EAAE;QACjC,IAAI0C,KAAK,CAACjD,QAAQ,KAAKhC,gBAAgB,CAACyB,MAAM,EAAE;UAC9CqD,wBAAwB,CAACG,KAAK,CAAC;;;IAGrC,CAAC;IAED;IACAH,wBAAwB,CAACd,IAAI,CAAC;IAC9B,IAAI,CAACtD,YAAY,CAAC6E,yBAAyB,CAAC,IAAI,CAAC3E,QAAQ,CAAC;IAC1D;IACA,IAAI,CAACF,YAAY,CAAC8E,gBAAgB,CAChC,IAAI,CAAC5E,QAAQ,CAACgE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAChE,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CACnD;EACH;EACA;;;;;;EAMA6E,gBAAgBA,CAACC,WAA2B,EAAEC,QAAkB;IAC9D,IAAI,CAACpF,eAAe,CAACqF,iBAAiB,CAAC;MACrC,GAAGD,QAAQ;MACXxE,IAAI,EAAEuE,WAAW,CAACvE,IAAI;MACtBqB,IAAI,EAAEkD;KACP,CAAC;EACJ;EACAG,gBAAgBA,CAACC,UAA0B,EAAEH,QAAkB;IAC7D,IAAI,CAACtF,aAAa,CACf0F,YAAY,CAAC;MACZ5D,EAAE,EAAE2D,UAAU,CAAC5D,QAAQ;MACvBf,IAAI,EAAEwE,QAAQ,CAACxE,IAAI;MACnBK,IAAI,EAAEsE,UAAU,CAACtE;KAClB,CAAC,CACDV,SAAS,CAAC,MAAK;MACd,IAAI,CAAC2E,gBAAgB,CACnB;QAAE,GAAGK,UAAU;QAAE3E,IAAI,EAAEwE,QAAQ,CAACxE;MAAI,CAAE,EACtC;QACE,GAAGwE,QAAQ;QACXnD,IAAI,EAAE;UACJ,GAAGmD,QAAQ,CAACnD,IAAI;UAChBrB,IAAI,EAAEwE,QAAQ,CAACxE;;OAElB,CACF;MACD,IAAI,CAACR,eAAe,CAACqF,eAAe,CAACF,UAAU,CAAC;IAClD,CAAC,CAAC;EACN;EAAC,QAAAG,CAAA,G;qBAvOU9F,iBAAiB,EAAA+F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,iBAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,sBAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAjB/G,iBAAiB;IAAAgH,OAAA,EAAjBhH,iBAAiB,CAAAiH,IAAA;IAAAC,UAAA,EAFhB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}