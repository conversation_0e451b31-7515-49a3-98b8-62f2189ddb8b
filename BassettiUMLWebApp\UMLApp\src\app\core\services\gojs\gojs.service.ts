import { Injectable } from '@angular/core';
import * as go from 'gojs';
import { LinkLabelDraggingTool } from 'src/app/extensions/LinkLabelDraggingTool';
import { linkPortList } from 'src/app/shared/configs/linkPortConfig';
import { paletteConfigs } from 'src/app/shared/configs/palletteConfigs';
import { AttributeOption, AttributeType } from 'src/app/shared/model/attribute';
import { GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramCommentNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType } from 'src/app/shared/model/project';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AccessService } from '../access/access.service';
import { AppService } from '../app.service';
import { EventListenerService } from '../diagram/event-listener.service';
import { PropertyService } from '../property/property.service';
import { SnackBarService } from '../snackbar/snack-bar.service';
import { TreeNodeService } from '../treeNode/tree-node.service';
import { GojsCommentService } from './gojs-comment/gojs-comment.service';
import { GojsAttributeService } from './gojsAttribute/gojs-attribute.service';
import { GojsCardinalityService } from './gojsCardinality/gojs-cardinality.service';
import { GojsClassService } from './gojsClass/gojs-class.service';
import { GojsCommonService } from './gojsCommon/gojs-common.service';
import { GojsEnumerationService } from './gojsEnumeration/gojs-enumeration.service';
import { GojsFolderService } from './gojsFolder/gojs-folder.service';
import { GojsLiteralService } from './gojsLiteral/gojs-literal.service';
@Injectable({
  providedIn: 'root',
})
export class GojsService {
  private _gojsDiagram!: go.Diagram;
  private $ = go.GraphObject.make;
  private _hasEditAccessOnly: boolean = false;
  private _attributeTypes: AttributeOption[] = [];
  private _diagrams: Diagram[] = [];
  constructor(
    private accessService: AccessService,
    private diagramUtils: DiagramUtils,
    private propertyService: PropertyService,
    private eventListenerService: EventListenerService,
    private goJsClassService: GojsClassService,
    private goJsEnumService: GojsEnumerationService,
    private goJsLiteralService: GojsLiteralService,
    private goJsFolderService: GojsFolderService,
    private goJsAttributeService: GojsAttributeService,
    private goJsCardinalityService: GojsCardinalityService,
    private goJsCommonService: GojsCommonService,
    private gojsCommentService: GojsCommentService,
    private treeNodeService: TreeNodeService,
    private snackBarService: SnackBarService,
    private appService: AppService
  ) {
    this.accessService.accessTypeChanges().subscribe((access) => {
      if (access != AccessType.Viewer) {
        this._hasEditAccessOnly = true;
      } else {
        this._hasEditAccessOnly = false;
      }
    });
    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this._gojsDiagram = diagram;
    });
    this.diagramUtils.getAttributeTypes().subscribe((options) => {
      this._attributeTypes = options.sort((a, b) =>
        a.name.localeCompare(b.name, undefined, { sensitivity: 'base' })
      );
      if (this._gojsDiagram && this._gojsDiagram.model) {
        this._gojsDiagram.model.commit((model) => {
          model.set(model.modelData, 'attributeTypes', options);
        }, null);
      }
    });
    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {
      this._diagrams = diagrams;
      if (this._gojsDiagram && this._diagrams.length == 0) {
        this._gojsDiagram.clear();
        this.snackBarService.warn('snackBar.diagramDeleteInfo');
      }
      if (this._gojsDiagram) {
        this._gojsDiagram.allowDrop =
          this._hasEditAccessOnly && this._diagrams.length > 0;
      }
    });
  }

  /**
   *Load the go js component  with the selected diagram details and set it to the layout container.
   * @private
   * @memberof DiagramEditorComponent
   */
  initDiagram(diagram: go.Diagram): void {
    this.goJsCommonService.setGojsDiagram(diagram);
    this._gojsDiagram.isReadOnly = !this._hasEditAccessOnly;
    const toolManager = this._gojsDiagram.toolManager;
    if (this._hasEditAccessOnly) {
      toolManager.mouseMoveTools.insertAt(0, new LinkLabelDraggingTool());
    } else {
      const firstTool = toolManager.mouseMoveTools.elt(0);
      if (firstTool instanceof LinkLabelDraggingTool) {
        toolManager.mouseMoveTools.removeAt(0);
      }
    }
    if (this.appService.isInitProject()) {
      this.initializeMainDiagram();
      this.eventListenerService.addDiagramEventListeners();

      diagram.div?.addEventListener('dragover', (event) => {
        event.preventDefault();
      });
      diagram.div?.addEventListener('drop', (event) => {
        this.onDropNode(event);
      });
    }
  }

  onDropNode(event: DragEvent) {
    event.preventDefault();
    const data = event.dataTransfer?.getData('text/plain');
    if (data) {
      const nodeData = JSON.parse(data);
      if (nodeData.category === GojsNodeCategory.Diagram) {
        event.stopPropagation();
        return;
      }
      // Get the drop position in screen coordinates (clientX, clientY)
      const clientX = event.clientX - 300;
      const clientY = event.clientY - 50;

      // Convert the screen coordinates to diagram coordinates
      const dropPoint = this._gojsDiagram.transformViewToDoc(
        new go.Point(clientX, clientY)
      );
      if (
        nodeData.category === GojsNodeCategory.Class ||
        nodeData.category === GojsNodeCategory.AssociativeClass ||
        nodeData.category === GojsNodeCategory.Enumeration
      ) {
        if (
          this.goJsCommonService.checkGroupNodeExist(
            this._gojsDiagram,
            nodeData
          )
        )
          return;

        delete nodeData.data.key;
        delete nodeData.data.id;
        this.handleGroupNodeCreationOrUpdate(
          {
            ...nodeData.data,
            position: go.Point.stringify(dropPoint),
          },
          true
        );
      } else if (nodeData.category === GojsNodeCategory.Folder) {
        const spacing = 170;
        const children =
          this.treeNodeService.getClassesEnumsFromFolder(nodeData);
        children.forEach((child, index) => {
          const newPoint = new go.Point(
            dropPoint.x + index * spacing,
            dropPoint.y
          );
          this.handleGroupNodeCreationOrUpdate(
            {
              ...child.data,
              position: go.Point.stringify(newPoint),
            },
            true
          );
        });
      }
    }
  }

  initPaletteDiagram(): void {
    const componentNodeTemplate = this.getComponentNodeTemplate();
    const componentGroupTemplate = this.getComponentGroupTemplate();
    this.configureComponentPalette(
      componentNodeTemplate,
      componentGroupTemplate
    );
  }

  /**
   * Initializes the main diagram with configuration settings and tools.
   * This method sets up the diagram's properties, tools, templates, and event listeners
   * templates, and event listeners.
   * @memberof DiagramEditorComponent
   */
  initializeMainDiagram(): void {
    this.setupDiagramTools();
    this.initializeItemTemplate();
    this.configureGroupTemplate();
    this.configureLinkTemplate();
    this.configureGroupTemplateAdornment();
    this.setupDiagramModel();
  }

  /**
   * Sets up tools and context menus for the diagram.
   * @private
   * @memberof DiagramEditorComponent
   */
  private setupDiagramTools(): void {
    if (
      this._gojsDiagram &&
      this._gojsDiagram.toolManager &&
      this._gojsDiagram.toolManager.panningTool
    ) {
      this._gojsDiagram.toolManager.panningTool.isEnabled =
        this._hasEditAccessOnly;
    }
  }

  /**
   * Initializes and returns the item template for the diagram.
   * This template includes text blocks for name, category, and type with appropriate bindings and styles.
   *
   * @private
   * @returns {go.Panel} The configured item template panel.
   * @memberof DiagramEditorComponent
   */
  private initializeItemTemplate(): go.Panel {
    return this.$(
      go.Panel,
      'Horizontal',
      {
        alignment: go.Spot.TopLeft,
        stretch: go.Stretch.Fill,
      },
      this.createNameTextBlock(),
      this.createCategoryTextBlock(),
      this.createTypeTextBlock()
    );
  }

  /**
   * Creates and returns a text block for the name with bindings and styles.
   *
   * @private
   * @returns {go.TextBlock} The configured name text block.
   * @memberof DiagramEditorComponent
   */
  private createNameTextBlock(): go.TextBlock {
    return this.$(
      go.TextBlock,
      {
        isMultiline: false,
        alignment: go.Spot.Center,
        minSize: new go.Size(50, NaN),
        margin: new go.Margin(3, 10, 0, 10),
        overflow: go.TextOverflow.Ellipsis,
        wrap: go.Wrap.Fit,
        textEdited: (
          textBlock: go.TextBlock,
          oldText: string,
          newText: string
        ) => {
          if (!newText.trim()) {
            // If the new text is empty or contains only spaces, restore the old value
            textBlock.text = oldText;
          }
        },
      },
      new go.Binding('text', 'name').makeTwoWay(),
      new go.Binding('editable', 'editable').makeTwoWay()
    );
  }

  /**
   * Creates and returns a text block for the category with bindings and styles.
   * @private
   * @returns {go.TextBlock} The configured category text block.
   * @memberof DiagramEditorComponent
   */
  private createCategoryTextBlock(): go.TextBlock {
    return this.$(
      go.TextBlock,
      ':',
      { stroke: 'black' },
      new go.Binding('visible', 'category', (category) => {
        return (
          category === GojsNodeCategory.Attribute ||
          category === GojsNodeCategory.Operation
        );
      })
    );
  }

  /**
   * Creates and returns a text block for the type with bindings, styles, and choices.
   *
   * @private
   * @returns {go.TextBlock} The configured type text block.
   * @memberof DiagramEditorComponent
   */
  private createTypeTextBlock(): go.TextBlock {
    return this.$(
      go.TextBlock,
      {
        isMultiline: false,
        alignment: go.Spot.Center,
        margin: new go.Margin(3, 10, 0, 10),
        textEditor: window.TextEditorSelectBox,
      },
      new go.Binding('choices', '', () => {
        return this._attributeTypes.sort();
      }).makeTwoWay(),
      new go.Binding('text', 'dataType', (dataType) => {
        const attributeOption = this._attributeTypes.find(
          (option: AttributeOption) =>
            option.id == dataType || option.name == dataType
        );
        return attributeOption
          ? attributeOption.name
          : `${AttributeType[AttributeType.Undefined]}`;
      }).makeTwoWay(),
      new go.Binding('name', 'dataType', (dataType) => {
        const attributeOption = this._attributeTypes.find(
          (option: AttributeOption) =>
            option.id == dataType || option.name == dataType
        );
        return attributeOption
          ? attributeOption.id
          : `0_${AttributeType[AttributeType.Undefined]}`;
      }).makeTwoWay(),
      new go.Binding('editable', 'editable').makeTwoWay(),
      new go.Binding('visible', 'category', (category) => {
        return (
          category === GojsNodeCategory.Attribute ||
          category === GojsNodeCategory.Operation
        );
      })
    );
  }

  /**
   * Configures the group template for the diagram with specific behaviors and styles.
   * This method orchestrates the creation of group templates and applies them to the diagram.
   *
   * @private
   * @memberof DiagramEditorComponent
   */
  private configureGroupTemplate(): void {
    this._gojsDiagram.groupTemplate = this.createGroupTemplate();
    this._gojsDiagram.groupTemplateMap.add(
      GojsNodeCategory.Package,
      this.createPackageGroupTemplate()
    );
    this._gojsDiagram.groupTemplateMap.add(
      GojsNodeCategory.Enumeration,
      this.createEnumerationGroupTemplate()
    );
    this._gojsDiagram.groupTemplateMap.add(
      GojsNodeCategory.Comment,
      this.createCommentGroupTemplate()
    );
    this._gojsDiagram.groupTemplateMap.add(
      GojsNodeCategory.AssociativeClass,
      this.createAssociativeClassGroupTemplate()
    );
    this._gojsDiagram.nodeTemplateMap.add(
      GojsNodeCategory.LinkLabel,
      this.createLinkLabelTemplate()
    );
  }

  /**
   * Creates and returns the main group template for the diagram.
   *
   * @private
   * @returns {go.Group} The configured group template.
   * @memberof DiagramEditorComponent
   */
  private createGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Auto',
      {
        ...this.getClassOrAssociativeProperties(),
      },
      //Binding the common property
      ...this.getCommonBindings(),
      this.createGroupShape(),
      this.createGroupPanels(),
      ...linkPortList.map((linkPort) =>
        this.createPort(
          linkPort.portId,
          linkPort.alignment,
          linkPort.isFromLinkable,
          linkPort.isToLinkable
        )
      )
    );
  }

  /**
   * Handles the completion of a drop operation in the diagram editor
   * @param {go.InputEvent} event is related to drop orientation
   * @param {*} objectData is dragged object data
   * @memberof DiagramEditorComponent
   */
  handleDropCompletion = (event: go.InputEvent, objectData: any | null) => {
    if (!this._hasEditAccessOnly || this._diagrams.length == 0) {
      event.diagram.currentTool.doCancel();
      return;
    }
    const selectedObjects = event.diagram.selection;
    selectedObjects.each((obj) => {
      if (obj.data && obj.data.category === GojsNodeCategory.Association) {
        return;
      }
      if (
        objectData &&
        objectData.data.supportingLevels.includes(obj.data.category)
      ) {
        this.handleMemberDrop(obj, objectData, event);
      } else if (obj.data.isGroup) {
        this.handleTopLevelDrop(obj, event);
      } else {
        event.diagram.remove(obj);
        return;
      }
    });
  };

  /**
   * Creates or updates a class on the diagram.
   *
   * @param classData - The data of the class to be created or updated.
   * @param event - The GoJS input event.
   */
  private handleGroupNodeCreationOrUpdate(
    groupNodeData: go.ObjectData,
    isFromLibrary: boolean,
    event?: go.InputEvent
  ) {
    if (
      groupNodeData['category'] === GojsNodeCategory.Class ||
      groupNodeData['category'] === GojsNodeCategory.AssociativeClass
    ) {
      this.goJsClassService.handleClassCreationOrUpdate(
        groupNodeData as GojsDiagramClassNode,
        this._gojsDiagram,
        isFromLibrary,
        event
      );
    } else if (groupNodeData['category'] === GojsNodeCategory.Enumeration) {
      this.goJsEnumService.handleEnumCreationOrUpdate(
        groupNodeData as GojsDiagramEnumerationNode,
        this._gojsDiagram,
        isFromLibrary,
        event
      );
    }
  }

  /**
   * Handles top-level drops in the diagram.
   *
   * @param obj - The dropped object.
   * @param event - The GoJS input event.
   */
  private handleTopLevelDrop(obj: go.Part, event: go.InputEvent) {
    if (obj.data.allowTopLevelDrops === true) {
      event.diagram.commandHandler.addTopLevelParts(
        event.diagram.selection,
        true
      );
      if (
        obj.data.category === GojsNodeCategory.Class ||
        obj.data.category === GojsNodeCategory.Enumeration ||
        obj.data.category === GojsNodeCategory.AssociativeClass
      ) {
        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);
      } else if (obj.data.category === GojsNodeCategory.Comment) {
        this.gojsCommentService.handleCommentDrop(obj.data, this._gojsDiagram);
      }
      event.diagram.clearSelection();
    } else {
      event.diagram.currentTool.doCancel();
    }
  }

  /**
   * Handles member element drops in the diagram.
   * @param obj - The dropped object.
   * @param objectData - The target object data.
   * @param event - The GoJS input event.
   */
  private handleMemberDrop(
    obj: go.Part,
    objectData: any,
    event: go.InputEvent
  ) {
    if (
      objectData.data.supportingLevels.includes(obj.category) &&
      !event.diagram.toolManager.textEditingTool.isActive
    ) {
      if (
        obj.data.category === GojsNodeCategory.Attribute ||
        obj.data.category === GojsNodeCategory.Operation
      ) {
        this.goJsAttributeService.handleAttributeDrop(
          obj,
          objectData.data,
          event,
          this._gojsDiagram,
          this._hasEditAccessOnly
        );
        this.handleSelectionDeleting(objectData);
      } else if (obj.data.category === GojsNodeCategory.Class) {
        this.handleGroupNodeCreationOrUpdate(obj.data, false, event);
        objectData.addMembers(objectData.diagram.selection, true);
      } else if (obj.data.category === GojsNodeCategory.EnumerationLiteral) {
        this.goJsLiteralService.handleLiteralDrop(
          obj,
          objectData.data,
          event,
          this._gojsDiagram,
          this._hasEditAccessOnly
        );
        this.handleSelectionDeleting(objectData);
      }
    } else {
      const diagram = event.diagram;
      // Remove the dropped node from the diagram
      if (diagram && obj) {
        diagram.remove(obj);
      }
      // Cancel the operation and clear selection
      event.diagram.currentTool.doCancel();
    }
  }

  /**
   * Handles the selection deleting event for the diagram.
   *
   * @param objectData - The target object data.
   */
  private handleSelectionDeleting(objectData: any) {
    this._gojsDiagram.removeDiagramListener(
      'SelectionDeleting',
      this.eventListenerService.addDeletingEventListener
    );
    objectData.diagram.commandHandler.deleteSelection();
    this._gojsDiagram.addDiagramListener(
      'SelectionDeleting',
      this.eventListenerService.addDeletingEventListener
    );
    this._gojsDiagram.model.updateTargetBindings(objectData);
  }

  /**
   * Handles mouse drag enter events for groups.
   *
   * @private
   * @param {go.DiagramEvent} _e - The diagram event.
   * @param {go.GraphObject} grp - The group being entered.
   * @param {go.GraphObject} _prev - The previous object.
   */
  private handleMouseDragEnter(
    _e: go.DiagramEvent,
    grp: go.GraphObject,
    _prev: go.GraphObject
  ): void {
    this.highlightGroup(grp, true);
  }

  createNewFolder(name: string, projectId: number) {
    this.goJsFolderService.onCreateNewFolder(
      name,
      projectId,
      this._hasEditAccessOnly
    );
  }
  /**
   * Handles mouse drag leave events for groups.
   *
   * @private
   * @param {go.DiagramEvent} _e - The diagram event.
   * @param {go.GraphObject} grp - The group being left.
   * @param {go.GraphObject} _next - The next object.
   */
  private handleMouseDragLeave(
    _e: go.DiagramEvent,
    grp: go.GraphObject,
    _next: go.GraphObject
  ): void {
    this.highlightGroup(grp, false);
  }

  /**
   * Handles selection changes for groups.
   *
   * @private
   * @param {go.Part} node - The node whose selection changed.
   */
  private handleSelectionChanged(node: go.Part): void {
    this.propertyService.transferDataOnSelection(node);
  }

  /**
   * Group shape color is highlighted or not
   * @param {*} grp is used  to identify whether the element belongs to a group or
   * @param {boolean} show is used for group visible or not
   * @return {boolean}
   * @memberof DiagramEditorComponent
   */
  private highlightGroup(grp: any, show: boolean): boolean {
    if (!grp) return false;
    // check that the drop may really happen into the Group
    const tool = grp.diagram.toolManager.draggingTool;
    grp.isHighlighted = show && grp.canAddMembers(tool.draggingParts);
    return grp.isHighlighted;
  }

  /**
   * Toggles the visibility of small ports on a node in the GoJS diagram.
   *
   * @param event - The input event that triggers the visibility change.
   * @param node - The node whose ports will be shown or hidden.
   * @param show - A boolean indicating whether to show or hide the ports.
   */
  private toggleSmallPortsVisibility(node: go.Node, show: boolean): void {
    node.ports.each((port: go.GraphObject) => {
      if (port.portId !== '') {
        (port as go.Shape).fill = show ? 'rgba(0, 0, 0, 0.3)' : null;
      }
    });
    if (this._gojsDiagram && this._gojsDiagram.toolManager.linkingTool) {
      this._gojsDiagram.toolManager.linkingTool.archetypeLinkData = {
        category:
          node.data.category == GojsNodeCategory.AssociativeClass
            ? GojsNodeCategory.LinkToLink
            : GojsNodeCategory.Association,
      };
    }
  }

  /**
   * Creates the visual shape for groups.
   * @private
   * @returns {go.Shape} The shape configuration.
   */
  private createGroupShape(
    additionalProperties: go.ObjectData = {},
    isAssociative: boolean = false
  ): go.Shape {
    const bindingProps: go.Binding[] = [
      new go.Binding('visible', 'showTablePanel').makeTwoWay(),
      new go.Binding('fromLinkable', 'editable').makeTwoWay(),
      new go.Binding('toLinkable', 'editable').makeTwoWay(),
      new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(),
      new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(),
      new go.Binding('fill', 'color', (value: string) => {
        return this.$(go.Brush, 'Linear', {
          0.0: this.goJsCommonService.updateRGBAColorWithOpacity(value, 0.4),
          1.0: value,
        });
      }),
    ];
    if (!isAssociative) {
      bindingProps.push(new go.Binding('stroke', 'color').makeTwoWay());
    }
    return this.$(
      go.Shape,
      'RoundedRectangle',
      {
        cursor: 'pointer',
        ...additionalProperties,
      },
      ...bindingProps
    );
  }

  /**
   * Creates and returns the group panel with the specified properties and bindings.
   * @returns {go.Panel} The group panel.
   */
  private createGroupPanels(): go.Panel {
    return this.$(go.Panel, 'Vertical', this.createTablePanel());
  }

  /**
   * Creates and returns the table panel containing the components of the group panel.
   * @returns {go.Panel} The table panel.
   */
  private createTablePanel(): go.Panel {
    return this.$(
      go.Panel,
      'Table',
      {
        name: 'Shape',
        defaultRowSeparatorStroke: 'black',
        defaultRowSeparatorStrokeWidth: 1,
        portId: '',
        cursor: 'pointer',
        minSize: new go.Size(150, 100),
      },
      this.createRowColumnDefinitions(),
      this.createTitleTextBlock(),
      this.createPropertiesPanel(),
      this.createMethodsPanel(),
      new go.Binding('visible', 'showTablePanel').makeTwoWay(),
      new go.Binding('desiredSize', 'size').makeTwoWay()
    );
  }

  /**
   * Creates and returns the row and column definitions for the table panel.
   * @returns {go.RowColumnDefinition[]} The row and column definitions.
   */
  private createRowColumnDefinitions(): go.RowColumnDefinition[] {
    return [
      this.$(go.RowColumnDefinition, {
        row: 0,
        minimum: 25,
        maximum: 25,
        stretch: go.Stretch.Fill,
        separatorStrokeWidth: 1,
        separatorStroke: 'black',
      }),
      this.$(go.RowColumnDefinition, {
        row: 1,
        minimum: 60,
        stretch: go.Stretch.Fill,
        position: 20,
      }),
    ];
  }

  /**
   * Creates and returns the title TextBlock for the table panel.
   * @returns {go.TextBlock} The title TextBlock.
   */
  private createTitleTextBlock(): go.TextBlock {
    return this.$(
      go.TextBlock,
      {
        row: 0,
        columnSpan: 2,
        font: 'bold 12pt sans-serif',
        minSize: new go.Size(150, NaN),
        margin: new go.Margin(0, 4, 0, 4),
        isMultiline: false,
        wrap: go.Wrap.Fit,
        alignment: go.Spot.Center, // Centers the text in its container
        alignmentFocus: go.Spot.Center, // Ensures the text itself is focused at the center
        editable: true,
        textAlign: 'center',
        name: 'TEXTBLOCK',
        textEdited: this.handleTableTextEdited.bind(this),
      },
      new go.Binding('text', 'name').makeTwoWay(),
      new go.Binding('editable', 'editable').makeTwoWay()
    );
  }

  /**
   * Handles the text edited event to update the property data.
   * @param {go.TextBlock} textBlock - The TextBlock that was edited.
   * @param {string} oldString - The old text string.
   * @param {string} newString - The new text string.
   */
  private handleTableTextEdited(
    textBlock: go.TextBlock,
    oldString: string,
    newString: string
  ): void {
    if (!newString.trim()) {
      // If the new text is empty or contains only spaces, restore the old value
      textBlock.text = oldString;
    } else {
      const tableData = textBlock.part?.data;
      if (tableData.category === GojsNodeCategory.Comment) {
        this.gojsCommentService.updateComment(
          tableData.name,
          newString,
          tableData,
          this._gojsDiagram
        );
      } else {
        this.propertyService.transferDataOnSelection(textBlock.part!);
      }
      if (
        tableData.category === GojsNodeCategory.Class ||
        tableData.category === GojsNodeCategory.AssociativeClass
      ) {
        this.goJsClassService.updateTemplateClass(
          { ...tableData, name: newString },
          this._gojsDiagram
        );
      }
      if (tableData.category === GojsNodeCategory.Enumeration) {
        this.goJsEnumService.updateEnumerationFromDiagram(
          { ...tableData, name: newString },
          this._gojsDiagram
        );
      }
    }
  }

  /**
   * Creates and returns the properties panel for the table panel.
   * @returns {go.Panel} The properties panel.
   */
  private createPropertiesPanel(): go.Panel {
    return this.$(
      go.Panel,
      'Vertical',
      {
        name: 'PROPERTIES',
        row: 1,
        stretch: go.Stretch.Fill,
        alignment: go.Spot.Center,
        itemTemplate: this.initializeItemTemplate(),
      },
      new go.Binding('itemArray', 'items', (items) =>
        items.filter(
          (item: GojsDiagramLiteralNode | GojsDiagramAttributeNode) =>
            item.category == GojsNodeCategory.EnumerationLiteral ||
            item.category == GojsNodeCategory.Attribute
        )
      ),
      new go.Binding('visible', 'items', (items) =>
        items.some(
          (item: GojsDiagramLiteralNode | GojsDiagramAttributeNode) =>
            item.category === GojsNodeCategory.Attribute ||
            item.category === GojsNodeCategory.EnumerationLiteral
        )
      )
    );
  }

  /**
   * Creates and returns the methods panel for the table panel.
   * @returns {go.Panel} The methods panel.
   */
  private createMethodsPanel(): go.Panel {
    return this.$(
      go.Panel,
      'Vertical',
      {
        name: 'Methods',
        row: 2,
        stretch: go.Stretch.Fill,
        alignment: go.Spot.Center,
        itemTemplate: this.initializeItemTemplate(),
      },
      new go.Binding('itemArray', 'items', (items) =>
        items.filter(
          (item: GojsDiagramEnumerationNode | GojsDiagramAttributeNode) =>
            item.category == GojsNodeCategory.Operation
        )
      ),
      new go.Binding('visible', 'items', (items) =>
        items.some(
          (item: GojsDiagramEnumerationNode | GojsDiagramAttributeNode) =>
            item.category === GojsNodeCategory.Operation
        )
      )
    );
  }

  /**
   * Creates a port shape for a node in the GoJS diagram.
   *
   * @param portId - The unique identifier for the port.
   * @param alignment - The alignment spot for the port on the node.
   * @param isOutput - Specifies if the port is for outgoing links.
   * @param isInput - Specifies if the port is for incoming links.
   * @returns A GoJS Shape configured as a port.
   */
  private createPort(
    portId: string,
    alignment: go.Spot,
    isOutput: boolean,
    isInput: boolean
  ): go.Shape {
    return this.$(go.Shape, {
      figure: 'Circle',
      fill: 'transparent',
      stroke: null,
      desiredSize: new go.Size(9, 9),
      alignment: alignment,
      alignmentFocus: alignment,
      portId: portId,
      fromSpot: alignment,
      toSpot: alignment,
      fromLinkable: isOutput,
      toLinkable: isInput,
      fromLinkableSelfNode: isOutput,
      toLinkableSelfNode: isInput,
      cursor: 'pointer',
    });
  }

  /**
   * Creates a specialized group template for packages.
   *
   * @private
   * @returns {go.Group} The package group template.
   */
  private createPackageGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Auto',
      {
        background: 'blue',
        ungroupable: true,
        mouseDragEnter: (_e, grp) => this.highlightGroup(grp, true),
        mouseDragLeave: (_e, grp) => this.highlightGroup(grp, false),
        computesBoundsAfterDrag: true,
        computesBoundsIncludingLocation: true,
        mouseDrop: this.handleDropCompletion,
        handlesDragDropForMembers: true,
        resizable: true,
        resizeObjectName: 'Placeholder',
      },
      this.createPackageGroupShape(),
      this.createPackageGroupPanels()
    );
  }

  /**
   * Creates the visual shape for package groups.
   *
   * @private
   * @returns {go.Shape} The shape configuration.
   */
  private createPackageGroupShape(): go.Shape {
    return this.$(
      go.Shape,
      'RoundedRectangle',
      {
        stroke: this.defaultColor(true),
        fill: this.defaultColor(true),
        strokeWidth: 2,
      },
      new go.Binding('stroke', 'horiz', this.defaultColor),
      new go.Binding('fill', 'horiz', this.defaultColor)
    );
  }

  /**
   * Creates panels for package groups.
   *
   * @private
   * @returns {go.Panel} The panel configuration.
   */
  private createPackageGroupPanels(): go.Panel {
    return this.$(
      go.Panel,
      'Vertical',
      { name: 'Placeholder' },
      this.createPackageGroupHeader(),
      this.$(go.Placeholder, {
        padding: 5,
        alignment: go.Spot.LeftCenter,
        minSize: new go.Size(200, 150),
      })
    );
  }

  private createCommentGroupPanels(): go.Panel {
    return this.$(
      go.Panel,
      'Vertical',
      { name: 'Placeholder' },
      this.createPackageGroupHeader(),
      this.$(
        go.TextBlock,
        {
          font: 'bold 10pt Helvetica, Arial, sans-serif',
          margin: new go.Margin(10, 8, 4, 8),
          textAlign: 'left',
          maxLines: Infinity,
          minSize: new go.Size(NaN, 200),
          wrap: go.Wrap.Fit,
          // isMultiline: false,
          alignment: go.Spot.TopLeft, // Keep text aligned to the top-left corner
          overflow: go.TextOverflow.Clip, // Prevent text overflow
          stretch: go.Stretch.Fill,
          textEdited: this.handleTableTextEdited.bind(this),
          mouseDrop: (e: go.InputEvent, _obj: go.GraphObject) =>
            e.diagram.currentTool.doCancel(),
        },
        new go.Binding('text', 'description').makeTwoWay(),
        new go.Binding('editable', 'editable').makeTwoWay(),
        new go.Binding('desiredSize', 'size').makeTwoWay()
      )
    );
  }

  /**
   * Creates the header panel for package groups.
   *
   * @private
   * @returns {go.Panel} The header panel configuration.
   */
  private createPackageGroupHeader(): go.Panel {
    return this.$(
      go.Panel,
      'Table',
      {
        stretch: go.Stretch.Horizontal,
        background: this.defaultColor(true),
      },
      new go.Binding('background', 'horiz', this.defaultColor),
      this.$(
        go.TextBlock,
        {
          alignment: go.Spot.Left,
          stretch: go.Stretch.Horizontal,
          editable: true,
          isMultiline: false,
          wrap: go.Wrap.Fit,
          margin: 5,
          font: this.defaultFont(false),
          opacity: 0.95,
          stroke: '#404040',
          textEdited: this.handleTextEdited.bind(this),
        },
        new go.Binding('font', 'horiz', this.defaultFont),
        new go.Binding('text', 'name').makeTwoWay()
      )
    );
  }

  private handleTextEdited(
    textBlock: go.TextBlock,
    _oldString: string,
    newString: string
  ): void {
    const tableData: GojsDiagramCommentNode =
      textBlock.diagram?.selection.first()?.data;
    if (tableData.category === GojsNodeCategory.Comment) {
      this.gojsCommentService.updateComment(
        newString,
        tableData.description,
        tableData,
        this._gojsDiagram
      );
    }
  }

  /**
   *For getting the default color
   * @private
   * @param {boolean} horiz is boolean  value for horizontal or vertical layout
   * @return {*}  {string}
   * @memberof DiagramEditorComponent
   */
  private defaultColor(horiz: boolean): string {
    // a Binding conversion function
    return horiz ? 'rgba(255, 221, 51, 0.55)' : 'rgba(51,211,229, 0.5)';
  }

  /**
   *For getting the default font
   * @param {boolean} horiz is boolean  value for horizontal or vertical layout
   * @return {*}  {string}
   * @memberof DiagramEditorComponent
   */
  private defaultFont(horiz: boolean): string {
    // a Binding conversion function
    return horiz ? 'bold 20px sans-serif' : 'bold 16px sans-serif';
  }

  private createEnumerationGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Auto',
      {
        selectionAdornmentTemplate: this.$(
          go.Adornment,
          'Spot',
          this.createSelectionBorderPanel(),
          this.createActionButtonPanel(false)
        ),
        ...this.getCommonGroupProperties(),
      },
      //Binding the common property
      ...this.getCommonBindings(),
      this.createGroupShape(),
      this.createGroupPanels()
    );
  }

  /**
   * Creates a button for adding attributes to the group node.
   *
   * @returns {go.Panel} The configured attribute button panel.
   */
  private createAttributeButton(isForLiteral: boolean): go.Panel {
    return this.createActionButton(
      !isForLiteral ? GoJsNodeIcon.Attribute : GoJsNodeIcon.EnumerationLiteral,
      'Attribute',
      (_e, obj) =>
        isForLiteral
          ? this.addNodeLiteral(obj)
          : this.goJsAttributeService.addNodeAttribute(
              obj,
              'Attribute',
              GojsNodeCategory.Attribute,
              this._gojsDiagram
            )
    );
  }

  /**
   * Creates a button with an image and a specified click handler.
   * This method helps in reducing redundancy by allowing the creation of multiple buttons with similar structure.
   *
   * @param {string} imagePath - The path to the button image.
   * @param {Function} clickHandler - The function to be executed on button click.
   * @returns {go.Panel} The configured button panel.
   */
  private createActionButton(
    iconUnicode: string, // Accepts Font Awesome Unicode (e.g., '\uf1fe')
    tooltipText: string, // Tooltip text to display on hover
    clickHandler: (e: go.InputEvent, obj: go.GraphObject) => void
  ): go.Panel {
    return this.$(
      'Button',
      {
        click: clickHandler,
        margin: new go.Margin(0, 10, 0, 0), // Adds margin to the right for spacing
        toolTip: this.$(
          go.Adornment, // Tooltip container
          'Auto', // Auto layout for proper sizing
          {
            alignment: go.Spot.Top, // Align tooltip above the button
            alignmentFocus: go.Spot.Bottom, // Tooltip bottom edge aligns with button's top edge
          },
          this.$(
            go.Shape,
            { fill: 'white', stroke: null } // Tooltip background
          ),
          this.$(
            go.TextBlock,
            {
              font: '10pt sans-serif',
              margin: 5,
              textAlign: 'center',
              wrap: go.Wrap.Fit, // Allow text wrapping if necessary
              alignment: go.Spot.Top, // Align tooltip above the button
              alignmentFocus: go.Spot.Top, // Tooltip bottom edge aligns with button's top edge
            },
            tooltipText // Tooltip text content
          )
        ),
      },
      this.$(
        go.TextBlock, // Use a TextBlock for Font Awesome icons
        {
          font: '14px FontAwesome', // Font for Font Awesome icons
          text: iconUnicode, // Set the icon Unicode
          textAlign: 'center',
          verticalAlignment: go.Spot.Center,
          desiredSize: new go.Size(15, 15), // Adjust size as needed
        }
      )
    );
  }

  /**
   * Adds a literal node to the currently selected node in the GoJS diagram.
   *
   * This method retrieves the currently selected node in the diagram and uses
   * the `GoJsLiteralService` to create a new literal node associated with the selected node.
   * The `idTemplateEnumeration` is passed to help identify the template enumeration.
   *
   * @param obj - The GoJS `GraphObject` from which the selected node is retrieved.
   */
  private addNodeLiteral(obj: go.GraphObject): void {
    // Get the currently selected node in the diagram
    const selectedNode = obj.diagram!.selection.first()!;
    // Extract the `idTemplateEnumeration` from the selected node's data
    const idTemplateEnumeration = selectedNode.data.idTemplateEnumeration;
    // Use the GoJsLiteralService to create a literal node
    this.goJsLiteralService.onCreateLiteral(
      selectedNode.data,
      'Literal',
      idTemplateEnumeration,
      this._gojsDiagram
    );
  }

  /**
   * Configures the link template for the diagram with specific behaviors and styles.
   * This method orchestrates the creation of link templates and applies them to the diagram.
   *
   * @private
   * @memberof DiagramEditorComponent
   */
  private configureLinkTemplate(): void {
    this._gojsDiagram.linkTemplate = this.createLinkTemplate();
    this._gojsDiagram.linkTemplateMap.add(
      GojsNodeCategory.LinkToLink,
      this.createLinkToLinkTemplate()
    );
  }

  /**
   * Creates and returns the link template for the diagram.
   *
   * @private
   * @returns {go.Link} The configured link template.
   * @memberof DiagramEditorComponent
   */
  private createLinkTemplate(): go.Link {
    return this.$(
      go.Link,
      {
        selectionChanged: this.handleSelectionChanged.bind(this),
      },
      this.customizeLinkTemplate(),
      new go.Binding('selectable', 'editable').makeTwoWay(),
      new go.Binding('reshapable', 'editable').makeTwoWay(),
      new go.Binding('resegmentable', 'editable').makeTwoWay(),
      new go.Binding('relinkableFrom', 'editable').makeTwoWay(),
      new go.Binding('relinkableTo', 'editable').makeTwoWay(),
      new go.Binding('deletable', 'editable').makeTwoWay(),
      new go.Binding('toLinkableDuplicates', 'editable').makeTwoWay(),
      // Instead of 'editable', you might use the following bindings:
      new go.Binding('fromLinkable', 'editable').makeTwoWay(),
      new go.Binding('toLinkable', 'editable').makeTwoWay(),
      new go.Binding('fromLinkableSelfNode', 'editable').makeTwoWay(),
      new go.Binding('toLinkableSelfNode', 'editable').makeTwoWay(),
      this.createLinkShape(),
      this.createLinkNameShape('name'),
      this.createCardinalityTextBlock(
        'Cardinality1',
        0,
        0.5,
        new go.Point(15, -15),
        'cardinalityTo'
      ),
      this.createCardinalityTextBlock(
        'Cardinality2',
        -1,
        0.5,
        new go.Point(-15, -15),
        'cardinalityFrom'
      ),
      this.$(
        go.TextBlock,
        {
          // text: 'fromComment',
          segmentIndex: 0,
          segmentOffset: new go.Point(NaN, 15),
          editable: true,
          maxSize: new go.Size(200, NaN),
          overflow: go.TextOverflow.Ellipsis, // Optional: Handle overflow gracefully
          wrap: go.Wrap.Fit,
          isMultiline: false,
        },
        new go.Binding('text', 'fromComment').makeTwoWay()
      ),
      this.$(
        go.TextBlock,
        {
          // text: 'toComment',
          segmentIndex: -1,
          segmentOffset: new go.Point(NaN, 15),
          editable: true,
          maxSize: new go.Size(200, NaN),
          overflow: go.TextOverflow.Ellipsis, // Optional: Handle overflow gracefully
          wrap: go.Wrap.Fit,
          isMultiline: false,
        },
        new go.Binding('text', 'toComment').makeTwoWay()
      )
    );
  }

  private createLinkToLinkTemplate(): go.Link {
    return this.$(
      go.Link,
      {
        deletable: false,
        movable: false,
      },
      new go.Binding('relinkableFrom', 'editable'),
      new go.Binding('relinkableTo', 'editable'),
      new go.Binding('selectable', 'editable'),
      new go.Binding('reshapable', 'editable'),
      this.$(go.Shape, {
        fill: 'black',
        strokeWidth: 2,
        strokeDashArray: [4, 8],
      })
    );
  }
  private createLinkNameShape(bindingProp: string): go.Panel {
    return this.$(
      go.Panel,
      'Auto',
      this.$(go.Shape, {
        fill: this.$(go.Brush, 'Radial'),
        stroke: 'transparent',
        background: 'white',
      }),
      this.$(
        go.TextBlock,
        {
          textAlign: 'center',
          font: '10pt helvetica, arial, sans-serif',
          stroke: 'black',
          margin: 4,
          isMultiline: false,
          editable: true,
        },
        new go.Binding('text', bindingProp).makeTwoWay(),
        new go.Binding(
          'segmentFraction',
          'labelPosition.segmentFraction'
        ).makeTwoWay()
      ),
      new go.Binding(
        'segmentOffset',
        'segmentOffset',
        go.Point.parse
      ).makeTwoWay(go.Point.stringify)
    );
  }
  /**
   * For customize the link shape and style in pallette
   * @private
   * @returns {go.Link}
   * @memberOf DiagramEditorComponent
   */
  private customizeLinkTemplate(): go.Link {
    return {
      routing: go.Routing.AvoidsNodes,
      corner: 5,
      fromEndSegmentLength: 30,
      toEndSegmentLength: 30,
      curve: go.Curve.JumpOver,
      toShortLength: 4,
    } as go.Link;
  }

  /**
   * Creates a shape for the link.
   * @private
   * @returns {go.Shape} The shape configuration.
   */
  private createLinkShape(): go.Shape {
    return this.$(
      go.Shape,
      {
        segmentFraction: 10,
        width: 100,
        strokeWidth: 1.2, // Add a slightly thicker stroke for better visibility
      },
      // Add color bindings
      new go.Binding('stroke', 'color').makeTwoWay(),
      new go.Binding('strokeWidth', 'isHighlighted', (h) =>
        h ? 2.5 : 1.5
      ).ofObject()
    );
  }

  /**
   * Creates a text block for cardinality with specified bindings, choices, and styles.
   * @private
   * @param {string} name - The name of the text block.
   * @param {number} segmentIndex - The segment index.
   * @param {number} segmentFraction - The segment fraction.
   * @param {go.Point} segmentOffset - The segment offset.
   * @param {string} cardinalityText - The cardinality name
   * @returns {go.TextBlock} The configured cardinality text block.
   */
  private createCardinalityTextBlock(
    name: string,
    segmentIndex: number,
    segmentFraction: number,
    segmentOffset: go.Point,
    cardinalityText: string
  ): go.TextBlock {
    return this.$(
      go.TextBlock,
      {
        name: name,
        segmentIndex: segmentIndex,
        segmentFraction: segmentFraction,
        segmentOffset: segmentOffset,
        textEditor: window.TextEditorSelectBox,
        choices: ['0..1', '1', '*', '1..*'],
      },
      new go.Binding('text', cardinalityText).makeTwoWay(),
      new go.Binding('choices', 'choices').makeTwoWay(),
      new go.Binding('editable', 'editable').makeTwoWay()
    );
  }

  /**
   * Configures the selection adornment template for group nodes in the diagram.
   * The adornment includes a border and buttons for adding attributes, methods, and drawing links.
   *
   * @private
   *
   * @memberOf DiagramEditorComponent
   */
  private configureGroupTemplateAdornment(): void {
    this._gojsDiagram.groupTemplate.selectionAdornmentTemplate = this.$(
      go.Adornment,
      'Spot',
      this.createSelectionBorderPanel(),
      this.createActionButtonPanel(true, false)
    );
  }

  /**
   * Creates an Auto Panel that serves as the background for the adornment,
   * with a border to highlight the selected group node.
   * @private
   *
   * @returns {go.Panel} The configured Auto Panel.
   * @memberOf DiagramEditorComponent
   */
  private createSelectionBorderPanel(): go.Panel {
    return this.$(
      go.Panel,
      'Auto',
      this.$(go.Shape, { stroke: 'dodgerblue', strokeWidth: 2, fill: null }),
      this.$(go.Placeholder)
    );
  }

  /**
   * Creates a Horizontal Panel that contains buttons for adding attributes, methods, and drawing links.
   * The panel is aligned at the top of the group node and positioned just below it.
   *
   * @returns {go.Panel} The configured Horizontal Panel.
   */
  private createActionButtonPanel(
    isClass: boolean,
    isAssociative: boolean = false
  ): go.Panel {
    const actionPanels: go.Panel[] = [];
    if (isClass) {
      actionPanels.push(this.createAttributeButton(false));
      actionPanels.push(this.createMethodButton());
      if (!isAssociative) actionPanels.push(this.createLinkButton());
    } else {
      actionPanels.push(this.createAttributeButton(true));
    }
    return this.$(
      go.Panel,
      'Horizontal',
      {
        alignment: go.Spot.Top,
        alignmentFocus: go.Spot.Bottom,
      },
      ...actionPanels
    );
  }

  /**
   * Creates a button for adding methods to the group node.
   * @returns {go.Panel} The configured method button panel.
   */
  private createMethodButton(): go.Panel {
    return this.createActionButton(
      GoJsNodeIcon.Operation,
      'Method',
      (_e, obj) =>
        this.goJsAttributeService.addNodeAttribute(
          obj,
          'Method',
          GojsNodeCategory.Operation,
          this._gojsDiagram
        )
    );
  }

  /**
   * Creates a button for drawing links between nodes.
   * The button supports both click and drag actions to initiate a link drawing operation.
   * @returns {go.Panel} The configured link button panel.
   */
  private createLinkButton(): go.Panel {
    return this.$(
      'Button',
      {
        click: (e, obj) => this.initiateLinkDrawing(e, obj), // Click on button and then click on target node
        actionMove: (e, obj) => this.initiateLinkDrawing(e, obj), // Drag from button to the target node
        toolTip: this.$(
          go.Adornment, // Tooltip container
          'Auto', // Auto layout for proper sizing
          {
            alignment: go.Spot.Top, // Align tooltip above the button
            alignmentFocus: go.Spot.Bottom, // Tooltip bottom edge aligns with button's top edge
          },
          this.$(
            go.Shape,
            { fill: 'white', stroke: null } // Tooltip background
          ),
          this.$(
            go.TextBlock,
            {
              font: '10pt sans-serif',
              margin: 5,
              textAlign: 'center',
              wrap: go.Wrap.Fit, // Allow text wrapping if necessary
              alignment: go.Spot.Top, // Align tooltip above the button
              alignmentFocus: go.Spot.Top, // Tooltip bottom edge aligns with button's top edge
            },
            'Draw Link' // Tooltip text content
          )
        ),
      },
      this.$(go.Shape, {
        geometryString: 'M0 0 L8 0 8 12 14 12 M12 10 L14 12 12 14', // Link shape geometry
        desiredSize: new go.Size(15, 15),
        fill: 'lightyellow',
      })
    );
  }

  /**
   * Initiates the linking tool to draw a link from the selected node.
   * @param {go.InputEvent} event - The input event triggering the link creation.
   * @param {go.GraphObject} button - The button that was clicked to start drawing the link.
   */
  private initiateLinkDrawing(event: go.InputEvent, button: any): void {
    const selectedNode = button.part.adornedPart;
    const linkingTool = event.diagram.toolManager.linkingTool;
    const specificPort = selectedNode.findPort('R2');
    if (specificPort) {
      linkingTool.startObject = specificPort;
      event.diagram.currentTool = linkingTool;
      linkingTool.doActivate();
    } else {
      console.error('Port not found on the selected node');
    }
  }

  /**
   * Sets up the model for the diagram
   *
   * @private
   * @memberof DiagramEditorComponent
   */
  private setupDiagramModel(): void {
    this.diagramUtils.initializeDiagramModelData(this._gojsDiagram);
  }

  private createCommentGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Auto',
      {
        background: 'blue',
        ungroupable: true,
        computesBoundsAfterDrag: true,
        computesBoundsIncludingLocation: true,
        isSubGraphExpanded: false,
        handlesDragDropForMembers: true,
        resizable: true,
        resizeObjectName: 'Placeholder',
      },
      //Binding the common property
      ...this.getCommonBindings(),
      new go.Binding('background', 'isHighlighted', (h) =>
        h ? 'rgba(255,0,0,0.2)' : 'transparent'
      ).ofObject(),
      this.createPackageGroupShape(),
      this.createCommentGroupPanels()
    );
  }

  /**
   * Creates and returns a node template for the palette.
   *
   * @private
   * @returns {go.Node} The configured node template.
   * @memberof DiagramEditorComponent
   */
  private getComponentNodeTemplate(): go.Node {
    return this.$(
      go.Node,
      'Horizontal',
      this.$(
        go.TextBlock, // Replace Picture with TextBlock for Font Awesome icons
        {
          width: 14,
          height: 14,
          font: '13px FontAwesome', // Use FontAwesome font
          textAlign: 'center',
          verticalAlignment: go.Spot.Center,
          margin: new go.Margin(0, 5, 0, 0), // Adjust margin as needed
        },
        new go.Binding('text', 'icon') // Bind to the icon property
      ),
      this.$(
        go.TextBlock,
        {
          stroke: 'black',
          font: '10pt sans-serif',
          editable: true,
          isMultiline: false,
          cursor: 'pointer',
          portId: '',
        },
        new go.Binding('text', 'name')
      )
    );
  }

  /**
   * Creates and returns a group template for the palette.
   *
   * @private
   * @returns {go.Group} The configured group template.
   * @memberof DiagramEditorComponent
   */
  private getComponentGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Horizontal',
      {
        cursor: 'pointer',
      },
      new go.Binding('selectable', 'editable').makeTwoWay(),
      this.$(
        go.TextBlock, // Replace Picture with TextBlock for Font Awesome icons
        {
          height: 14,
          width: 14,
          font: '13px FontAwesome', // Use FontAwesome font
          textAlign: 'center',
          verticalAlignment: go.Spot.Center,
          margin: new go.Margin(0, 5, 0, 0), // Adjust margin as needed
        },
        new go.Binding('text', 'icon') // Bind to the icon property
      ),
      this.$(
        go.TextBlock,
        {
          name: 'Label',
          stroke: 'black',
          font: '10pt sans-serif',
          alignment: go.Spot.Right,
          alignmentFocus: go.Spot.Left,
        },
        new go.Binding('text', 'name')
      )
    );
  }

  private createAssociativeClassGroupTemplate(): go.Group {
    return this.$(
      go.Group,
      'Auto',
      {
        selectionAdornmentTemplate: this.$(
          go.Adornment,
          'Spot',
          this.createSelectionBorderPanel(),
          this.createActionButtonPanel(true, true)
        ),
        ...this.getClassOrAssociativeProperties(),
      },
      //Binding the common property
      ...this.getCommonBindings(),
      this.createGroupShape(
        {
          stroke: 'black',
          strokeWidth: 1.5,
          strokeDashArray: [4, 8],
        },
        true
      ),
      this.createGroupPanels(),
      ...linkPortList.map((linkPort) =>
        this.createPort(
          linkPort.portId,
          linkPort.alignment,
          linkPort.isFromLinkable,
          linkPort.isToLinkable
        )
      )
    );
  }

  private createLinkLabelTemplate(): go.Node {
    return this.$(
      go.Node,
      {
        avoidable: true,
        layerName: 'Foreground',
        movable: false,
        deletable: false,
        fromLinkableSelfNode: false,
      },
      new go.Binding('selectable', 'editable').makeTwoWay(),
      this.$(
        go.Shape,
        'Ellipse',
        {
          width: 0.5,
          height: 0.5,
          fill: 'rgba(0,0,0,0.01)', // Nearly transparent fill
          stroke: 'rgba(0,0,0,0.05)', // Very light stroke
          portId: '',
          cursor: 'pointer',
          fromLinkable: false,
        },
        new go.Binding('toLinkable', 'editable').makeTwoWay()
      )
    );
  }

  /**
   * Retrieves common properties and event handlers shared across all group elements in the diagram.
   * @private
   * @return {*}  {go.ObjectData} An object containing properties and event handlers for group elements.
   * @memberof GojsService
   */
  private getCommonGroupProperties(): go.ObjectData {
    return {
      mouseDragEnter: () => this.handleMouseDragEnter.bind(this),
      mouseDragLeave: () => this.handleMouseDragLeave.bind(this),
      computesBoundsAfterDrag: true,
      computesBoundsIncludingLocation: true,
      mouseDrop: this.handleDropCompletion,
      resizeObjectName: 'Shape',
      selectionChanged: this.handleSelectionChanged.bind(this),
    };
  }

  /**
   * Retrieves properties configuration for Class and Associative Class elements in the diagram.
   * @private
   * @return {*}  {go.ObjectData} An object containing properties and event handlers for Class elements
   * @memberof GojsService
   */
  private getClassOrAssociativeProperties(): go.ObjectData {
    return {
      ...this.getCommonGroupProperties(),
      mouseEnter: (_e: MouseEvent, node: go.Node) =>
        this.toggleSmallPortsVisibility(node, true),
      mouseLeave: (_e: MouseEvent, node: go.Node) =>
        this.toggleSmallPortsVisibility(node, false),
      linkValidation: (
        fromNode: go.Node,
        _fromPort: go.GraphObject,
        toNode: go.Node,
        _toPort: go.GraphObject,
        link: go.Link
      ) =>
        this.goJsCardinalityService.validateGroupLink(
          fromNode,
          toNode,
          link,
          this._gojsDiagram
        ),
    };
  }

  /**
   * Returns an array of common two-way bindings used across different GoJS group templates.
   * @private
   * @return {*}  {go.Binding[]} An array of GoJS Binding objects configured for two-way data binding
   * @memberof GojsService
   */
  private getCommonBindings(): go.Binding[] {
    return [
      new go.Binding('resizable', 'editable').makeTwoWay(),
      // new go.Binding('id', 'id').makeTwoWay(),
      new go.Binding('selectable', 'editable').makeTwoWay(),
      new go.Binding('handlesDragDropForMembers', 'editable').makeTwoWay(),
      new go.Binding('position', 'position', go.Point.parse).makeTwoWay(
        go.Point.stringify
      ),
    ];
  }

  /**
   * Configures the palette with the provided templates.
   * @private
   * @param {go.Node} paletteTemplate - The node template for the palette.
   * @param {go.Group} groupTemplate - The group template for the palette.
   * @memberof DiagramEditorComponent
   */
  private configureComponentPalette(
    paletteTemplate: go.Node,
    groupTemplate: go.Group
  ): void {
    paletteConfigs.forEach((config) => {
      if (!go.Palette.fromDiv(config.name)) {
        const palette = new go.Palette(config.name);

        // Configure palette settings
        palette.allowZoom = false;
        palette.allowDrop =
          !this._gojsDiagram.toolManager.textEditingTool.isActive;

        // Set the node template (left-aligned version)
        palette.nodeTemplate = paletteTemplate;

        // Set the group template
        palette.groupTemplate = groupTemplate;

        // Configure the palette layout as a grid
        palette.layout = this.$(go.GridLayout, {
          wrappingColumn: 2, // Number of columns in the grid
          // spacing: new go.Size(10, 10), // Spacing between items
          alignment: go.GridAlignment.Position, // Align items to the grid positions
        });

        // Set the model
        const model = new go.GraphLinksModel(config.data, []);
        if (config.links) {
          model.addLinkDataCollection(config.links);
        }
        palette.model = model;
      }
    });
  }
}
