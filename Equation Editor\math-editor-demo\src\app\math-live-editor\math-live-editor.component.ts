import {
  AfterViewInit,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { all, create } from 'mathjs';
import { MathfieldElement } from 'mathlive';
@Component({
  selector: 'app-math-live-editor',
  templateUrl: './math-live-editor.component.html',
  styleUrls: ['./math-live-editor.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class MathLiveEditorComponent implements AfterViewInit {
  @ViewChild('mathLiveContainer') mathLiveContainer!: ElementRef;
  latexOutput: string = '';
  mathField!: MathfieldElement;

  // Create an instance of math.js
  private math = create(all);
  ngAfterViewInit(): void {
    // Initialize the MathLive editor with configuration options
    this.mathField = new MathfieldElement();
    // Append the MathLive editor to the container
    this.mathLiveContainer.nativeElement.appendChild(this.mathField);

    // Event listeners
    this.mathField.addEventListener('input', () => {
      // Update LaTeX output dynamically
      this.latexOutput = this.mathField.getValue();
    });

    // this.mathField.addEventListener('selectionDidChange', () => {
    //   console.log('Selection changed:', this.mathField.getSelection());
    // });

    this.mathField.addEventListener('error', (error: any) => {
      console.error('MathLive Error:', error);
    });
  }

  /**
   * Get the current LaTeX value of the editor.
   */
  getLatex() {
    this.latexOutput = this.mathField.getValue();
    alert(`LaTeX Output: ${this.latexOutput}`);
  }

  /**
   * Insert a custom LaTeX snippet into the editor.
   */
  insertSnippet(snippet: string) {
    this.mathField.executeCommand(['insert', snippet]);
  }

  /**
   * Toggle read-only mode.
   */
  toggleReadOnly() {
    // const isReadOnly = this.mathField.getConfig().readOnly;
    // this.mathField.setConfig({ readOnly: !isReadOnly });
  }

  /**
   * Focus the editor programmatically.
   */
  focusEditor() {
    this.mathField.focus();
  }

  /**
   * Clear the editor content.
   */
  clearEditor() {
    this.mathField.setValue('');
    this.latexOutput = '';
  }

  /**
   * Undo the last action.
   */
  undo() {
    this.mathField.executeCommand('undo');
  }

  /**
   * Redo the last undone action.
   */
  redo() {
    this.mathField.executeCommand('redo');
  }
  computeResult(): void {
    // Retrieve the current LaTeX expression from the MathLive field
    const latex = this.mathField.getValue();

    // Convert LaTeX to an evaluable arithmetic expression
    // Example: convert "\frac{5}{2}" to "(5/2)"
    let expression = latex.replace(/\\frac{([^}]+)}{([^}]+)}/g, '($1/$2)');

    // Additional conversion steps could be added here as needed

    try {
      // Evaluate the arithmetic expression using math.js
      const result = this.math.evaluate(expression);
      alert(`Result: ${result}`);
    } catch (error: any) {
      alert(`Error evaluating expression: ${error.message || error}`);
    }
  }
}
