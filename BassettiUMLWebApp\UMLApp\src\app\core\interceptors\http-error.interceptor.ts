import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ErrorService } from '../services/errors/error.service';
import { UserService } from '../services/user/user.service';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {
  constructor(
    private errorService: ErrorService,
    private userService: UserService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    if (!this.errorService.isRequestBlackListed(request.url)) {
      return next.handle(request).pipe(
        catchError((errorObj: HttpErrorResponse) => {
          if (errorObj.status == 401) {
            return this.handleUnauthorizedError(request, next);
          } else if (
            (errorObj.status == 404 &&
              errorObj.error?.type === 'RefreshToken') ||
            (errorObj.status === 400 &&
              errorObj.error?.errorKey === 'INVALID_GRANT')
          ) {
            this.userService.logout(true);
            return throwError(() => errorObj);
          } else if (
            errorObj.status == 400 &&
            (errorObj.error?.errorKey == 'ALREADY_DELETED_EVENT' ||
              errorObj.error?.errorKey == 'ALREADY_EXIST')
          ) {
            return throwError(() => errorObj);
          } else {
            this.errorService.addError(errorObj);
            return throwError(() => errorObj);
          }
        })
      );
    } else {
      return next.handle(request);
    }
  }

  /**
   * Handles an unauthorized error in an HTTP request by refreshing the access token,
   * updating the tokens, and creating a new request with the updated tokens.
   *
   * @private
   * @param {HttpRequest<any>} request The original HTTP request.
   * @param {HttpHandler} next The HTTP handler for the next interceptor in the chain.
   * @return  {Observable<HttpEvent<any>>}
   * @memberof HttpErrorInterceptor
   */
  private handleUnauthorizedError(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    return this.userService.refreshToken().pipe(
      switchMap(() => {
        const newRequest = request.clone({
          // withCredentials: true,
          setHeaders: {
            Authorization: environment.bearerToken,
          },
        });
        return next.handle(newRequest);
      }),
      catchError((errObj) => {
        this.errorService.addError(errObj);
        this.userService.logout(true);
        return throwError(() => errObj);
      })
    );
  }
}
