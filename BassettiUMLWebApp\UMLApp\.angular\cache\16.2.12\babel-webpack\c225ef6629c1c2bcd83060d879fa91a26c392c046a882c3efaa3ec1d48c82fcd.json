{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, switchMap } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/errors/error.service\";\nimport * as i2 from \"../services/user/user.service\";\nexport class HttpErrorInterceptor {\n  constructor(errorService, userService) {\n    this.errorService = errorService;\n    this.userService = userService;\n  }\n  intercept(request, next) {\n    if (!this.errorService.isRequestBlackListed(request.url)) {\n      return next.handle(request).pipe(catchError(errorObj => {\n        if (errorObj.status == 401) {\n          return this.handleUnauthorizedError(request, next);\n        } else if (errorObj.status == 404 && errorObj.error?.type === 'RefreshToken' || errorObj.status === 400 && errorObj.error?.errorKey === 'INVALID_GRANT') {\n          this.userService.logout(true);\n          return throwError(() => errorObj);\n        } else if (errorObj.status == 400 && (errorObj.error?.errorKey == 'ALREADY_DELETED_EVENT' || errorObj.error?.errorKey == 'ALREADY_EXIST')) {\n          return throwError(() => errorObj);\n        } else {\n          this.errorService.addError(errorObj);\n          return throwError(() => errorObj);\n        }\n      }));\n    } else {\n      return next.handle(request);\n    }\n  }\n  /**\n   * Handles an unauthorized error in an HTTP request by refreshing the access token,\n   * updating the tokens, and creating a new request with the updated tokens.\n   *\n   * @private\n   * @param {HttpRequest<any>} request The original HTTP request.\n   * @param {HttpHandler} next The HTTP handler for the next interceptor in the chain.\n   * @return  {Observable<HttpEvent<any>>}\n   * @memberof HttpErrorInterceptor\n   */\n  handleUnauthorizedError(request, next) {\n    return this.userService.refreshToken().pipe(switchMap(() => {\n      const newRequest = request.clone({\n        // withCredentials: true,\n        setHeaders: {\n          Authorization: environment.bearerToken\n        }\n      });\n      return next.handle(newRequest);\n    }), catchError(errObj => {\n      this.errorService.addError(errObj);\n      this.userService.logout(true);\n      return throwError(() => errObj);\n    }));\n  }\n  static #_ = this.ɵfac = function HttpErrorInterceptor_Factory(t) {\n    return new (t || HttpErrorInterceptor)(i0.ɵɵinject(i1.ErrorService), i0.ɵɵinject(i2.UserService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HttpErrorInterceptor,\n    factory: HttpErrorInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["throwError", "catchError", "switchMap", "environment", "HttpErrorInterceptor", "constructor", "errorService", "userService", "intercept", "request", "next", "isRequestBlackListed", "url", "handle", "pipe", "errorObj", "status", "handleUnauthorizedError", "error", "type", "<PERSON><PERSON><PERSON>", "logout", "addError", "refreshToken", "newRequest", "clone", "setHeaders", "Authorization", "bearerToken", "err<PERSON><PERSON><PERSON>", "_", "i0", "ɵɵinject", "i1", "ErrorService", "i2", "UserService", "_2", "factory", "ɵfac"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\interceptors\\http-error.interceptor.ts"], "sourcesContent": ["import {\r\n  HttpErrorResponse,\r\n  HttpEvent,\r\n  HttpHandler,\r\n  HttpInterceptor,\r\n  HttpRequest,\r\n} from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable, throwError } from 'rxjs';\r\nimport { catchError, switchMap } from 'rxjs/operators';\r\nimport { environment } from '../../../environments/environment';\r\nimport { ErrorService } from '../services/errors/error.service';\r\nimport { UserService } from '../services/user/user.service';\r\n\r\n@Injectable()\r\nexport class HttpErrorInterceptor implements HttpInterceptor {\r\n  constructor(\r\n    private errorService: ErrorService,\r\n    private userService: UserService\r\n  ) {}\r\n\r\n  intercept(\r\n    request: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    if (!this.errorService.isRequestBlackListed(request.url)) {\r\n      return next.handle(request).pipe(\r\n        catchError((errorObj: HttpErrorResponse) => {\r\n          if (errorObj.status == 401) {\r\n            return this.handleUnauthorizedError(request, next);\r\n          } else if (\r\n            (errorObj.status == 404 &&\r\n              errorObj.error?.type === 'RefreshToken') ||\r\n            (errorObj.status === 400 &&\r\n              errorObj.error?.errorKey === 'INVALID_GRANT')\r\n          ) {\r\n            this.userService.logout(true);\r\n            return throwError(() => errorObj);\r\n          } else if (\r\n            errorObj.status == 400 &&\r\n            (errorObj.error?.errorKey == 'ALREADY_DELETED_EVENT' ||\r\n              errorObj.error?.errorKey == 'ALREADY_EXIST')\r\n          ) {\r\n            return throwError(() => errorObj);\r\n          } else {\r\n            this.errorService.addError(errorObj);\r\n            return throwError(() => errorObj);\r\n          }\r\n        })\r\n      );\r\n    } else {\r\n      return next.handle(request);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles an unauthorized error in an HTTP request by refreshing the access token,\r\n   * updating the tokens, and creating a new request with the updated tokens.\r\n   *\r\n   * @private\r\n   * @param {HttpRequest<any>} request The original HTTP request.\r\n   * @param {HttpHandler} next The HTTP handler for the next interceptor in the chain.\r\n   * @return  {Observable<HttpEvent<any>>}\r\n   * @memberof HttpErrorInterceptor\r\n   */\r\n  private handleUnauthorizedError(\r\n    request: HttpRequest<any>,\r\n    next: HttpHandler\r\n  ): Observable<HttpEvent<any>> {\r\n    return this.userService.refreshToken().pipe(\r\n      switchMap(() => {\r\n        const newRequest = request.clone({\r\n          // withCredentials: true,\r\n          setHeaders: {\r\n            Authorization: environment.bearerToken,\r\n          },\r\n        });\r\n        return next.handle(newRequest);\r\n      }),\r\n      catchError((errObj) => {\r\n        this.errorService.addError(errObj);\r\n        this.userService.logout(true);\r\n        return throwError(() => errObj);\r\n      })\r\n    );\r\n  }\r\n}\r\n"], "mappings": "AAQA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,SAASC,WAAW,QAAQ,mCAAmC;;;;AAK/D,OAAM,MAAOC,oBAAoB;EAC/BC,YACUC,YAA0B,EAC1BC,WAAwB;IADxB,KAAAD,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEHC,SAASA,CACPC,OAAyB,EACzBC,IAAiB;IAEjB,IAAI,CAAC,IAAI,CAACJ,YAAY,CAACK,oBAAoB,CAACF,OAAO,CAACG,GAAG,CAAC,EAAE;MACxD,OAAOF,IAAI,CAACG,MAAM,CAACJ,OAAO,CAAC,CAACK,IAAI,CAC9Bb,UAAU,CAAEc,QAA2B,IAAI;QACzC,IAAIA,QAAQ,CAACC,MAAM,IAAI,GAAG,EAAE;UAC1B,OAAO,IAAI,CAACC,uBAAuB,CAACR,OAAO,EAAEC,IAAI,CAAC;SACnD,MAAM,IACJK,QAAQ,CAACC,MAAM,IAAI,GAAG,IACrBD,QAAQ,CAACG,KAAK,EAAEC,IAAI,KAAK,cAAc,IACxCJ,QAAQ,CAACC,MAAM,KAAK,GAAG,IACtBD,QAAQ,CAACG,KAAK,EAAEE,QAAQ,KAAK,eAAgB,EAC/C;UACA,IAAI,CAACb,WAAW,CAACc,MAAM,CAAC,IAAI,CAAC;UAC7B,OAAOrB,UAAU,CAAC,MAAMe,QAAQ,CAAC;SAClC,MAAM,IACLA,QAAQ,CAACC,MAAM,IAAI,GAAG,KACrBD,QAAQ,CAACG,KAAK,EAAEE,QAAQ,IAAI,uBAAuB,IAClDL,QAAQ,CAACG,KAAK,EAAEE,QAAQ,IAAI,eAAe,CAAC,EAC9C;UACA,OAAOpB,UAAU,CAAC,MAAMe,QAAQ,CAAC;SAClC,MAAM;UACL,IAAI,CAACT,YAAY,CAACgB,QAAQ,CAACP,QAAQ,CAAC;UACpC,OAAOf,UAAU,CAAC,MAAMe,QAAQ,CAAC;;MAErC,CAAC,CAAC,CACH;KACF,MAAM;MACL,OAAOL,IAAI,CAACG,MAAM,CAACJ,OAAO,CAAC;;EAE/B;EAEA;;;;;;;;;;EAUQQ,uBAAuBA,CAC7BR,OAAyB,EACzBC,IAAiB;IAEjB,OAAO,IAAI,CAACH,WAAW,CAACgB,YAAY,EAAE,CAACT,IAAI,CACzCZ,SAAS,CAAC,MAAK;MACb,MAAMsB,UAAU,GAAGf,OAAO,CAACgB,KAAK,CAAC;QAC/B;QACAC,UAAU,EAAE;UACVC,aAAa,EAAExB,WAAW,CAACyB;;OAE9B,CAAC;MACF,OAAOlB,IAAI,CAACG,MAAM,CAACW,UAAU,CAAC;IAChC,CAAC,CAAC,EACFvB,UAAU,CAAE4B,MAAM,IAAI;MACpB,IAAI,CAACvB,YAAY,CAACgB,QAAQ,CAACO,MAAM,CAAC;MAClC,IAAI,CAACtB,WAAW,CAACc,MAAM,CAAC,IAAI,CAAC;MAC7B,OAAOrB,UAAU,CAAC,MAAM6B,MAAM,CAAC;IACjC,CAAC,CAAC,CACH;EACH;EAAC,QAAAC,CAAA,G;qBAtEU1B,oBAAoB,EAAA2B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAApBjC,oBAAoB;IAAAkC,OAAA,EAApBlC,oBAAoB,CAAAmC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}