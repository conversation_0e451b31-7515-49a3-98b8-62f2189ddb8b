import {
  <PERSON>ttp<PERSON><PERSON>,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AuthParserService implements HttpInterceptor {
  constructor() {}

  intercept(
    request: HttpRequest<any>,
    next: <PERSON>ttpHandler
  ): Observable<HttpEvent<any>> {
    request = request.clone({
      // withCredentials: true,
      setHeaders: {
        Authorization: environment.bearerToken,
      },
    });
    return next.handle(request);
  }
}
