import { ComponentType } from '@angular/cdk/portal';
import { computed, Injectable, signal } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Sort } from '@angular/material/sort';
import { Router } from '@angular/router';
import {
  BehaviorSubject,
  catchError,
  EMPTY,
  filter,
  finalize,
  forkJoin,
  Observable,
  of,
  switchMap,
  tap,
} from 'rxjs';
import {
  ProjectDialogData,
  ProjectDialogResult,
} from 'src/app/shared/model/dialog';
import {
  AccessType,
  ILockProject,
  LockResult,
  Project,
  ProjectDetails,
  ProjectFilterCriteria,
  ProjectWithPermission,
} from 'src/app/shared/model/project';
import { IUser } from 'src/app/shared/model/user';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { ProjectDialogComponent } from '../../components/project-dialog/project-dialog.component';
import { AccessService } from '../access/access.service';
import { ProjectApiService } from '../api/project-api.service';
import { AppService } from '../app.service';
import { LoaderService } from '../loader/loader.service';
import { SnackBarService } from '../snackbar/snack-bar.service';
import { TreeNodeService } from '../treeNode/tree-node.service';
import { UserService } from '../user/user.service';

import { HttpResponse } from '@angular/common/http';
import { TranslateService } from '@ngx-translate/core';
import { FolderDTO } from '../../../shared/model/class';
import { Diagram } from '../../../shared/model/diagram';
import { ErrorService } from '../errors/error.service';
@Injectable({
  providedIn: 'root',
})
export class ProjectService {
  /** The current project details as an observable */
  private _currentProjectSubject = new BehaviorSubject<ProjectDetails | null>(
    null
  );
  private projectSearchTerm = new BehaviorSubject<string | null>(null);
  private projectFilterCriteria = new BehaviorSubject<ProjectFilterCriteria>(
    {}
  );
  private _lockedProjectSubject = new BehaviorSubject<ILockProject | null>(
    null
  );

  /** List of filtered projects based on permissions and search criteria */
  filteredProjects = signal<ProjectWithPermission[]>([]);
  projects = signal<ProjectWithPermission[]>([]);

  // State management signals
  isLoading = signal<boolean>(false);
  totalProjectCount = signal<number>(0);
  currentPage = signal<number>(1);
  pageSize = signal<number>(10);
  currentSort = signal<Sort>({ active: 'lastModifiedDate', direction: 'desc' });

  // Computed signals for derived state
  sortedProjects = computed(() => {
    const sort = this.currentSort();
    if (!sort.active || !sort.direction) {
      return this.projects();
    }

    return [...this.projects()].sort((a, b) => {
      const isAsc = sort.direction === 'asc';
      const valueA = this.getSortingValue(a, sort.active);
      const valueB = this.getSortingValue(b, sort.active);

      if (valueA < valueB) {
        return isAsc ? -1 : 1;
      }
      if (valueA > valueB) {
        return isAsc ? 1 : -1;
      }
      return 0;
    });
  });

  /** Whether the current project is locked */
  private _isProjectLocked: boolean = false;

  constructor(
    private projectApiService: ProjectApiService,
    private _userService: UserService,
    private loaderService: LoaderService,
    private treeNodeService: TreeNodeService,
    private router: Router,
    private snackBarService: SnackBarService,
    private dialog: MatDialog,
    private diagramUtils: DiagramUtils,
    private appService: AppService,
    private accessService: AccessService,
    private errorService: ErrorService,
    private translateService: TranslateService
  ) {}

  /**
   * Gets the lock status of the active project.
   *
   * @returns {boolean} - Whether the project is locked.
   */
  get isProjectLocked(): boolean {
    return this._isProjectLocked;
  }

  /**
   * Sets the lock status of the active project.
   *
   * @param value - The lock status.
   */
  set isProjectLocked(value: boolean) {
    this._isProjectLocked = value;
  }

  /**
   * Updates the project search term and notifies subscribers.
   * @param searchTerm - The new search term for projects, or `null` to clear the search term.
   */
  setProjectSearchTerm(searchTerm: string | null): void {
    this.projectSearchTerm.next(searchTerm);
  }

  // Track the last filter criteria to prevent duplicate API calls
  private lastFilterCriteria: ProjectFilterCriteria = {};

  /**
   * Updates the project filter criteria and notifies subscribers.
   * Prevents duplicate API calls by checking if the criteria have changed.
   * @param criteria - An object containing the filter criteria, such as `type` or `productLine`.
   */
  setProjectFilterCriteria(criteria: ProjectFilterCriteria): void {
    // Create a clean copy of the criteria, removing undefined values
    const cleanCriteria: ProjectFilterCriteria = {};

    // Only include properties that have values
    if (criteria.type && criteria.type.length > 0) {
      cleanCriteria.type = criteria.type;
    }

    if (criteria.productLine && criteria.productLine.length > 0) {
      cleanCriteria.productLine = criteria.productLine;
    }

    // Only include isSelfProject if it's true
    if (criteria.isSelfProject === true) {
      cleanCriteria.isSelfProject = true;
    }

    // Check if the criteria have changed
    const currentCriteria = JSON.stringify(this.lastFilterCriteria);
    const newCriteria = JSON.stringify(cleanCriteria);

    if (currentCriteria !== newCriteria) {
      this.lastFilterCriteria = { ...cleanCriteria };
      this.projectFilterCriteria.next(cleanCriteria);

      // Reset to first page when filter criteria change
      this.currentPage.set(1);
    }
  }

  /**
   * Retrieves an observable for project filter criteria updates.
   * @returns An observable that emits the current filter criteria.
   */
  projectFilterCriteriaChanges(): Observable<ProjectFilterCriteria> {
    return this.projectFilterCriteria.asObservable();
  }

  getProjectFilterCriteria(): ProjectFilterCriteria {
    return this.projectFilterCriteria.getValue();
  }

  /**
   * Retrieves an observable for project search term updates.
   * @returns An observable that emits the current search term.
   */
  getProjectSearchTerm(): Observable<string | null> {
    return this.projectSearchTerm.asObservable();
  }

  /**
   * Sets the current project and updates its diagrams with the project ID.
   * This method updates the library details for the project using `treeNodeService`
   * and emits the updated project to subscribers.
   * @param project - The project details to set as the current project.
   */
  setCurrentProject(project: ProjectDetails): void {
    project.diagrams = project.diagrams.map((diagram) => {
      return { ...diagram, idProject: project.id! };
    });
    this.appService.lastModifiedDate.set(project.lastModifiedDate);
    this.treeNodeService.setLibraryDetails(project);
    this._currentProjectSubject.next(project);
  }

  /**
   * Subscribes to changes in the current project.
   * @returns An observable that emits updates to the current project or `null` if none is set.
   */
  currentProjectChanges(): Observable<ProjectDetails | null> {
    return this._currentProjectSubject.asObservable();
  }

  /**
   * Sets the lock status of a project and notifies subscribers.
   * @param lock - An object representing the project lock status, or `null` to clear the lock.
   */
  setLockedProject(lock: ILockProject | null): void {
    this._lockedProjectSubject.next(lock);
  }

  /**
   * Retrieves the current lock status of a project.
   * @returns The current lock status, or `null` if no lock is set.
   */
  getLockedProject(): ILockProject | null {
    return this._lockedProjectSubject.getValue();
  }

  /**
   * Creates a new project by invoking the API service.
   * Updates the projects signal with the new project.
   *
   * @param project - The details of the project to create.
   * @returns An observable that emits the created project.
   */
  createNewProject(project: Project): Observable<Project> {
    this.isLoading.set(true);
    return this.projectApiService.createProject(project).pipe(
      tap((createdProject) => {
        // Add the new project to the projects signal
        if (createdProject && createdProject.id) {
          // We need to cast it to ProjectWithPermission since the API returns Project
          const newProject = createdProject as unknown as ProjectWithPermission;

          // Update the projects signal
          const currentProjects = this.projects();
          this.projects.set([newProject, ...currentProjects]);

          // Update the filtered projects signal
          const currentFilteredProjects = this.filteredProjects();
          this.filteredProjects.set([newProject, ...currentFilteredProjects]);

          // Increment the total count
          this.totalProjectCount.set(this.totalProjectCount() + 1);
        }
      }),
      finalize(() => {
        this.isLoading.set(false);
      })
    );
  }

  /**
   * Updates an existing project.
   * Updates the projects signal with the updated project.
   *
   * @param project - The updated project data.
   * @returns {Observable<Project>} - The updated project.
   */
  updateProject(project: Project): Observable<Project> {
    this.isLoading.set(true);
    return this.projectApiService.updateProject(project).pipe(
      tap((updatedProject) => {
        if (updatedProject && updatedProject.id) {
          // We need to cast it to ProjectWithPermission since the API returns Project
          const projectWithPermission =
            updatedProject as unknown as ProjectWithPermission;

          // Update the projects signal
          const currentProjects = this.projects();
          const updatedProjects = currentProjects.map((p) =>
            p.id === projectWithPermission.id
              ? { ...p, ...projectWithPermission }
              : p
          );
          this.projects.set(updatedProjects);

          // Update the filtered projects signal
          const currentFilteredProjects = this.filteredProjects();
          const updatedFilteredProjects = currentFilteredProjects.map((p) =>
            p.id === projectWithPermission.id
              ? { ...p, ...projectWithPermission }
              : p
          );
          this.filteredProjects.set(updatedFilteredProjects);
        }
      }),
      finalize(() => {
        this.isLoading.set(false);
      })
    );
  }

  /**
   * Deletes a project by its ID.
   * Updates the projects signal by removing the deleted project.
   *
   * @param projectId - The ID of the project to delete.
   * @returns {Observable<void>} - An observable that completes when the project is deleted.
   */
  deleteProject(projectId: number): Observable<void> {
    this.isLoading.set(true);
    return this.projectApiService.deleteProject(projectId).pipe(
      tap(() => {
        // Remove the project from the projects signal
        const currentProjects = this.projects();
        const updatedProjects = currentProjects.filter(
          (p) => p.id !== projectId
        );
        this.projects.set(updatedProjects);

        // Remove the project from the filtered projects signal
        const currentFilteredProjects = this.filteredProjects();
        const updatedFilteredProjects = currentFilteredProjects.filter(
          (p) => p.id !== projectId
        );
        this.filteredProjects.set(updatedFilteredProjects);

        // Decrement the total count
        this.totalProjectCount.set(Math.max(0, this.totalProjectCount() - 1));
      }),
      finalize(() => {
        this.isLoading.set(false);
      })
    );
  }

  /**
   * Retrieves all projects with permissions for the logged-in user.
   * Updates the projects and filteredProjects signals with the results.
   *
   * @param {number} pageSize - Number of projects per page
   * @param {number} currentPage - Current page number
   * @param {string} [sortBy] - Field to sort by (name, description, type, productLine, lastModifiedDate)
   * @param {boolean} [sortDescending=true] - Whether to sort in descending order
   * @param {string} [searchTerm] - Term to search for in project name and description
   * @param {ProjectFilterCriteria} [filterCriteria] - Additional filter criteria
   * @param {boolean} [isForPagination=false] - Whether this request is for pagination (append results) or a fresh load
   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response
   */
  getProjects(
    pageSize: number,
    currentPage: number,
    sortBy?: string,
    sortDescending: boolean = true,
    searchTerm?: string,
    filterCriteria?: ProjectFilterCriteria,
    isForPagination: boolean = false
  ): Observable<HttpResponse<ProjectWithPermission[]>> {
    // Update state signals
    this.isLoading.set(true);
    this.pageSize.set(pageSize);
    this.currentPage.set(currentPage);

    if (sortBy) {
      this.currentSort.set({
        active: sortBy,
        direction: sortDescending ? 'desc' : 'asc',
      });
    }

    return this.projectApiService
      .getProjects(
        pageSize,
        currentPage,
        sortBy,
        sortDescending,
        searchTerm,
        filterCriteria
      )
      .pipe(
        tap((response) => {
          // Update total count from pagination header
          const paginationHeader = response.headers.get('x-pagination');
          if (paginationHeader) {
            const pagination = JSON.parse(paginationHeader);
            this.totalProjectCount.set(pagination.TotalRecords);
          }

          // Update projects signals
          if (Array.isArray(response.body)) {
            const validProjects = response.body.filter(
              (project) => project != null
            );

            if (isForPagination) {
              // For pagination, append to existing projects
              const currentProjects = this.projects();
              const newProjects = [...currentProjects];

              // Replace projects at the current page
              const startIndex = (currentPage - 1) * pageSize;
              for (let i = 0; i < validProjects.length; i++) {
                if (startIndex + i < newProjects.length) {
                  newProjects[startIndex + i] = validProjects[i];
                } else {
                  newProjects.push(validProjects[i]);
                }
              }

              this.projects.set(newProjects);
              this.filteredProjects.set([...newProjects]);
            } else {
              // For fresh load, replace all projects
              this.projects.set(validProjects);
              this.filteredProjects.set([...validProjects]);
            }
          }
        }),
        finalize(() => {
          this.isLoading.set(false);
        })
      );
  }

  /**
   * Retrieves a project by its ID.
   *
   * @param idProject - The ID of the project to retrieve.
   * @returns {Observable<ProjectDetails>} - The project details.
   */
  getProjectById(idProject: number): Observable<ProjectDetails> {
    return this.projectApiService.getProjectById(idProject);
  }

  /**
   * Filters projects based on the search term.
   * This is a client-side filter that operates on the existing projects.
   * For server-side filtering, use getProjects with a searchTerm.
   *
   * @param searchTerm - The search term for filtering projects.
   */
  filterProjects(searchTerm: string): void {
    if (searchTerm !== '') {
      const allProjects = this.projects();
      const filtered = allProjects.filter((project) => {
        const searchTermLower = searchTerm.toLowerCase();
        return (
          project.name?.toLowerCase().includes(searchTermLower) ||
          project.description?.toLowerCase().includes(searchTermLower) ||
          project.type?.toLowerCase().includes(searchTermLower) ||
          project.productLine?.toLowerCase().includes(searchTermLower) ||
          project.admin?.toLowerCase().includes(searchTermLower)
        );
      });
      this.filteredProjects.set(filtered);
    } else {
      // If search term is empty, show all projects
      this.filteredProjects.set(this.projects());
    }
  }

  /**
   * Applies sorting to the projects.
   * Updates the currentSort signal and sorts the projects accordingly.
   *
   * @param sort - The sort configuration to apply
   */
  applySorting(sort: Sort): void {
    this.currentSort.set(sort);
    // The sortedProjects computed signal will automatically update
  }

  /**
   * Loads the next page of projects.
   * Increments the current page and loads the projects for that page.
   *
   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response
   */
  loadNextPage(): Observable<HttpResponse<ProjectWithPermission[]>> {
    const nextPage = this.currentPage() + 1;
    return this.getProjects(
      this.pageSize(),
      nextPage,
      this.currentSort().active,
      this.currentSort().direction === 'desc',
      this.projectSearchTerm.getValue() || undefined,
      this.getProjectFilterCriteria(),
      true // isForPagination
    );
  }

  /**
   * Loads the previous page of projects.
   * Decrements the current page and loads the projects for that page.
   *
   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response
   */
  loadPreviousPage(): Observable<HttpResponse<ProjectWithPermission[]>> {
    const prevPage = Math.max(1, this.currentPage() - 1);
    return this.getProjects(
      this.pageSize(),
      prevPage,
      this.currentSort().active,
      this.currentSort().direction === 'desc',
      this.projectSearchTerm.getValue() || undefined,
      this.getProjectFilterCriteria(),
      true // isForPagination
    );
  }

  /**
   * Refreshes the current page of projects.
   * Reloads the projects for the current page with the current sort and filter settings.
   *
   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response
   */
  refreshProjects(): Observable<HttpResponse<ProjectWithPermission[]>> {
    // Set loading state
    this.isLoading.set(true);

    // Get current search term
    const searchTerm = this.projectSearchTerm.getValue() || undefined;

    return this.getProjects(
      this.pageSize(),
      this.currentPage(),
      this.currentSort().active,
      this.currentSort().direction === 'desc',
      searchTerm,
      this.getProjectFilterCriteria(),
      false // Not for pagination, fresh load
    );
  }

  /**
   * Locks a project.
   *
   * @param lockObj - The lock object to lock the project with.
   */
  lockProject(lockObj: ILockProject): void {
    this.projectApiService.lockProject(lockObj).subscribe({
      next: (lockedProject) => {
        this.setLockedProject(lockedProject);
        this._isProjectLocked = true;
        if (lockObj.idContact != lockedProject.idContact)
          this.translateService
            .get(
              ['errors.lockedProject.header', 'errors.lockedProject.content'],
              { user: lockedProject.lockedUserName }
            )
            .subscribe((translations) => {
              this.errorService.addError({
                errorKey: 1204,
                type: 'warn',
                header: translations['errors.lockedProject.header'],
                content: `${translations['errors.lockedProject.content']} ${lockedProject.lockedUserName}`,
                isCustomError: true,
              });
            });
      },
      error: () => {
        this.setLockedProject(null);
      },
    });
  }

  /**
   * Unlocks a project by its ID.
   *
   * @param idProject - The ID of the project to unlock.
   */
  unlockProject(idProject: number): void {
    this.projectApiService.unlockProject(idProject).subscribe(() => {
      this._isProjectLocked = false;
    });
  }

  /**
   * Locks the project on load if the user has access and the project is not locked.
   *
   * @param idProject - The ID of the project to lock.
   * @param idUser - The ID of the user requesting the lock.
   */
  lockProjectOnLoad(idProject: number, idUser?: number): void {
    if (idUser) {
      this.lockProject({
        idProject: idProject,
        idContact: idUser,
      });
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  /**
   * Checks if a project is locked.
   *
   * @param idProject - The ID of the project to check.
   * @returns {Observable<LockResult>} - The result of the lock check.
   */
  checkProjectLocked(idProject: number): Observable<LockResult> {
    return this.projectApiService.isProjectLocked(idProject);
  }

  /**
   * Opens a project and navigates to the editor view.
   *
   * @param idProject - The ID of the project to open.
   * @param isLock - Whether to lock the project.
   * @param initialDiagramId - Optional ID of a specific diagram to open.
   */
  openProject(
    idProject: number,
    isLock: boolean = false,
    initialDiagramId?: number
  ): void {
    this.loaderService.showLoader();
    let project$ = this.getProjectById(idProject); // Always fetch project

    // First get the project to check access type
    project$
      .pipe(
        switchMap((project) => {
          // Always check lock status regardless of user access level
          const lockStatus$ = this.checkProjectLocked(idProject);

          // Only attempt to lock if user has edit access (not a Viewer) and isLock parameter is true
          const shouldLock = isLock && project.accessType !== AccessType.Viewer;

          return forkJoin({
            project: of(project),
            lockStatus: lockStatus$,
            shouldLock: of(shouldLock),
          });
        }),
        finalize(() => this.loaderService.hideLoader()),
        catchError((error) => {
          this.handleError(error);
          return EMPTY;
        })
      )
      .subscribe({
        next: ({ project, lockStatus, shouldLock }) => {
          // Set the access type first
          this.accessService.setProjectAccess(project.accessType);

          const loggedInUser = this._userService.getUser();
          this.saveUserData(loggedInUser);

          // Always handle project access based on lock status
          if (lockStatus) {
            this.handleProjectAccess(lockStatus, project, loggedInUser);
          }

          // Only attempt to lock if user has edit access and we should lock
          if (shouldLock && lockStatus) {
            this.lockProjectOnLoad(idProject, loggedInUser?.id);
          }

          // Always navigate to the route with both project ID and diagram ID
          // If no specific diagram ID is provided, use the first diagram in the project
          let diagramId = initialDiagramId;

          if (!diagramId) {
            // First try to get from main diagrams
            if (project.diagrams.length > 0) {
              diagramId = project.diagrams[0].id;
            } else if (project.folders) {
              // If no diagrams in main array, try to find first diagram in folders
              const firstFolderDiagram = this.getFirstDiagramFromFolders(
                project.folders
              );
              diagramId = firstFolderDiagram?.id;
            }
          }
          if (diagramId) {
            // Find and set the target diagram as active
            let targetDiagram: Diagram | null =
              project.diagrams.find((d) => d.id === diagramId) ?? null;

            // If not found in main diagrams, search in folders
            if (!targetDiagram && project.folders) {
              targetDiagram = this.findDiagramInFolders(
                project.folders,
                diagramId
              );
            }

            if (targetDiagram) {
              // We'll set the active diagram after setting the current project
              setTimeout(() => {
                this.router.navigate([
                  `/editor/${project.id}/diagram/${diagramId}`,
                ]);
                this.diagramUtils.setActiveDiagram(targetDiagram);
              }, 0);
            }
          } else {
            // If there are no diagrams in the project, clear the active diagram
            // and navigate to a placeholder URL
            this.router.navigate([`/editor/${project.id}/diagram/0`]);
            this.diagramUtils.setActiveDiagram(null);
          }

          this.setCurrentProject(project);
        },
      });
  }

  private handleProjectAccess(
    lockStatus: LockResult,
    project: ProjectDetails,
    loggedInUser: IUser | null
  ): void {
    if (!lockStatus.isProjectLocked) {
      this.accessService.setProjectAccess(project.accessType);
      return;
    }

    // For users with edit access (Editor or Admin), check if they are the ones locking the project
    const isCurrentUserLocking =
      loggedInUser &&
      lockStatus.lockingIdContact !== 0 &&
      loggedInUser.id !== lockStatus.lockingIdContact;

    // If the project is locked by another user, downgrade to Viewer and show a dialog
    if (isCurrentUserLocking) {
      this.accessService.setProjectAccess(AccessType.Viewer);

      // Show dialog message about the project being locked by another user
      this.translateService
        .get(['errors.lockedProject.header', 'errors.lockedProject.content'], {
          user: lockStatus.lockedUserName,
        })
        .subscribe((translations) => {
          this.errorService.addError({
            errorKey: 1204,
            type: 'warn',
            header: translations['errors.lockedProject.header'],
            content: `${translations['errors.lockedProject.content']} ${lockStatus.lockedUserName}`,
            isCustomError: true,
          });
        });
    } else {
      // Otherwise, keep their original access level
      this.accessService.setProjectAccess(project.accessType);
    }
  }

  private saveUserData(user: IUser | null): void {
    if (!localStorage.getItem('userData') && user) {
      localStorage.setItem('userData', JSON.stringify(user));
    }
  }

  private handleError(error: any): void {
    console.error('Error opening project:', error);
  }

  /**
   * Gets the value to use for sorting based on the property name.
   * @param project - The project to get the value from
   * @param property - The property name to get the value for
   * @returns The value to use for sorting
   */
  private getSortingValue(
    project: ProjectWithPermission,
    property: string
  ): any {
    switch (property) {
      case 'name':
      case 'description':
      case 'type':
      case 'productLine':
      case 'admin':
        return project[property]?.toLowerCase() || '';
      case 'lastModifiedDate':
        return new Date(project.lastModifiedDate || 0);
      default:
        return project[property as keyof ProjectWithPermission];
    }
  }

  /**
   * Handles unlocking a project if the logged-in user has access to unlock it.
   *
   * @param idProject - The ID of the project to unlock.
   */
  handleProjectUnlock(idProject: number): void {
    const lockedProject = this.getLockedProject();
    const loggedInUser = this._userService.getUser();
    if (
      lockedProject &&
      loggedInUser &&
      lockedProject.idContact == loggedInUser.id
    ) {
      this.unlockProject(idProject);
    }
  }
  exportProject(idProject: number, formData: FormData): Observable<File> {
    return this.projectApiService.exportProjectDetails(idProject, formData);
  }
  exportDiagramImages(formData: FormData): Observable<File> {
    return this.projectApiService.exportImages(formData);
  }

  openProjectDialog(project?: ProjectWithPermission): void {
    const dialogData = this.prepareProjectDialogData(project);
    const dialogRef = this.openDialog<
      ProjectDialogComponent,
      ProjectDialogData,
      ProjectDialogResult
    >(ProjectDialogComponent, dialogData);

    this.handleProjectDialogResult(dialogRef, project);
  }
  /**
   * Prepare dialog data for project creation/editing
   */
  private prepareProjectDialogData(
    project?: ProjectWithPermission
  ): ProjectDialogData {
    const isEditableProject =
      project && project.accessType !== AccessType.Viewer;
    return {
      title: isEditableProject
        ? 'dashboard.editProject'
        : 'dashboard.newProject',
      btnText: isEditableProject ? 'dialog.save' : 'dialog.create',
      ...(isEditableProject && { project }),
    };
  }
  /**
   * Open a generic dialog with specified configuration
   */
  private openDialog<T, D, R>(
    component: ComponentType<T>,
    data: D,
    options: any = {}
  ): MatDialogRef<T, R> {
    return this.dialog.open<T, D, R>(component, {
      autoFocus: false,
      ...options,
      data,
    });
  }
  /**
   * Handle project dialog result
   */
  private handleProjectDialogResult(
    dialogRef: MatDialogRef<ProjectDialogComponent, ProjectDialogResult>,
    originalProject?: ProjectWithPermission
  ): void {
    dialogRef
      .afterClosed()
      .pipe(
        filter(
          (result): result is ProjectDialogResult =>
            result !== null && result !== undefined
        ),
        switchMap((result) =>
          this.processProjectDialogResult(result, originalProject)
        )
        // takeUntil(this.destroy$)
      )
      .subscribe();
  }
  /**
   * Process project dialog result
   */
  private processProjectDialogResult(
    dialogResult: ProjectDialogResult,
    originalProject?: ProjectWithPermission
  ): Observable<Project | null> {
    this.diagramUtils.clearEnumTypeOptions();

    const projectServiceCall = dialogResult.isCreation
      ? this.createNewProject(dialogResult.project)
      : this.updateProject(dialogResult.project);

    return projectServiceCall.pipe(
      tap((resultProject) => {
        if (resultProject && resultProject.id) {
          this.handleProjectServiceSuccess(
            resultProject,
            dialogResult,
            originalProject
          );
        }
      })
    );
  }
  /**
   * Handle successful project service operation
   */
  private handleProjectServiceSuccess(
    resultProject: Project,
    dialogResult: ProjectDialogResult,
    originalProject?: ProjectWithPermission
  ): void {
    if (dialogResult.isCreation) {
      // When creating a new project, open it with the first diagram (if any)
      this.openProject(resultProject.id!, true);
    } else if (originalProject) {
      const updatedProject: ProjectWithPermission = {
        ...originalProject,
        ...dialogResult.project,
      };
      this.updateProjectInList(updatedProject);
    }

    this.snackBarService.openSnackbar(
      dialogResult.isCreation
        ? 'snackBar.projectCreationMsg'
        : 'snackBar.projectUpdatedMsg'
    );
  }

  /**
   * Recursively searches for a diagram in project folders by ID
   * @param folders - Array of folders to search in
   * @param diagramId - ID of the diagram to find
   * @returns The found diagram or null if not found
   */
  private findDiagramInFolders(
    folders: FolderDTO[],
    diagramId: number
  ): Diagram | null {
    for (const folder of folders) {
      // Search in current folder's diagrams
      if (folder.diagrams) {
        const foundDiagram = folder.diagrams.find((d) => d.id === diagramId);
        if (foundDiagram) {
          return foundDiagram;
        }
      }

      // Recursively search in child folders
      if (folder.childFolders && folder.childFolders.length > 0) {
        const foundInChild = this.findDiagramInFolders(
          folder.childFolders,
          diagramId
        );
        if (foundInChild) {
          return foundInChild;
        }
      }
    }
    return null;
  }

  /**
   * Gets the first diagram found in project folders (recursively)
   * @param folders - Array of folders to search in
   * @returns The first diagram found or null if none found
   */
  private getFirstDiagramFromFolders(folders: FolderDTO[]): Diagram | null {
    for (const folder of folders) {
      // Check current folder's diagrams first
      if (folder.diagrams && folder.diagrams.length > 0) {
        return folder.diagrams[0];
      }

      // Recursively search in child folders
      if (folder.childFolders && folder.childFolders.length > 0) {
        const foundInChild = this.getFirstDiagramFromFolders(
          folder.childFolders
        );
        if (foundInChild) {
          return foundInChild;
        }
      }
    }
    return null;
  }
  private updateProjectInList(updatedProject: ProjectWithPermission): void {
    const projectIndex = this.projects().findIndex(
      (p) => p.id === updatedProject.id
    );
    const filteredProjectIndex = this.filteredProjects().findIndex(
      (p) => p.id === updatedProject.id
    );
    if (projectIndex !== -1 && filteredProjectIndex !== -1) {
      this.projects()[projectIndex] = updatedProject;
      this.filteredProjects()[filteredProjectIndex] = updatedProject;
      this.setProjectFilterCriteria(this.getProjectFilterCriteria());
    }
  }
}
