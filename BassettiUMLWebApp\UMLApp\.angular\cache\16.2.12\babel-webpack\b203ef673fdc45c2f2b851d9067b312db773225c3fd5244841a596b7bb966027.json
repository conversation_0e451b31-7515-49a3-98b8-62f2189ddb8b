{"ast": null, "code": "import { effect } from '@angular/core';\nimport * as go from 'gojs';\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\nimport { LibraryTreeComponent } from 'src/app/core/components/library-tree/library-tree.component';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/diagram/diagram.service\";\nimport * as i2 from \"src/app/core/services/project/project.service\";\nimport * as i3 from \"src/app/core/services/property/property.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"src/app/core/services/cardinality/cardinality.service\";\nimport * as i6 from \"src/app/core/services/access/access.service\";\nimport * as i7 from \"src/app/core/services/gojs/gojs.service\";\nimport * as i8 from \"src/app/shared/utils/diagram-utils\";\nimport * as i9 from \"src/app/core/services/loader/loader.service\";\nimport * as i10 from \"src/app/core/services/treeNode/tree-node.service\";\nimport * as i11 from \"src/app/core/services/navbar/navbar.service\";\nimport * as i12 from \"@angular/material/dialog\";\nimport * as i13 from \"src/app/core/services/app.service\";\nimport * as i14 from \"src/app/core/services/versionHistory/version-history.service\";\nimport * as i15 from \"@angular/common\";\nimport * as i16 from \"@angular/material/button\";\nimport * as i17 from \"@angular/material/sidenav\";\nimport * as i18 from \"@angular/material/icon\";\nimport * as i19 from \"@angular/material/expansion\";\nimport * as i20 from \"../../../components/properties/properties.component\";\nimport * as i21 from \"../../../components/action-controls/action-controls.component\";\nimport * as i22 from \"../../../components/library-tree/library-tree.component\";\nimport * as i23 from \"../../../components/version-history/version-history.component\";\nimport * as i24 from \"@ngx-translate/core\";\nfunction DiagramEditorComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"app-properties\", 17);\n    i0.ɵɵlistener(\"onChangePropertyData\", function DiagramEditorComponent_div_19_Template_app_properties_onChangePropertyData_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onUpdateProperties($event, ctx_r2.libraryTreeComponent.selectedTreeNode));\n    })(\"isColorPickerSelected\", function DiagramEditorComponent_div_19_Template_app_properties_isColorPickerSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onColorPickerSelection($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"nodeData\", ctx_r0.propertyData)(\"editAccess\", ctx_r0.hasEditAccessOnly)(\"dataTypes\", ctx_r0.attributeTypes)(\"idDiagram\", ctx_r0.selectedDiagramId);\n  }\n}\nfunction DiagramEditorComponent_mat_drawer_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-drawer-container\", 18)(1, \"mat-drawer\", 19);\n    i0.ɵɵelement(2, \"app-version-history\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"content-margin\": a0\n  };\n};\nexport class DiagramEditorComponent {\n  constructor(diagramService, projectService, propertyService, route, router, linkService, accessService, goJsService, diagramUtils, loaderService, treeNodeService, navbarService, dialog, appService, versionHistoryService) {\n    this.diagramService = diagramService;\n    this.projectService = projectService;\n    this.propertyService = propertyService;\n    this.route = route;\n    this.router = router;\n    this.linkService = linkService;\n    this.accessService = accessService;\n    this.goJsService = goJsService;\n    this.diagramUtils = diagramUtils;\n    this.loaderService = loaderService;\n    this.treeNodeService = treeNodeService;\n    this.navbarService = navbarService;\n    this.dialog = dialog;\n    this.appService = appService;\n    this.versionHistoryService = versionHistoryService;\n    this.$ = go.GraphObject.make;\n    this.isComponentPanelExpanded = false;\n    this.isLibraryPanelExpanded = true;\n    this.diagrams = [];\n    this.selectedDiagramId = -1;\n    this.hasEditAccessOnly = false;\n    this.attributeTypes = [];\n    this._downloadSub = null;\n    this._downloadAllSub = null;\n    this._currentProjectSub = null;\n    this._colorSelectionSub = null;\n    this._diagramSub = null;\n    this._currentProjDiagramsSub = null;\n    this._deleteDiagramSub = null;\n    this._propertyDataSub = null;\n    this._attributeTypeSub = null;\n    this.showVersionHistory = false;\n    this.isLoading$ = this.loaderService.isLoading$;\n    this._diagramLayoutSub = null;\n    this.selectedNodeTag = TreeNodeTag.Project;\n    effect(() => {\n      this.showVersionHistory = this.navbarService.showVersionHistory();\n    });\n  }\n  ngOnInit() {\n    this.appService.setIsInitProject(true);\n    go.Diagram.licenseKey = environment.licenseKey;\n    this.projectId = parseInt(this.route.snapshot.paramMap.get('id') ?? '');\n    // Check if a specific diagram ID is provided in the route\n    const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');\n    const initialDiagramId = diagramIdParam ? parseInt(diagramIdParam) : undefined;\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${initialDiagramId}`;\n    if (localStorage.getItem('reloaded') || localStorage.getItem('copyUrl')) {\n      // Open the project with the specific diagram if provided\n      this.projectService.openProject(this.projectId, true, initialDiagramId);\n      this.linkService.initLinkTypes();\n      localStorage.removeItem('reloaded');\n      localStorage.removeItem('copyUrl');\n    }\n    this.configureDiagramProperties();\n    this.initProject();\n    this.accessService.accessTypeChanges().subscribe(response => {\n      this.hasEditAccessOnly = response != AccessType.Viewer;\n    });\n    this.subscribeToDiagramService();\n    this._attributeTypeSub = this.diagramUtils.getAttributeTypes().subscribe(options => {\n      this.attributeTypes = options;\n    });\n  }\n  unloadHandler(event) {\n    event.preventDefault();\n    this.projectService.handleProjectUnlock(this.projectId);\n    localStorage.setItem('reloaded', 'true');\n  }\n  /**\n   * Configures properties for the main diagram.\n   * @memberof DiagramEditorComponent\n   */\n  configureDiagramProperties() {\n    this.diagram = this.$(go.Diagram, 'diagramDiv', {\n      initialAutoScale: go.AutoScale.Uniform,\n      allowCopy: false,\n      'animationManager.isEnabled': false,\n      'linkingTool.isEnabled': true,\n      'draggingTool.isGridSnapEnabled': true,\n      'linkingTool.isUnconnectedLinkValid': false,\n      'linkingTool.portGravity': 20,\n      'relinkingTool.isUnconnectedLinkValid': false,\n      'relinkingTool.portGravity': 10,\n      'linkReshapingTool.handleArchetype': this.$(go.Shape, 'Diamond', {\n        desiredSize: new go.Size(7, 7),\n        fill: 'lightblue',\n        stroke: '#0069B4'\n      }),\n      mouseDrop: e => this.goJsService.handleDropCompletion(e, null),\n      'commandHandler.archetypeGroupData': {\n        isGroup: true,\n        text: 'Group',\n        horiz: false\n      },\n      'undoManager.isEnabled': true,\n      'relinkingTool.isEnabled': true\n    });\n    this.diagram.grid = new go.Panel('Grid', {\n      gridCellSize: new go.Size(10, 10)\n    }).add(new go.Shape('LineH', {\n      stroke: 'lightgray',\n      strokeWidth: 0.5,\n      interval: 1\n    }), new go.Shape('LineH', {\n      stroke: 'gray',\n      strokeWidth: 0.5,\n      interval: 5\n    }), new go.Shape('LineH', {\n      stroke: 'gray',\n      strokeWidth: 1.0,\n      interval: 10\n    }), new go.Shape('LineV', {\n      stroke: 'lightgray',\n      strokeWidth: 0.5,\n      interval: 1\n    }), new go.Shape('LineV', {\n      stroke: 'gray',\n      strokeWidth: 0.5,\n      interval: 5\n    }), new go.Shape('LineV', {\n      stroke: 'gray',\n      strokeWidth: 1.0,\n      interval: 10\n    }));\n  }\n  initProject() {\n    this._diagramSub = this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) {\n        console.log(diagram);\n        this.currentDiagram = diagram;\n        this.selectedDiagramId = this.currentDiagram.id;\n        this.diagramService.getDiagramDetails(diagram.id);\n      }\n    });\n    this._currentProjDiagramsSub = this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (diagrams.length > 0) {\n        this.diagrams = diagrams;\n        // If no active diagram is set yet, set the first diagram as active\n        // This handles the case when no specific diagram ID is provided in the URL\n        if (!this.currentDiagram && this.diagrams.length > 0) {\n          // Check if a diagram ID is specified in the route\n          const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');\n          if (diagramIdParam) {\n            // If a diagram ID is specified, find and set that diagram as active\n            const diagramId = parseInt(diagramIdParam);\n            const targetDiagram = this.diagrams.find(d => d.id === diagramId);\n            if (targetDiagram) {\n              debugger;\n              this.diagramUtils.setActiveDiagram(targetDiagram);\n            } else {\n              debugger;\n              // If the specified diagram doesn't exist, fall back to the first diagram\n              this.diagramUtils.setActiveDiagram(this.diagrams[0]);\n            }\n          } else {\n            debugger;\n            // If no diagram ID is specified, set the first diagram as active\n            this.diagramUtils.setActiveDiagram(this.diagrams[0]);\n          }\n        }\n      }\n    });\n    this._currentProjectSub = this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) {\n        this.linkService.setProjectLinks(project);\n        this.goJsService.initDiagram(this.diagram);\n        this.diagramService.getPaletteDiagramDetails();\n      }\n    });\n  }\n  onDiagramSelectionChange(diagramId) {\n    this.selectedDiagramId = diagramId;\n    const activeDiagram = this.diagrams.find(d => d.id === diagramId);\n    debugger;\n    this.diagramUtils.setActiveDiagram(activeDiagram);\n    this.propertyService.setPropertyData(null);\n    // Update the URL to include the selected diagram ID without reloading the page\n    this.router.navigate([`/editor/${this.projectId}/diagram/${diagramId}`], {\n      replaceUrl: true // Replace the current URL instead of adding a new history entry\n    });\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${diagramId}`;\n  }\n  /**\n   * Subscribes to various diagram service subjects to handle diagram actions.\n   */\n  subscribeToDiagramService() {\n    this._deleteDiagramSub = this.diagramService.deleteDiagramEvent.subscribe(() => {\n      this.onDeleteDiagram();\n    });\n    this._downloadSub = this.diagramService.downloadDiagramEvent.subscribe(() => {\n      this.diagramService.initiateDiagramDownload(true, false);\n    });\n    this._downloadAllSub = this.diagramService.downloadAllDiagramEvent.subscribe(isForOnlyImage => {\n      this.diagramService.initiateDiagramDownload(false, isForOnlyImage);\n      this.onDiagramSelectionChange(this.currentDiagram.id);\n    });\n    this._propertyDataSub = this.propertyService.propertyDataChanges().subscribe(propertyData => {\n      if (propertyData) this.propertyData = propertyData;\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      if (this.diagram) this.diagram.allowDrop = diagrams.length > 0;\n    });\n  }\n  onDeleteDiagram() {\n    const dialogRef = this.dialog.open(DialogConfirmationComponent, {\n      width: '320px',\n      data: {\n        title: 'dialog.title',\n        reject: 'dialog.no',\n        confirm: 'dialog.yes'\n      }\n    });\n    dialogRef.afterClosed().subscribe(isConfirm => {\n      if (isConfirm) {\n        if (this.selectedDiagramId) {\n          this.diagramService.deleteDiagram(this.selectedDiagramId);\n          this.treeNodeService.deleteDiagram(`atTag${GojsNodeCategory.Diagram}_${this.selectedDiagramId}`);\n          this.diagrams = this.diagrams.filter(diagram => diagram.id !== this.selectedDiagramId);\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n          if (this.diagrams.length > 0) {\n            this.currentDiagram = this.diagrams[0];\n            this.selectedDiagramId = this.currentDiagram.id;\n            debugger;\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\n            // Update the URL to reflect the new active diagram\n            this.router.navigate([`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`], {\n              replaceUrl: true\n            });\n          } else {\n            debugger;\n            this.diagramUtils.setActiveDiagram(null);\n            // If there are no diagrams left, navigate to a placeholder URL\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\n              replaceUrl: true\n            });\n          }\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.linkService.clearLinks();\n    this.linkService.clearLinkToLinks();\n    this.versionHistoryService.setSelectedVersion(null);\n    this.projectService.handleProjectUnlock(this.projectId);\n    if (this._downloadSub) this._downloadSub.unsubscribe();\n    if (this._downloadAllSub) this._downloadAllSub.unsubscribe();\n    if (this._colorSelectionSub) this._colorSelectionSub.unsubscribe();\n    if (this._diagramSub) this._diagramSub.unsubscribe();\n    if (this._currentProjDiagramsSub) this._currentProjDiagramsSub.unsubscribe();\n    if (this._deleteDiagramSub) this._deleteDiagramSub.unsubscribe();\n    if (this._propertyDataSub) this._propertyDataSub.unsubscribe();\n    if (this._currentProjectSub) this._currentProjectSub.unsubscribe();\n    if (this._attributeTypeSub) this._attributeTypeSub.unsubscribe();\n    if (this._diagramLayoutSub) this._diagramLayoutSub.unsubscribe();\n    this.propertyService.setPropertyData(null);\n  }\n  /**\n   * Subscribes to color selection changes and updates the component state accordingly.\n   */\n  onColorPickerSelection(isClicked) {\n    if (isClicked) {\n      this.isComponentPanelExpanded = false;\n      this.isLibraryPanelExpanded = false;\n    }\n  }\n  /**\n   * Subscribes to property data updates and handles updates to the diagram and palette.\n   */\n  onUpdateProperties(updatedNode, treeNode) {\n    this.diagramService.getUpdatedProperties(updatedNode, treeNode);\n  }\n  onCreateFolder(event) {\n    event.stopPropagation();\n    this.goJsService.createNewFolder('New Folder', this.projectId);\n    if (this.libraryTreeComponent.dataSource && this.libraryTreeComponent.dataSource.data.length > 0) {\n      this.libraryTreeComponent.treeControl.expand(this.libraryTreeComponent.dataSource.data[0]);\n    }\n  }\n  // Function to open version history\n  openVersionHistory() {\n    this.navbarService.setShowVersionHistory(true);\n    this.accessService.setProjectAccess(AccessType.Viewer);\n  }\n  // Function to close version history\n  closeVersionHistory() {\n    this.navbarService.setShowVersionHistory(false);\n    // this.accessService.setProjectAccess(this.project.accessType);\n  }\n  static #_ = this.ɵfac = function DiagramEditorComponent_Factory(t) {\n    return new (t || DiagramEditorComponent)(i0.ɵɵdirectiveInject(i1.DiagramService), i0.ɵɵdirectiveInject(i2.ProjectService), i0.ɵɵdirectiveInject(i3.PropertyService), i0.ɵɵdirectiveInject(i4.ActivatedRoute), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.CardinalityService), i0.ɵɵdirectiveInject(i6.AccessService), i0.ɵɵdirectiveInject(i7.GojsService), i0.ɵɵdirectiveInject(i8.DiagramUtils), i0.ɵɵdirectiveInject(i9.LoaderService), i0.ɵɵdirectiveInject(i10.TreeNodeService), i0.ɵɵdirectiveInject(i11.NavbarService), i0.ɵɵdirectiveInject(i12.MatDialog), i0.ɵɵdirectiveInject(i13.AppService), i0.ɵɵdirectiveInject(i14.VersionHistoryService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DiagramEditorComponent,\n    selectors: [[\"app-dashboard\"]],\n    viewQuery: function DiagramEditorComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(LibraryTreeComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.libraryTreeComponent = _t.first);\n      }\n    },\n    hostBindings: function DiagramEditorComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"beforeunload\", function DiagramEditorComponent_beforeunload_HostBindingHandler($event) {\n          return ctx.unloadHandler($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 24,\n    vars: 21,\n    consts: [[1, \"project-container\"], [\"mode\", \"side\", \"opened\", \"\", 1, \"side-container\", 3, \"disableClose\"], [\"multi\", \"true\"], [\"expanded\", \"true\", \"disabled\", \"true\", 1, \"expansion-panel\", \"library-panel\", 3, \"hideToggle\"], [1, \"disable_ripple\"], [1, \"panel-title\"], [\"mat-icon-button\", \"\", \"matRippleDisabled\", \"\", 3, \"disabled\", \"click\"], [1, \"tree-container\"], [3, \"expandNodeTag\"], [\"expanded\", \"true\", \"disabled\", \"true\", 1, \"expansion-panel\", 3, \"hideToggle\"], [\"id\", \"classDiagram\", 1, \"disable_ripple\", \"component-panel\"], [\"class\", \"property\", 4, \"ngIf\"], [3, \"ngClass\"], [\"id\", \"diagramDiv\", 1, \"main-container\"], [3, \"diagram\"], [\"class\", \"version-history-container\", 4, \"ngIf\"], [1, \"property\"], [2, \"margin-top\", \"20px\", \"padding-top\", \"20px\", 3, \"nodeData\", \"editAccess\", \"dataTypes\", \"idDiagram\", \"onChangePropertyData\", \"isColorPickerSelected\"], [1, \"version-history-container\"], [\"mode\", \"over\", \"opened\", \"\", 1, \"version-history-drawer\"]],\n    template: function DiagramEditorComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"mat-drawer-container\", 0)(1, \"mat-drawer\", 1)(2, \"mat-accordion\", 2)(3, \"mat-expansion-panel\", 3)(4, \"mat-expansion-panel-header\", 4)(5, \"mat-panel-title\", 5);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function DiagramEditorComponent_Template_button_click_8_listener($event) {\n          return ctx.onCreateFolder($event);\n        });\n        i0.ɵɵelementStart(9, \"mat-icon\");\n        i0.ɵɵtext(10, \"create_new_folder\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"div\", 7);\n        i0.ɵɵelement(12, \"app-library-tree\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"mat-expansion-panel\", 9)(14, \"mat-expansion-panel-header\", 4)(15, \"mat-panel-title\", 5);\n        i0.ɵɵtext(16);\n        i0.ɵɵpipe(17, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(18, \"div\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(19, DiagramEditorComponent_div_19_Template, 2, 4, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"mat-drawer-content\", 12);\n        i0.ɵɵelement(21, \"div\", 13)(22, \"app-action-controls\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(23, DiagramEditorComponent_mat_drawer_container_23_Template, 3, 0, \"mat-drawer-container\", 15);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disableClose\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"hideToggle\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 15, \"diagram.library\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", !ctx.hasEditAccessOnly);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"expandNodeTag\", ctx.selectedNodeTag);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleProp(\"visibility\", ctx.showVersionHistory ? \"hidden\" : \"visible\")(\"height\", ctx.showVersionHistory ? \"0\" : \"auto\");\n        i0.ɵɵproperty(\"hideToggle\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(17, 17, \"diagram.components\"), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.hasEditAccessOnly);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c0, ctx.showVersionHistory));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"diagram\", ctx.diagram);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.showVersionHistory);\n      }\n    },\n    dependencies: [i15.NgClass, i15.NgIf, i16.MatIconButton, i17.MatDrawer, i17.MatDrawerContainer, i17.MatDrawerContent, i18.MatIcon, i19.MatAccordion, i19.MatExpansionPanel, i19.MatExpansionPanelHeader, i19.MatExpansionPanelTitle, i20.PropertiesComponent, i21.ActionControlsComponent, i22.LibraryTreeComponent, i23.VersionHistoryComponent, i24.TranslatePipe],\n    styles: [\".header-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: auto auto;\\n  justify-content: space-between;\\n}\\n@media (max-width: 462px) {\\n  .header-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: auto auto;\\n    justify-content: space-between;\\n    font-size: small;\\n  }\\n}\\n@media (max-width: 360px) {\\n  .header-grid[_ngcontent-%COMP%] {\\n    font-size: 8px;\\n    gap: 17px;\\n    margin: 6px;\\n  }\\n}\\n.header-grid[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.header-grid[_ngcontent-%COMP%]   .btn-dash[_ngcontent-%COMP%] {\\n  margin-top: 2.5rem;\\n  margin-left: 0.5rem;\\n  padding: 0.8rem;\\n  font-size: medium;\\n  border-radius: 4px;\\n  color: black;\\n  cursor: pointer;\\n  font-family: Roboto, \\\"Helvetica Neue\\\", sans-serif;\\n}\\n@media (max-width: 462px) {\\n  .header-grid[_ngcontent-%COMP%]   .btn-dash[_ngcontent-%COMP%] {\\n    margin-top: 1.2rem;\\n  }\\n}\\n\\n.project-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  overflow: hidden;\\n  height: calc(100vh - var(--toolbar-height));\\n  overflow: hidden;\\n  border: 0.5px solid grey;\\n  position: relative;\\n  top: calc(var(--toolbar-height) - 1px);\\n}\\n\\n.side-container[_ngcontent-%COMP%] {\\n  width: 15vw;\\n  max-width: 15vw;\\n  background-color: #f5f4f4;\\n  overflow: hidden;\\n  margin: 0;\\n}\\n\\n.main-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background-color: white;\\n  overflow: hidden;\\n}\\n\\n.disable_ripple[_ngcontent-%COMP%] {\\n  background-color: #f5f4f4;\\n  padding: 0 !important;\\n  height: 2.3rem;\\n}\\n\\n.mat-expansion-panel-body[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n.expansion-panel[_ngcontent-%COMP%] {\\n  padding: 0.4rem;\\n  background: transparent;\\n}\\n.expansion-panel[_ngcontent-%COMP%]     .mat-expansion-indicator {\\n  margin: 0 0.7rem;\\n}\\n\\n#LibraryDiagram[_ngcontent-%COMP%] {\\n  height: 27vh;\\n}\\n\\n.component-panel[_ngcontent-%COMP%] {\\n  height: 14vh;\\n  z-index: 0;\\n  position: relative;\\n}\\n\\n.property[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 20px;\\n  margin-bottom: 1rem;\\n  flex: 0 0 auto;\\n}\\n\\n.panel-title[_ngcontent-%COMP%] {\\n  padding: 0.2rem;\\n}\\n\\n.palette_panel[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 30vh;\\n  padding: 0;\\n}\\n\\n.mat-expansion-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n}\\n\\n.menu[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  width: 90px;\\n  opacity: 0;\\n  margin: 0;\\n  padding: 8px 0;\\n  z-index: 999;\\n  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);\\n  list-style: none;\\n  background-color: #ffffff;\\n  border-radius: 4px;\\n}\\n\\n.menu-item[_ngcontent-%COMP%] {\\n  display: block;\\n  position: relative;\\n  min-width: 40px;\\n  margin: 0;\\n  padding: 6px 16px;\\n  font: bold 12px sans-serif;\\n  color: rgba(0, 0, 0, 0.87);\\n  cursor: pointer;\\n}\\n\\n.add-menu[_ngcontent-%COMP%] {\\n  top: 5px;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  opacity: 0;\\n  pointer-events: none;\\n  content: \\\"\\\";\\n  width: 100%;\\n  height: 100%;\\n  background-color: #000000;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 0.04;\\n}\\n\\n.menu[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%] {\\n  top: -8px;\\n  left: 100%;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.menu-item[_ngcontent-%COMP%]:hover {\\n  background-color: #0069b4;\\n  color: #ffffff;\\n}\\n\\n.literal-item[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.show-menu[_ngcontent-%COMP%], .menu-item[_ngcontent-%COMP%]:hover    > .menu[_ngcontent-%COMP%] {\\n  display: block;\\n  opacity: 1;\\n}\\n\\n.submenu-indicator[_ngcontent-%COMP%] {\\n  display: none;\\n  float: inline-end;\\n}\\n\\n#add[_ngcontent-%COMP%]:hover    > .submenu-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n}\\n\\n.loader-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  z-index: 1000;\\n}\\n\\n.tree-container[_ngcontent-%COMP%] {\\n  max-height: 320px;\\n  overflow-y: auto;\\n}\\n\\nmat-expansion-panel-header.mat-expansion-panel-header[aria-disabled=true][_ngcontent-%COMP%] {\\n  color: initial;\\n}\\n\\n\\n\\n.overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  z-index: 1000;\\n}\\n\\n\\n\\n.version-history-container[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: var(--toolbar-height);\\n  right: 0;\\n  bottom: 0;\\n  width: 280px;\\n  z-index: 999;\\n  background: #cfcfcf;\\n}\\n\\n.version-history-drawer[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.version-history-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px;\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.12);\\n}\\n.version-history-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.version-history-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.back-button[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 1.5px;\\n  left: 5px;\\n  z-index: 1011;\\n  background-color: #f5f5f5;\\n  border-radius: 50%;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\\n}\\n\\n.content-margin[_ngcontent-%COMP%] {\\n  margin-right: 280px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["effect", "go", "DialogConfirmationComponent", "LibraryTreeComponent", "GojsNodeCategory", "AccessType", "TreeNodeTag", "environment", "i0", "ɵɵelementStart", "ɵɵlistener", "DiagramEditorComponent_div_19_Template_app_properties_onChangePropertyData_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onUpdateProperties", "libraryTreeComponent", "selectedTreeNode", "DiagramEditorComponent_div_19_Template_app_properties_isColorPickerSelected_1_listener", "ctx_r4", "onColorPickerSelection", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "propertyData", "hasEditAccessOnly", "attributeTypes", "selectedDiagramId", "ɵɵelement", "DiagramEditorComponent", "constructor", "diagramService", "projectService", "propertyService", "route", "router", "linkService", "accessService", "goJsService", "diagramUtils", "loaderService", "treeNodeService", "navbarService", "dialog", "appService", "versionHistoryService", "$", "GraphObject", "make", "isComponentPanelExpanded", "isLibraryPanelExpanded", "diagrams", "_downloadSub", "_downloadAllSub", "_currentProjectSub", "_colorSelectionSub", "_diagramSub", "_currentProjDiagramsSub", "_deleteDiagramSub", "_propertyDataSub", "_attributeTypeSub", "showVersionHistory", "isLoading$", "_diagramLayoutSub", "selectedNodeTag", "Project", "ngOnInit", "setIsInitProject", "Diagram", "licenseKey", "projectId", "parseInt", "snapshot", "paramMap", "get", "diagramIdParam", "initialDiagramId", "undefined", "localStorage", "getItem", "openProject", "initLinkTypes", "removeItem", "configureDiagramProperties", "initProject", "accessTypeChanges", "subscribe", "response", "Viewer", "subscribeToDiagramService", "getAttributeTypes", "options", "unload<PERSON><PERSON><PERSON>", "event", "preventDefault", "handleProjectUnlock", "setItem", "diagram", "initialAutoScale", "AutoScale", "Uniform", "allowCopy", "<PERSON><PERSON><PERSON>", "desiredSize", "Size", "fill", "stroke", "mouseDrop", "e", "handleDropCompletion", "isGroup", "text", "horiz", "grid", "Panel", "gridCellSize", "add", "strokeWidth", "interval", "activeDiagramChanges", "console", "log", "currentDiagram", "id", "getDiagramDetails", "currentProjectDiagramsChanges", "length", "diagramId", "targetDiagram", "find", "d", "setActiveDiagram", "currentProjectChanges", "project", "setProjectLinks", "initDiagram", "getPaletteDiagramDetails", "onDiagramSelectionChange", "activeDiagram", "setPropertyData", "navigate", "replaceUrl", "deleteDiagramEvent", "onDeleteDiagram", "downloadDiagramEvent", "initiateDiagramDownload", "downloadAllDiagramEvent", "isForOnlyImage", "propertyDataChanges", "allowDrop", "dialogRef", "open", "width", "data", "title", "reject", "confirm", "afterClosed", "isConfirm", "deleteDiagram", "filter", "setCurrentProjectDiagrams", "ngOnDestroy", "clearLinks", "clearLinkToLinks", "setSelectedVersion", "unsubscribe", "isClicked", "updatedNode", "treeNode", "getUpdatedProperties", "onCreateFolder", "stopPropagation", "createNewFolder", "dataSource", "treeControl", "expand", "openVersionHistory", "setShowVersionHistory", "setProjectAccess", "closeVersionHistory", "_", "ɵɵdirectiveInject", "i1", "DiagramService", "i2", "ProjectService", "i3", "PropertyService", "i4", "ActivatedRoute", "Router", "i5", "CardinalityService", "i6", "AccessService", "i7", "GojsService", "i8", "DiagramUtils", "i9", "LoaderService", "i10", "TreeNodeService", "i11", "NavbarService", "i12", "MatDialog", "i13", "AppService", "i14", "VersionHistoryService", "_2", "selectors", "viewQuery", "DiagramEditorComponent_Query", "rf", "ctx", "ɵɵresolveWindow", "ɵɵtext", "DiagramEditorComponent_Template_button_click_8_listener", "ɵɵtemplate", "DiagramEditorComponent_div_19_Template", "DiagramEditorComponent_mat_drawer_container_23_Template", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "ɵɵstyleProp", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\pages\\home\\diagram-editor\\diagram-editor.component.ts", "D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\pages\\home\\diagram-editor\\diagram-editor.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  effect,\r\n  HostListener,\r\n  On<PERSON><PERSON><PERSON>,\r\n  OnInit,\r\n  ViewChild,\r\n} from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport * as go from 'gojs';\r\nimport { Subscription } from 'rxjs';\r\nimport { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';\r\nimport { LibraryTreeComponent } from 'src/app/core/components/library-tree/library-tree.component';\r\nimport { AccessService } from 'src/app/core/services/access/access.service';\r\nimport { AppService } from 'src/app/core/services/app.service';\r\nimport { CardinalityService } from 'src/app/core/services/cardinality/cardinality.service';\r\nimport { DiagramService } from 'src/app/core/services/diagram/diagram.service';\r\nimport { GojsService } from 'src/app/core/services/gojs/gojs.service';\r\nimport { LoaderService } from 'src/app/core/services/loader/loader.service';\r\nimport { NavbarService } from 'src/app/core/services/navbar/navbar.service';\r\nimport { ProjectService } from 'src/app/core/services/project/project.service';\r\nimport { PropertyService } from 'src/app/core/services/property/property.service';\r\nimport { TreeNodeService } from 'src/app/core/services/treeNode/tree-node.service';\r\nimport { VersionHistoryService } from 'src/app/core/services/versionHistory/version-history.service';\r\nimport { AttributeOption } from 'src/app/shared/model/attribute';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport { ConfirmDialogData } from 'src/app/shared/model/dialog';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsLinkNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './diagram-editor.component.html',\r\n  styleUrls: ['./diagram-editor.component.scss'],\r\n})\r\nexport class DiagramEditorComponent implements OnInit, OnDestroy {\r\n  private $ = go.GraphObject.make;\r\n  isComponentPanelExpanded = false;\r\n  isLibraryPanelExpanded = true;\r\n  diagram!: go.Diagram;\r\n  diagrams: Diagram[] = [];\r\n  selectedDiagramId: number = -1;\r\n  project!: ProjectDetails;\r\n  projectId!: number;\r\n  hasEditAccessOnly: boolean = false;\r\n  propertyData!:\r\n    | GojsDiagramClassNode\r\n    | GojsDiagramEnumerationNode\r\n    | GojsDiagramAttributeNode\r\n    | GojsDiagramLiteralNode\r\n    | GojsLinkNode\r\n    | GojsFolderNode;\r\n\r\n  attributeTypes: AttributeOption[] = [];\r\n  private currentDiagram!: Diagram;\r\n  private _downloadSub: Subscription | null = null;\r\n  private _downloadAllSub: Subscription | null = null;\r\n  private _currentProjectSub: Subscription | null = null;\r\n  private _colorSelectionSub: Subscription | null = null;\r\n  private _diagramSub: Subscription | null = null;\r\n  private _currentProjDiagramsSub: Subscription | null = null;\r\n  private _deleteDiagramSub: Subscription | null = null;\r\n  private _propertyDataSub: Subscription | null = null;\r\n  private _attributeTypeSub: Subscription | null = null;\r\n  showVersionHistory = false;\r\n  currentProjectDetails!: ProjectDetails;\r\n  isLoading$ = this.loaderService.isLoading$;\r\n  private _diagramLayoutSub: Subscription | null = null;\r\n  @ViewChild(LibraryTreeComponent)\r\n  libraryTreeComponent!: LibraryTreeComponent;\r\n  selectedNodeTag: string = TreeNodeTag.Project;\r\n  constructor(\r\n    private diagramService: DiagramService,\r\n    private projectService: ProjectService,\r\n    private propertyService: PropertyService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private linkService: CardinalityService,\r\n    private accessService: AccessService,\r\n    private goJsService: GojsService,\r\n    private diagramUtils: DiagramUtils,\r\n    private loaderService: LoaderService,\r\n    private treeNodeService: TreeNodeService,\r\n    private navbarService: NavbarService,\r\n    private dialog: MatDialog,\r\n    private appService: AppService,\r\n    private versionHistoryService: VersionHistoryService\r\n  ) {\r\n    effect(() => {\r\n      this.showVersionHistory = this.navbarService.showVersionHistory();\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.appService.setIsInitProject(true);\r\n    go.Diagram.licenseKey = environment.licenseKey;\r\n    this.projectId = parseInt(this.route.snapshot.paramMap.get('id') ?? '');\r\n\r\n    // Check if a specific diagram ID is provided in the route\r\n    const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');\r\n    const initialDiagramId = diagramIdParam\r\n      ? parseInt(diagramIdParam)\r\n      : undefined;\r\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${initialDiagramId}`;\r\n\r\n    if (localStorage.getItem('reloaded') || localStorage.getItem('copyUrl')) {\r\n      // Open the project with the specific diagram if provided\r\n      this.projectService.openProject(this.projectId, true, initialDiagramId);\r\n      this.linkService.initLinkTypes();\r\n      localStorage.removeItem('reloaded');\r\n      localStorage.removeItem('copyUrl');\r\n    }\r\n\r\n    this.configureDiagramProperties();\r\n    this.initProject();\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      this.hasEditAccessOnly = response != AccessType.Viewer;\r\n    });\r\n    this.subscribeToDiagramService();\r\n    this._attributeTypeSub = this.diagramUtils\r\n      .getAttributeTypes()\r\n      .subscribe((options) => {\r\n        this.attributeTypes = options;\r\n      });\r\n  }\r\n\r\n  @HostListener('window:beforeunload', ['$event'])\r\n  unloadHandler(event: BeforeUnloadEvent): void {\r\n    event.preventDefault();\r\n    this.projectService.handleProjectUnlock(this.projectId);\r\n    localStorage.setItem('reloaded', 'true');\r\n  }\r\n  /**\r\n   * Configures properties for the main diagram.\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  configureDiagramProperties(): void {\r\n    this.diagram = this.$(go.Diagram, 'diagramDiv', {\r\n      initialAutoScale: go.AutoScale.Uniform,\r\n      allowCopy: false,\r\n      'animationManager.isEnabled': false,\r\n      'linkingTool.isEnabled': true, // invoked explicitly by drawLink function, below\r\n      'draggingTool.isGridSnapEnabled': true,\r\n      'linkingTool.isUnconnectedLinkValid': false,\r\n      'linkingTool.portGravity': 20,\r\n      'relinkingTool.isUnconnectedLinkValid': false,\r\n      'relinkingTool.portGravity': 10,\r\n      'linkReshapingTool.handleArchetype': this.$(go.Shape, 'Diamond', {\r\n        desiredSize: new go.Size(7, 7),\r\n        fill: 'lightblue',\r\n        stroke: '#0069B4',\r\n      }),\r\n      mouseDrop: (e: go.InputEvent) =>\r\n        this.goJsService.handleDropCompletion(e, null),\r\n      'commandHandler.archetypeGroupData': {\r\n        isGroup: true,\r\n        text: 'Group',\r\n        horiz: false,\r\n      },\r\n      'undoManager.isEnabled': true,\r\n      'relinkingTool.isEnabled': true,\r\n    });\r\n    this.diagram.grid = new go.Panel('Grid', {\r\n      gridCellSize: new go.Size(10, 10),\r\n    }).add(\r\n      new go.Shape('LineH', {\r\n        stroke: 'lightgray',\r\n        strokeWidth: 0.5,\r\n        interval: 1,\r\n      }),\r\n      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),\r\n      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 1.0, interval: 10 }),\r\n      new go.Shape('LineV', {\r\n        stroke: 'lightgray',\r\n        strokeWidth: 0.5,\r\n        interval: 1,\r\n      }),\r\n      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),\r\n      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 1.0, interval: 10 })\r\n    );\r\n  }\r\n  private initProject() {\r\n    this._diagramSub = this.diagramUtils\r\n      .activeDiagramChanges()\r\n      .subscribe((diagram) => {\r\n        if (diagram) {\r\n          console.log(diagram);\r\n          this.currentDiagram = diagram;\r\n          this.selectedDiagramId = this.currentDiagram.id!;\r\n          this.diagramService.getDiagramDetails(diagram.id!);\r\n        }\r\n      });\r\n\r\n    this._currentProjDiagramsSub = this.diagramUtils\r\n      .currentProjectDiagramsChanges()\r\n      .subscribe((diagrams) => {\r\n        if (diagrams.length > 0) {\r\n          this.diagrams = diagrams;\r\n\r\n          // If no active diagram is set yet, set the first diagram as active\r\n          // This handles the case when no specific diagram ID is provided in the URL\r\n          if (!this.currentDiagram && this.diagrams.length > 0) {\r\n            // Check if a diagram ID is specified in the route\r\n            const diagramIdParam =\r\n              this.route.snapshot.paramMap.get('idDiagram');\r\n\r\n            if (diagramIdParam) {\r\n              // If a diagram ID is specified, find and set that diagram as active\r\n              const diagramId = parseInt(diagramIdParam);\r\n              const targetDiagram = this.diagrams.find(\r\n                (d) => d.id === diagramId\r\n              );\r\n\r\n              if (targetDiagram) {\r\n                debugger;\r\n                this.diagramUtils.setActiveDiagram(targetDiagram);\r\n              } else {\r\n                debugger;\r\n                // If the specified diagram doesn't exist, fall back to the first diagram\r\n                this.diagramUtils.setActiveDiagram(this.diagrams[0]);\r\n              }\r\n            } else {\r\n              debugger;\r\n              // If no diagram ID is specified, set the first diagram as active\r\n              this.diagramUtils.setActiveDiagram(this.diagrams[0]);\r\n            }\r\n          }\r\n        }\r\n      });\r\n\r\n    this._currentProjectSub = this.projectService\r\n      .currentProjectChanges()\r\n      .subscribe((project) => {\r\n        if (project) {\r\n          this.linkService.setProjectLinks(project);\r\n          this.goJsService.initDiagram(this.diagram);\r\n          this.diagramService.getPaletteDiagramDetails();\r\n        }\r\n      });\r\n  }\r\n\r\n  onDiagramSelectionChange(diagramId: number) {\r\n    this.selectedDiagramId = diagramId;\r\n    const activeDiagram = this.diagrams.find((d) => d.id === diagramId);\r\n    debugger;\r\n    this.diagramUtils.setActiveDiagram(activeDiagram!);\r\n    this.propertyService.setPropertyData(null);\r\n\r\n    // Update the URL to include the selected diagram ID without reloading the page\r\n    this.router.navigate([`/editor/${this.projectId}/diagram/${diagramId}`], {\r\n      replaceUrl: true, // Replace the current URL instead of adding a new history entry\r\n    });\r\n    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${diagramId}`;\r\n  }\r\n\r\n  /**\r\n   * Subscribes to various diagram service subjects to handle diagram actions.\r\n   */\r\n  private subscribeToDiagramService() {\r\n    this._deleteDiagramSub = this.diagramService.deleteDiagramEvent.subscribe(\r\n      () => {\r\n        this.onDeleteDiagram();\r\n      }\r\n    );\r\n    this._downloadSub = this.diagramService.downloadDiagramEvent.subscribe(\r\n      () => {\r\n        this.diagramService.initiateDiagramDownload(true, false);\r\n      }\r\n    );\r\n    this._downloadAllSub =\r\n      this.diagramService.downloadAllDiagramEvent.subscribe(\r\n        (isForOnlyImage) => {\r\n          this.diagramService.initiateDiagramDownload(false, isForOnlyImage);\r\n          this.onDiagramSelectionChange(this.currentDiagram.id!);\r\n        }\r\n      );\r\n    this._propertyDataSub = this.propertyService\r\n      .propertyDataChanges()\r\n      .subscribe((propertyData) => {\r\n        if (propertyData) this.propertyData = propertyData;\r\n      });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      if (this.diagram) this.diagram.allowDrop = diagrams.length > 0;\r\n    });\r\n  }\r\n\r\n  onDeleteDiagram() {\r\n    const dialogRef = this.dialog.open<\r\n      DialogConfirmationComponent,\r\n      ConfirmDialogData,\r\n      boolean\r\n    >(DialogConfirmationComponent, {\r\n      width: '320px',\r\n      data: {\r\n        title: 'dialog.title',\r\n        reject: 'dialog.no',\r\n        confirm: 'dialog.yes',\r\n      },\r\n    });\r\n    dialogRef.afterClosed().subscribe((isConfirm) => {\r\n      if (isConfirm) {\r\n        if (this.selectedDiagramId) {\r\n          this.diagramService.deleteDiagram(this.selectedDiagramId);\r\n          this.treeNodeService.deleteDiagram(\r\n            `atTag${GojsNodeCategory.Diagram}_${this.selectedDiagramId}`\r\n          );\r\n          this.diagrams = this.diagrams.filter(\r\n            (diagram: Diagram) => diagram.id !== this.selectedDiagramId\r\n          );\r\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n          if (this.diagrams.length > 0) {\r\n            this.currentDiagram = this.diagrams[0];\r\n            this.selectedDiagramId = this.currentDiagram.id!;\r\n            debugger;\r\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\r\n\r\n            // Update the URL to reflect the new active diagram\r\n            this.router.navigate(\r\n              [`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],\r\n              {\r\n                replaceUrl: true,\r\n              }\r\n            );\r\n          } else {\r\n            debugger;\r\n            this.diagramUtils.setActiveDiagram(null);\r\n\r\n            // If there are no diagrams left, navigate to a placeholder URL\r\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\r\n              replaceUrl: true,\r\n            });\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.linkService.clearLinks();\r\n    this.linkService.clearLinkToLinks();\r\n    this.versionHistoryService.setSelectedVersion(null);\r\n    this.projectService.handleProjectUnlock(this.projectId);\r\n    if (this._downloadSub) this._downloadSub.unsubscribe();\r\n    if (this._downloadAllSub) this._downloadAllSub.unsubscribe();\r\n    if (this._colorSelectionSub) this._colorSelectionSub.unsubscribe();\r\n    if (this._diagramSub) this._diagramSub.unsubscribe();\r\n    if (this._currentProjDiagramsSub)\r\n      this._currentProjDiagramsSub.unsubscribe();\r\n    if (this._deleteDiagramSub) this._deleteDiagramSub.unsubscribe();\r\n    if (this._propertyDataSub) this._propertyDataSub.unsubscribe();\r\n    if (this._currentProjectSub) this._currentProjectSub.unsubscribe();\r\n    if (this._attributeTypeSub) this._attributeTypeSub.unsubscribe();\r\n    if (this._diagramLayoutSub) this._diagramLayoutSub.unsubscribe();\r\n    this.propertyService.setPropertyData(null);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to color selection changes and updates the component state accordingly.\r\n   */\r\n  onColorPickerSelection(isClicked: boolean) {\r\n    if (isClicked) {\r\n      this.isComponentPanelExpanded = false;\r\n      this.isLibraryPanelExpanded = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Subscribes to property data updates and handles updates to the diagram and palette.\r\n   */\r\n  onUpdateProperties(\r\n    updatedNode:\r\n      | GojsDiagramClassNode\r\n      | GojsDiagramEnumerationNode\r\n      | GojsDiagramAttributeNode\r\n      | GojsDiagramLiteralNode\r\n      | GojsLinkNode\r\n      | GojsFolderNode,\r\n    treeNode: TreeNode | null\r\n  ): void {\r\n    this.diagramService.getUpdatedProperties(updatedNode, treeNode);\r\n  }\r\n\r\n  onCreateFolder(event: Event) {\r\n    event.stopPropagation();\r\n    this.goJsService.createNewFolder('New Folder', this.projectId);\r\n    if (\r\n      this.libraryTreeComponent.dataSource &&\r\n      this.libraryTreeComponent.dataSource.data.length > 0\r\n    ) {\r\n      this.libraryTreeComponent.treeControl.expand(\r\n        this.libraryTreeComponent.dataSource.data[0]\r\n      );\r\n    }\r\n  }\r\n\r\n  // Function to open version history\r\n  openVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(true);\r\n    this.accessService.setProjectAccess(AccessType.Viewer);\r\n  }\r\n\r\n  // Function to close version history\r\n  closeVersionHistory() {\r\n    this.navbarService.setShowVersionHistory(false);\r\n    // this.accessService.setProjectAccess(this.project.accessType);\r\n  }\r\n}\r\n", "<mat-drawer-container class=\"project-container\">\r\n  <mat-drawer mode=\"side\" opened class=\"side-container\" [disableClose]=\"true\">\r\n    <mat-accordion multi=\"true\">\r\n      <mat-expansion-panel\r\n        class=\"expansion-panel library-panel\"\r\n        [hideToggle]=\"true\"\r\n        expanded=\"true\"\r\n        disabled=\"true\"\r\n      >\r\n        <mat-expansion-panel-header class=\"disable_ripple\">\r\n          <mat-panel-title class=\"panel-title\">\r\n            {{ \"diagram.library\" | translate }}\r\n          </mat-panel-title>\r\n          <button\r\n            [disabled]=\"!hasEditAccessOnly\"\r\n            mat-icon-button\r\n            (click)=\"onCreateFolder($event)\"\r\n            matRippleDisabled\r\n          >\r\n            <mat-icon>create_new_folder</mat-icon>\r\n          </button>\r\n        </mat-expansion-panel-header>\r\n        <div class=\"tree-container\">\r\n          <app-library-tree\r\n            [expandNodeTag]=\"selectedNodeTag\"\r\n          ></app-library-tree>\r\n        </div>\r\n      </mat-expansion-panel>\r\n\r\n      <!-- Hide the components panel when version history is shown -->\r\n      <mat-expansion-panel\r\n        class=\"expansion-panel\"\r\n        [hideToggle]=\"true\"\r\n        expanded=\"true\"\r\n        disabled=\"true\"\r\n        [style.visibility]=\"showVersionHistory ? 'hidden' : 'visible'\"\r\n        [style.height]=\"showVersionHistory ? '0' : 'auto'\"\r\n      >\r\n        <mat-expansion-panel-header class=\"disable_ripple\">\r\n          <mat-panel-title class=\"panel-title\">\r\n            {{ \"diagram.components\" | translate }}\r\n          </mat-panel-title>\r\n        </mat-expansion-panel-header>\r\n        <div id=\"classDiagram\" class=\"disable_ripple component-panel\"></div>\r\n      </mat-expansion-panel>\r\n    </mat-accordion>\r\n    <div class=\"property\" *ngIf=\"hasEditAccessOnly\">\r\n      <app-properties\r\n        style=\"margin-top: 20px; padding-top: 20px\"\r\n        [nodeData]=\"propertyData\"\r\n        (onChangePropertyData)=\"\r\n          onUpdateProperties($event, libraryTreeComponent.selectedTreeNode)\r\n        \"\r\n        [editAccess]=\"hasEditAccessOnly\"\r\n        (isColorPickerSelected)=\"onColorPickerSelection($event)\"\r\n        [dataTypes]=\"attributeTypes\"\r\n        [idDiagram]=\"selectedDiagramId\"\r\n      ></app-properties>\r\n    </div>\r\n  </mat-drawer>\r\n  <mat-drawer-content [ngClass]=\"{ 'content-margin': showVersionHistory }\">\r\n    <div class=\"main-container\" id=\"diagramDiv\"></div>\r\n    <app-action-controls [diagram]=\"diagram\"></app-action-controls>\r\n  </mat-drawer-content>\r\n</mat-drawer-container>\r\n\r\n<!-- Overlay and Version History Drawer -->\r\n<mat-drawer-container\r\n  class=\"version-history-container\"\r\n  *ngIf=\"showVersionHistory\"\r\n>\r\n  <mat-drawer mode=\"over\" opened class=\"version-history-drawer\">\r\n    <app-version-history></app-version-history>\r\n  </mat-drawer>\r\n</mat-drawer-container>\r\n"], "mappings": "AAAA,SAEEA,MAAM,QAKD,eAAe;AAGtB,OAAO,KAAKC,EAAE,MAAM,MAAM;AAE1B,SAASC,2BAA2B,QAAQ,2EAA2E;AACvH,SAASC,oBAAoB,QAAQ,6DAA6D;AAelG,SAOEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAwB,8BAA8B;AACzE,SAAmBC,WAAW,QAAQ,+BAA+B;AAErE,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICMtDC,EAAA,CAAAC,cAAA,cAAgD;IAI5CD,EAAA,CAAAE,UAAA,kCAAAC,sFAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OACaR,EAAA,CAAAS,WAAA,CAAAF,MAAA,CAAAG,kBAAA,CAAAN,MAAA,EAAAG,MAAA,CAAAI,oBAAA,CAAAC,gBAAA,CACZ;IAAA,qCAAAC,uFAAAT,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAQ,MAAA,GAAAd,EAAA,CAAAQ,aAAA;MAAA,OAEwBR,EAAA,CAAAS,WAAA,CAAAK,MAAA,CAAAC,sBAAA,CAAAX,MAAA,CAA8B;IAAA,EAFtD;IAKFJ,EAAA,CAAAgB,YAAA,EAAiB;;;;IARhBhB,EAAA,CAAAiB,SAAA,GAAyB;IAAzBjB,EAAA,CAAAkB,UAAA,aAAAC,MAAA,CAAAC,YAAA,CAAyB,eAAAD,MAAA,CAAAE,iBAAA,eAAAF,MAAA,CAAAG,cAAA,eAAAH,MAAA,CAAAI,iBAAA;;;;;IAkBjCvB,EAAA,CAAAC,cAAA,+BAGC;IAEGD,EAAA,CAAAwB,SAAA,0BAA2C;IAC7CxB,EAAA,CAAAgB,YAAA,EAAa;;;;;;;;AD1Bf,OAAM,MAAOS,sBAAsB;EAoCjCC,YACUC,cAA8B,EAC9BC,cAA8B,EAC9BC,eAAgC,EAChCC,KAAqB,EACrBC,MAAc,EACdC,WAA+B,EAC/BC,aAA4B,EAC5BC,WAAwB,EACxBC,YAA0B,EAC1BC,aAA4B,EAC5BC,eAAgC,EAChCC,aAA4B,EAC5BC,MAAiB,EACjBC,UAAsB,EACtBC,qBAA4C;IAd5C,KAAAd,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,qBAAqB,GAArBA,qBAAqB;IAlDvB,KAAAC,CAAC,GAAGjD,EAAE,CAACkD,WAAW,CAACC,IAAI;IAC/B,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,sBAAsB,GAAG,IAAI;IAE7B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAxB,iBAAiB,GAAW,CAAC,CAAC;IAG9B,KAAAF,iBAAiB,GAAY,KAAK;IASlC,KAAAC,cAAc,GAAsB,EAAE;IAE9B,KAAA0B,YAAY,GAAwB,IAAI;IACxC,KAAAC,eAAe,GAAwB,IAAI;IAC3C,KAAAC,kBAAkB,GAAwB,IAAI;IAC9C,KAAAC,kBAAkB,GAAwB,IAAI;IAC9C,KAAAC,WAAW,GAAwB,IAAI;IACvC,KAAAC,uBAAuB,GAAwB,IAAI;IACnD,KAAAC,iBAAiB,GAAwB,IAAI;IAC7C,KAAAC,gBAAgB,GAAwB,IAAI;IAC5C,KAAAC,iBAAiB,GAAwB,IAAI;IACrD,KAAAC,kBAAkB,GAAG,KAAK;IAE1B,KAAAC,UAAU,GAAG,IAAI,CAACtB,aAAa,CAACsB,UAAU;IAClC,KAAAC,iBAAiB,GAAwB,IAAI;IAGrD,KAAAC,eAAe,GAAW9D,WAAW,CAAC+D,OAAO;IAkB3CrE,MAAM,CAAC,MAAK;MACV,IAAI,CAACiE,kBAAkB,GAAG,IAAI,CAACnB,aAAa,CAACmB,kBAAkB,EAAE;IACnE,CAAC,CAAC;EACJ;EAEAK,QAAQA,CAAA;IACN,IAAI,CAACtB,UAAU,CAACuB,gBAAgB,CAAC,IAAI,CAAC;IACtCtE,EAAE,CAACuE,OAAO,CAACC,UAAU,GAAGlE,WAAW,CAACkE,UAAU;IAC9C,IAAI,CAACC,SAAS,GAAGC,QAAQ,CAAC,IAAI,CAACrC,KAAK,CAACsC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAEvE;IACA,MAAMC,cAAc,GAAG,IAAI,CAACzC,KAAK,CAACsC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;IACpE,MAAME,gBAAgB,GAAGD,cAAc,GACnCJ,QAAQ,CAACI,cAAc,CAAC,GACxBE,SAAS;IACb,IAAI,CAACb,eAAe,GAAG,QAAQhE,gBAAgB,CAACoE,OAAO,IAAIQ,gBAAgB,EAAE;IAE7E,IAAIE,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,SAAS,CAAC,EAAE;MACvE;MACA,IAAI,CAAC/C,cAAc,CAACgD,WAAW,CAAC,IAAI,CAACV,SAAS,EAAE,IAAI,EAAEM,gBAAgB,CAAC;MACvE,IAAI,CAACxC,WAAW,CAAC6C,aAAa,EAAE;MAChCH,YAAY,CAACI,UAAU,CAAC,UAAU,CAAC;MACnCJ,YAAY,CAACI,UAAU,CAAC,SAAS,CAAC;;IAGpC,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAAC/C,aAAa,CAACgD,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAC5D,IAAI,CAAC9D,iBAAiB,GAAG8D,QAAQ,IAAItF,UAAU,CAACuF,MAAM;IACxD,CAAC,CAAC;IACF,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACrB,YAAY,CACvCmD,iBAAiB,EAAE,CACnBJ,SAAS,CAAEK,OAAO,IAAI;MACrB,IAAI,CAACjE,cAAc,GAAGiE,OAAO;IAC/B,CAAC,CAAC;EACN;EAGAC,aAAaA,CAACC,KAAwB;IACpCA,KAAK,CAACC,cAAc,EAAE;IACtB,IAAI,CAAC9D,cAAc,CAAC+D,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC;IACvDQ,YAAY,CAACkB,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC;EAC1C;EACA;;;;EAIAb,0BAA0BA,CAAA;IACxB,IAAI,CAACc,OAAO,GAAG,IAAI,CAACnD,CAAC,CAACjD,EAAE,CAACuE,OAAO,EAAE,YAAY,EAAE;MAC9C8B,gBAAgB,EAAErG,EAAE,CAACsG,SAAS,CAACC,OAAO;MACtCC,SAAS,EAAE,KAAK;MAChB,4BAA4B,EAAE,KAAK;MACnC,uBAAuB,EAAE,IAAI;MAC7B,gCAAgC,EAAE,IAAI;MACtC,oCAAoC,EAAE,KAAK;MAC3C,yBAAyB,EAAE,EAAE;MAC7B,sCAAsC,EAAE,KAAK;MAC7C,2BAA2B,EAAE,EAAE;MAC/B,mCAAmC,EAAE,IAAI,CAACvD,CAAC,CAACjD,EAAE,CAACyG,KAAK,EAAE,SAAS,EAAE;QAC/DC,WAAW,EAAE,IAAI1G,EAAE,CAAC2G,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9BC,IAAI,EAAE,WAAW;QACjBC,MAAM,EAAE;OACT,CAAC;MACFC,SAAS,EAAGC,CAAgB,IAC1B,IAAI,CAACtE,WAAW,CAACuE,oBAAoB,CAACD,CAAC,EAAE,IAAI,CAAC;MAChD,mCAAmC,EAAE;QACnCE,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE,OAAO;QACbC,KAAK,EAAE;OACR;MACD,uBAAuB,EAAE,IAAI;MAC7B,yBAAyB,EAAE;KAC5B,CAAC;IACF,IAAI,CAACf,OAAO,CAACgB,IAAI,GAAG,IAAIpH,EAAE,CAACqH,KAAK,CAAC,MAAM,EAAE;MACvCC,YAAY,EAAE,IAAItH,EAAE,CAAC2G,IAAI,CAAC,EAAE,EAAE,EAAE;KACjC,CAAC,CAACY,GAAG,CACJ,IAAIvH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MACpBI,MAAM,EAAE,WAAW;MACnBW,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE;KACX,CAAC,EACF,IAAIzH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAC,CAAE,CAAC,EACxE,IAAIzH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAE,CAAE,CAAC,EACzE,IAAIzH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MACpBI,MAAM,EAAE,WAAW;MACnBW,WAAW,EAAE,GAAG;MAChBC,QAAQ,EAAE;KACX,CAAC,EACF,IAAIzH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAC,CAAE,CAAC,EACxE,IAAIzH,EAAE,CAACyG,KAAK,CAAC,OAAO,EAAE;MAAEI,MAAM,EAAE,MAAM;MAAEW,WAAW,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAE,CAAE,CAAC,CAC1E;EACH;EACQlC,WAAWA,CAAA;IACjB,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACjB,YAAY,CACjCgF,oBAAoB,EAAE,CACtBjC,SAAS,CAAEW,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACXuB,OAAO,CAACC,GAAG,CAACxB,OAAO,CAAC;QACpB,IAAI,CAACyB,cAAc,GAAGzB,OAAO;QAC7B,IAAI,CAACtE,iBAAiB,GAAG,IAAI,CAAC+F,cAAc,CAACC,EAAG;QAChD,IAAI,CAAC5F,cAAc,CAAC6F,iBAAiB,CAAC3B,OAAO,CAAC0B,EAAG,CAAC;;IAEtD,CAAC,CAAC;IAEJ,IAAI,CAAClE,uBAAuB,GAAG,IAAI,CAAClB,YAAY,CAC7CsF,6BAA6B,EAAE,CAC/BvC,SAAS,CAAEnC,QAAQ,IAAI;MACtB,IAAIA,QAAQ,CAAC2E,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAAC3E,QAAQ,GAAGA,QAAQ;QAExB;QACA;QACA,IAAI,CAAC,IAAI,CAACuE,cAAc,IAAI,IAAI,CAACvE,QAAQ,CAAC2E,MAAM,GAAG,CAAC,EAAE;UACpD;UACA,MAAMnD,cAAc,GAClB,IAAI,CAACzC,KAAK,CAACsC,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,WAAW,CAAC;UAE/C,IAAIC,cAAc,EAAE;YAClB;YACA,MAAMoD,SAAS,GAAGxD,QAAQ,CAACI,cAAc,CAAC;YAC1C,MAAMqD,aAAa,GAAG,IAAI,CAAC7E,QAAQ,CAAC8E,IAAI,CACrCC,CAAC,IAAKA,CAAC,CAACP,EAAE,KAAKI,SAAS,CAC1B;YAED,IAAIC,aAAa,EAAE;cACjB;cACA,IAAI,CAACzF,YAAY,CAAC4F,gBAAgB,CAACH,aAAa,CAAC;aAClD,MAAM;cACL;cACA;cACA,IAAI,CAACzF,YAAY,CAAC4F,gBAAgB,CAAC,IAAI,CAAChF,QAAQ,CAAC,CAAC,CAAC,CAAC;;WAEvD,MAAM;YACL;YACA;YACA,IAAI,CAACZ,YAAY,CAAC4F,gBAAgB,CAAC,IAAI,CAAChF,QAAQ,CAAC,CAAC,CAAC,CAAC;;;;IAI5D,CAAC,CAAC;IAEJ,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACtB,cAAc,CAC1CoG,qBAAqB,EAAE,CACvB9C,SAAS,CAAE+C,OAAO,IAAI;MACrB,IAAIA,OAAO,EAAE;QACX,IAAI,CAACjG,WAAW,CAACkG,eAAe,CAACD,OAAO,CAAC;QACzC,IAAI,CAAC/F,WAAW,CAACiG,WAAW,CAAC,IAAI,CAACtC,OAAO,CAAC;QAC1C,IAAI,CAAClE,cAAc,CAACyG,wBAAwB,EAAE;;IAElD,CAAC,CAAC;EACN;EAEAC,wBAAwBA,CAACV,SAAiB;IACxC,IAAI,CAACpG,iBAAiB,GAAGoG,SAAS;IAClC,MAAMW,aAAa,GAAG,IAAI,CAACvF,QAAQ,CAAC8E,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACP,EAAE,KAAKI,SAAS,CAAC;IACnE;IACA,IAAI,CAACxF,YAAY,CAAC4F,gBAAgB,CAACO,aAAc,CAAC;IAClD,IAAI,CAACzG,eAAe,CAAC0G,eAAe,CAAC,IAAI,CAAC;IAE1C;IACA,IAAI,CAACxG,MAAM,CAACyG,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAYyD,SAAS,EAAE,CAAC,EAAE;MACvEc,UAAU,EAAE,IAAI,CAAE;KACnB,CAAC;IACF,IAAI,CAAC7E,eAAe,GAAG,QAAQhE,gBAAgB,CAACoE,OAAO,IAAI2D,SAAS,EAAE;EACxE;EAEA;;;EAGQtC,yBAAyBA,CAAA;IAC/B,IAAI,CAAC/B,iBAAiB,GAAG,IAAI,CAAC3B,cAAc,CAAC+G,kBAAkB,CAACxD,SAAS,CACvE,MAAK;MACH,IAAI,CAACyD,eAAe,EAAE;IACxB,CAAC,CACF;IACD,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAACrB,cAAc,CAACiH,oBAAoB,CAAC1D,SAAS,CACpE,MAAK;MACH,IAAI,CAACvD,cAAc,CAACkH,uBAAuB,CAAC,IAAI,EAAE,KAAK,CAAC;IAC1D,CAAC,CACF;IACD,IAAI,CAAC5F,eAAe,GAClB,IAAI,CAACtB,cAAc,CAACmH,uBAAuB,CAAC5D,SAAS,CAClD6D,cAAc,IAAI;MACjB,IAAI,CAACpH,cAAc,CAACkH,uBAAuB,CAAC,KAAK,EAAEE,cAAc,CAAC;MAClE,IAAI,CAACV,wBAAwB,CAAC,IAAI,CAACf,cAAc,CAACC,EAAG,CAAC;IACxD,CAAC,CACF;IACH,IAAI,CAAChE,gBAAgB,GAAG,IAAI,CAAC1B,eAAe,CACzCmH,mBAAmB,EAAE,CACrB9D,SAAS,CAAE9D,YAAY,IAAI;MAC1B,IAAIA,YAAY,EAAE,IAAI,CAACA,YAAY,GAAGA,YAAY;IACpD,CAAC,CAAC;IACJ,IAAI,CAACe,YAAY,CAACsF,6BAA6B,EAAE,CAACvC,SAAS,CAAEnC,QAAQ,IAAI;MACvE,IAAI,IAAI,CAAC8C,OAAO,EAAE,IAAI,CAACA,OAAO,CAACoD,SAAS,GAAGlG,QAAQ,CAAC2E,MAAM,GAAG,CAAC;IAChE,CAAC,CAAC;EACJ;EAEAiB,eAAeA,CAAA;IACb,MAAMO,SAAS,GAAG,IAAI,CAAC3G,MAAM,CAAC4G,IAAI,CAIhCzJ,2BAA2B,EAAE;MAC7B0J,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;QACJC,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE;;KAEZ,CAAC;IACFN,SAAS,CAACO,WAAW,EAAE,CAACvE,SAAS,CAAEwE,SAAS,IAAI;MAC9C,IAAIA,SAAS,EAAE;QACb,IAAI,IAAI,CAACnI,iBAAiB,EAAE;UAC1B,IAAI,CAACI,cAAc,CAACgI,aAAa,CAAC,IAAI,CAACpI,iBAAiB,CAAC;UACzD,IAAI,CAACc,eAAe,CAACsH,aAAa,CAChC,QAAQ/J,gBAAgB,CAACoE,OAAO,IAAI,IAAI,CAACzC,iBAAiB,EAAE,CAC7D;UACD,IAAI,CAACwB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC6G,MAAM,CACjC/D,OAAgB,IAAKA,OAAO,CAAC0B,EAAE,KAAK,IAAI,CAAChG,iBAAiB,CAC5D;UACD,IAAI,CAACY,YAAY,CAAC0H,yBAAyB,CAAC,IAAI,CAAC9G,QAAQ,CAAC;UAC1D,IAAI,IAAI,CAACA,QAAQ,CAAC2E,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,CAACJ,cAAc,GAAG,IAAI,CAACvE,QAAQ,CAAC,CAAC,CAAC;YACtC,IAAI,CAACxB,iBAAiB,GAAG,IAAI,CAAC+F,cAAc,CAACC,EAAG;YAChD;YACA,IAAI,CAACpF,YAAY,CAAC4F,gBAAgB,CAAC,IAAI,CAACT,cAAc,CAAC;YAEvD;YACA,IAAI,CAACvF,MAAM,CAACyG,QAAQ,CAClB,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAY,IAAI,CAACoD,cAAc,CAACC,EAAE,EAAE,CAAC,EAC/D;cACEkB,UAAU,EAAE;aACb,CACF;WACF,MAAM;YACL;YACA,IAAI,CAACtG,YAAY,CAAC4F,gBAAgB,CAAC,IAAI,CAAC;YAExC;YACA,IAAI,CAAChG,MAAM,CAACyG,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACtE,SAAS,YAAY,CAAC,EAAE;cAC5DuE,UAAU,EAAE;aACb,CAAC;;;;IAIV,CAAC,CAAC;EACJ;EAEAqB,WAAWA,CAAA;IACT,IAAI,CAAC9H,WAAW,CAAC+H,UAAU,EAAE;IAC7B,IAAI,CAAC/H,WAAW,CAACgI,gBAAgB,EAAE;IACnC,IAAI,CAACvH,qBAAqB,CAACwH,kBAAkB,CAAC,IAAI,CAAC;IACnD,IAAI,CAACrI,cAAc,CAAC+D,mBAAmB,CAAC,IAAI,CAACzB,SAAS,CAAC;IACvD,IAAI,IAAI,CAAClB,YAAY,EAAE,IAAI,CAACA,YAAY,CAACkH,WAAW,EAAE;IACtD,IAAI,IAAI,CAACjH,eAAe,EAAE,IAAI,CAACA,eAAe,CAACiH,WAAW,EAAE;IAC5D,IAAI,IAAI,CAAC/G,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAAC+G,WAAW,EAAE;IAClE,IAAI,IAAI,CAAC9G,WAAW,EAAE,IAAI,CAACA,WAAW,CAAC8G,WAAW,EAAE;IACpD,IAAI,IAAI,CAAC7G,uBAAuB,EAC9B,IAAI,CAACA,uBAAuB,CAAC6G,WAAW,EAAE;IAC5C,IAAI,IAAI,CAAC5G,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAAC4G,WAAW,EAAE;IAChE,IAAI,IAAI,CAAC3G,gBAAgB,EAAE,IAAI,CAACA,gBAAgB,CAAC2G,WAAW,EAAE;IAC9D,IAAI,IAAI,CAAChH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB,CAACgH,WAAW,EAAE;IAClE,IAAI,IAAI,CAAC1G,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAAC0G,WAAW,EAAE;IAChE,IAAI,IAAI,CAACvG,iBAAiB,EAAE,IAAI,CAACA,iBAAiB,CAACuG,WAAW,EAAE;IAChE,IAAI,CAACrI,eAAe,CAAC0G,eAAe,CAAC,IAAI,CAAC;EAC5C;EAEA;;;EAGAxH,sBAAsBA,CAACoJ,SAAkB;IACvC,IAAIA,SAAS,EAAE;MACb,IAAI,CAACtH,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACC,sBAAsB,GAAG,KAAK;;EAEvC;EAEA;;;EAGApC,kBAAkBA,CAChB0J,WAMkB,EAClBC,QAAyB;IAEzB,IAAI,CAAC1I,cAAc,CAAC2I,oBAAoB,CAACF,WAAW,EAAEC,QAAQ,CAAC;EACjE;EAEAE,cAAcA,CAAC9E,KAAY;IACzBA,KAAK,CAAC+E,eAAe,EAAE;IACvB,IAAI,CAACtI,WAAW,CAACuI,eAAe,CAAC,YAAY,EAAE,IAAI,CAACvG,SAAS,CAAC;IAC9D,IACE,IAAI,CAACvD,oBAAoB,CAAC+J,UAAU,IACpC,IAAI,CAAC/J,oBAAoB,CAAC+J,UAAU,CAACrB,IAAI,CAAC3B,MAAM,GAAG,CAAC,EACpD;MACA,IAAI,CAAC/G,oBAAoB,CAACgK,WAAW,CAACC,MAAM,CAC1C,IAAI,CAACjK,oBAAoB,CAAC+J,UAAU,CAACrB,IAAI,CAAC,CAAC,CAAC,CAC7C;;EAEL;EAEA;EACAwB,kBAAkBA,CAAA;IAChB,IAAI,CAACvI,aAAa,CAACwI,qBAAqB,CAAC,IAAI,CAAC;IAC9C,IAAI,CAAC7I,aAAa,CAAC8I,gBAAgB,CAAClL,UAAU,CAACuF,MAAM,CAAC;EACxD;EAEA;EACA4F,mBAAmBA,CAAA;IACjB,IAAI,CAAC1I,aAAa,CAACwI,qBAAqB,CAAC,KAAK,CAAC;IAC/C;EACF;EAAC,QAAAG,CAAA,G;qBAlXUxJ,sBAAsB,EAAAzB,EAAA,CAAAkL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApL,EAAA,CAAAkL,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtL,EAAA,CAAAkL,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxL,EAAA,CAAAkL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1L,EAAA,CAAAkL,iBAAA,CAAAO,EAAA,CAAAE,MAAA,GAAA3L,EAAA,CAAAkL,iBAAA,CAAAU,EAAA,CAAAC,kBAAA,GAAA7L,EAAA,CAAAkL,iBAAA,CAAAY,EAAA,CAAAC,aAAA,GAAA/L,EAAA,CAAAkL,iBAAA,CAAAc,EAAA,CAAAC,WAAA,GAAAjM,EAAA,CAAAkL,iBAAA,CAAAgB,EAAA,CAAAC,YAAA,GAAAnM,EAAA,CAAAkL,iBAAA,CAAAkB,EAAA,CAAAC,aAAA,GAAArM,EAAA,CAAAkL,iBAAA,CAAAoB,GAAA,CAAAC,eAAA,GAAAvM,EAAA,CAAAkL,iBAAA,CAAAsB,GAAA,CAAAC,aAAA,GAAAzM,EAAA,CAAAkL,iBAAA,CAAAwB,GAAA,CAAAC,SAAA,GAAA3M,EAAA,CAAAkL,iBAAA,CAAA0B,GAAA,CAAAC,UAAA,GAAA7M,EAAA,CAAAkL,iBAAA,CAAA4B,GAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBvL,sBAAsB;IAAAwL,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAiCtBzN,oBAAoB;;;;;;;;;;iBAjCpB0N,GAAA,CAAA7H,aAAA,CAAApF,MAAA,CAAqB;QAAA,UAAAJ,EAAA,CAAAsN,eAAA;;;;;;;;QC/ClCtN,EAAA,CAAAC,cAAA,8BAAgD;QAWpCD,EAAA,CAAAuN,MAAA,GACF;;QAAAvN,EAAA,CAAAgB,YAAA,EAAkB;QAClBhB,EAAA,CAAAC,cAAA,gBAKC;QAFCD,EAAA,CAAAE,UAAA,mBAAAsN,wDAAApN,MAAA;UAAA,OAASiN,GAAA,CAAA9C,cAAA,CAAAnK,MAAA,CAAsB;QAAA,EAAC;QAGhCJ,EAAA,CAAAC,cAAA,eAAU;QAAAD,EAAA,CAAAuN,MAAA,yBAAiB;QAAAvN,EAAA,CAAAgB,YAAA,EAAW;QAG1ChB,EAAA,CAAAC,cAAA,cAA4B;QAC1BD,EAAA,CAAAwB,SAAA,2BAEoB;QACtBxB,EAAA,CAAAgB,YAAA,EAAM;QAIRhB,EAAA,CAAAC,cAAA,8BAOC;QAGKD,EAAA,CAAAuN,MAAA,IACF;;QAAAvN,EAAA,CAAAgB,YAAA,EAAkB;QAEpBhB,EAAA,CAAAwB,SAAA,eAAoE;QACtExB,EAAA,CAAAgB,YAAA,EAAsB;QAExBhB,EAAA,CAAAyN,UAAA,KAAAC,sCAAA,kBAYM;QACR1N,EAAA,CAAAgB,YAAA,EAAa;QACbhB,EAAA,CAAAC,cAAA,8BAAyE;QACvED,EAAA,CAAAwB,SAAA,eAAkD;QAEpDxB,EAAA,CAAAgB,YAAA,EAAqB;QAIvBhB,EAAA,CAAAyN,UAAA,KAAAE,uDAAA,mCAOuB;;;QAzEiC3N,EAAA,CAAAiB,SAAA,GAAqB;QAArBjB,EAAA,CAAAkB,UAAA,sBAAqB;QAIrElB,EAAA,CAAAiB,SAAA,GAAmB;QAAnBjB,EAAA,CAAAkB,UAAA,oBAAmB;QAMflB,EAAA,CAAAiB,SAAA,GACF;QADEjB,EAAA,CAAA4N,kBAAA,MAAA5N,EAAA,CAAA6N,WAAA,gCACF;QAEE7N,EAAA,CAAAiB,SAAA,GAA+B;QAA/BjB,EAAA,CAAAkB,UAAA,cAAAmM,GAAA,CAAAhM,iBAAA,CAA+B;QAU/BrB,EAAA,CAAAiB,SAAA,GAAiC;QAAjCjB,EAAA,CAAAkB,UAAA,kBAAAmM,GAAA,CAAAzJ,eAAA,CAAiC;QAWrC5D,EAAA,CAAAiB,SAAA,GAA8D;QAA9DjB,EAAA,CAAA8N,WAAA,eAAAT,GAAA,CAAA5J,kBAAA,wBAA8D,WAAA4J,GAAA,CAAA5J,kBAAA;QAH9DzD,EAAA,CAAAkB,UAAA,oBAAmB;QAQflB,EAAA,CAAAiB,SAAA,GACF;QADEjB,EAAA,CAAA4N,kBAAA,MAAA5N,EAAA,CAAA6N,WAAA,oCACF;QAKiB7N,EAAA,CAAAiB,SAAA,GAAuB;QAAvBjB,EAAA,CAAAkB,UAAA,SAAAmM,GAAA,CAAAhM,iBAAA,CAAuB;QAc5BrB,EAAA,CAAAiB,SAAA,GAAoD;QAApDjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA+N,eAAA,KAAAC,GAAA,EAAAX,GAAA,CAAA5J,kBAAA,EAAoD;QAEjDzD,EAAA,CAAAiB,SAAA,GAAmB;QAAnBjB,EAAA,CAAAkB,UAAA,YAAAmM,GAAA,CAAAxH,OAAA,CAAmB;QAOzC7F,EAAA,CAAAiB,SAAA,GAAwB;QAAxBjB,EAAA,CAAAkB,UAAA,SAAAmM,GAAA,CAAA5J,kBAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}