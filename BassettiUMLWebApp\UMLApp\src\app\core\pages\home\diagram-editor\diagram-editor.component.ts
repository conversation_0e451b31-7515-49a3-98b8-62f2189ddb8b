import {
  Component,
  effect,
  HostListener,
  On<PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import * as go from 'gojs';
import { Subscription } from 'rxjs';
import { DialogConfirmationComponent } from 'src/app/core/components/dialog-confirmation/dialog-confirmation.component';
import { LibraryTreeComponent } from 'src/app/core/components/library-tree/library-tree.component';
import { AccessService } from 'src/app/core/services/access/access.service';
import { AppService } from 'src/app/core/services/app.service';
import { CardinalityService } from 'src/app/core/services/cardinality/cardinality.service';
import { DiagramService } from 'src/app/core/services/diagram/diagram.service';
import { GojsService } from 'src/app/core/services/gojs/gojs.service';
import { LoaderService } from 'src/app/core/services/loader/loader.service';
import { NavbarService } from 'src/app/core/services/navbar/navbar.service';
import { ProjectService } from 'src/app/core/services/project/project.service';
import { PropertyService } from 'src/app/core/services/property/property.service';
import { TreeNodeService } from 'src/app/core/services/treeNode/tree-node.service';
import { VersionHistoryService } from 'src/app/core/services/versionHistory/version-history.service';
import { AttributeOption } from 'src/app/shared/model/attribute';
import { Diagram } from 'src/app/shared/model/diagram';
import { ConfirmDialogData } from 'src/app/shared/model/dialog';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType, ProjectDetails } from 'src/app/shared/model/project';
import { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: './diagram-editor.component.html',
  styleUrls: ['./diagram-editor.component.scss'],
})
export class DiagramEditorComponent implements OnInit, OnDestroy {
  private $ = go.GraphObject.make;
  isComponentPanelExpanded = false;
  isLibraryPanelExpanded = true;
  diagram!: go.Diagram;
  diagrams: Diagram[] = [];
  selectedDiagramId: number = -1;
  project!: ProjectDetails;
  projectId!: number;
  hasEditAccessOnly: boolean = false;
  propertyData!:
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsLinkNode
    | GojsFolderNode;

  attributeTypes: AttributeOption[] = [];
  private currentDiagram!: Diagram;
  private _downloadSub: Subscription | null = null;
  private _downloadAllSub: Subscription | null = null;
  private _currentProjectSub: Subscription | null = null;
  private _colorSelectionSub: Subscription | null = null;
  private _diagramSub: Subscription | null = null;
  private _currentProjDiagramsSub: Subscription | null = null;
  private _deleteDiagramSub: Subscription | null = null;
  private _propertyDataSub: Subscription | null = null;
  private _attributeTypeSub: Subscription | null = null;
  showVersionHistory = false;
  currentProjectDetails!: ProjectDetails;
  isLoading$ = this.loaderService.isLoading$;
  private _diagramLayoutSub: Subscription | null = null;
  @ViewChild(LibraryTreeComponent)
  libraryTreeComponent!: LibraryTreeComponent;
  selectedNodeTag: string = TreeNodeTag.Project;
  constructor(
    private diagramService: DiagramService,
    private projectService: ProjectService,
    private propertyService: PropertyService,
    private route: ActivatedRoute,
    private router: Router,
    private linkService: CardinalityService,
    private accessService: AccessService,
    private goJsService: GojsService,
    private diagramUtils: DiagramUtils,
    private loaderService: LoaderService,
    private treeNodeService: TreeNodeService,
    private navbarService: NavbarService,
    private dialog: MatDialog,
    private appService: AppService,
    private versionHistoryService: VersionHistoryService
  ) {
    effect(() => {
      this.showVersionHistory = this.navbarService.showVersionHistory();
    });
  }

  ngOnInit() {
    this.appService.setIsInitProject(true);
    go.Diagram.licenseKey = environment.licenseKey;
    this.projectId = parseInt(this.route.snapshot.paramMap.get('id') ?? '');

    // Check if a specific diagram ID is provided in the route
    const diagramIdParam = this.route.snapshot.paramMap.get('idDiagram');
    const initialDiagramId = diagramIdParam
      ? parseInt(diagramIdParam)
      : undefined;
    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${initialDiagramId}`;

    if (localStorage.getItem('reloaded') || localStorage.getItem('copyUrl')) {
      // Open the project with the specific diagram if provided
      this.projectService.openProject(this.projectId, true, initialDiagramId);
      this.linkService.initLinkTypes();
      localStorage.removeItem('reloaded');
      localStorage.removeItem('copyUrl');
    }

    this.configureDiagramProperties();
    this.initProject();
    this.accessService.accessTypeChanges().subscribe((response) => {
      this.hasEditAccessOnly = response != AccessType.Viewer;
    });
    this.subscribeToDiagramService();
    this._attributeTypeSub = this.diagramUtils
      .getAttributeTypes()
      .subscribe((options) => {
        this.attributeTypes = options;
      });
  }

  @HostListener('window:beforeunload', ['$event'])
  unloadHandler(event: BeforeUnloadEvent): void {
    event.preventDefault();
    this.projectService.handleProjectUnlock(this.projectId);
    localStorage.setItem('reloaded', 'true');
  }
  /**
   * Configures properties for the main diagram.
   * @memberof DiagramEditorComponent
   */
  configureDiagramProperties(): void {
    this.diagram = this.$(go.Diagram, 'diagramDiv', {
      initialAutoScale: go.AutoScale.Uniform,
      allowCopy: false,
      'animationManager.isEnabled': false,
      'linkingTool.isEnabled': true, // invoked explicitly by drawLink function, below
      'draggingTool.isGridSnapEnabled': true,
      'linkingTool.isUnconnectedLinkValid': false,
      'linkingTool.portGravity': 20,
      'relinkingTool.isUnconnectedLinkValid': false,
      'relinkingTool.portGravity': 10,
      'linkReshapingTool.handleArchetype': this.$(go.Shape, 'Diamond', {
        desiredSize: new go.Size(7, 7),
        fill: 'lightblue',
        stroke: '#0069B4',
      }),
      mouseDrop: (e: go.InputEvent) =>
        this.goJsService.handleDropCompletion(e, null),
      'commandHandler.archetypeGroupData': {
        isGroup: true,
        text: 'Group',
        horiz: false,
      },
      'undoManager.isEnabled': true,
      'relinkingTool.isEnabled': true,
    });
    this.diagram.grid = new go.Panel('Grid', {
      gridCellSize: new go.Size(10, 10),
    }).add(
      new go.Shape('LineH', {
        stroke: 'lightgray',
        strokeWidth: 0.5,
        interval: 1,
      }),
      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),
      new go.Shape('LineH', { stroke: 'gray', strokeWidth: 1.0, interval: 10 }),
      new go.Shape('LineV', {
        stroke: 'lightgray',
        strokeWidth: 0.5,
        interval: 1,
      }),
      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 0.5, interval: 5 }),
      new go.Shape('LineV', { stroke: 'gray', strokeWidth: 1.0, interval: 10 })
    );
  }
  private initProject() {
    this._diagramSub = this.diagramUtils
      .activeDiagramChanges()
      .subscribe((diagram) => {
        if (diagram && diagram.id) {
          this.currentDiagram = diagram;
          this.selectedDiagramId = this.currentDiagram.id!;
          debugger;
          this.diagramService.getDiagramDetails(diagram.id!);
        }
      });

    this._currentProjDiagramsSub = this.diagramUtils
      .currentProjectDiagramsChanges()
      .subscribe((diagrams) => {
        if (diagrams.length > 0) {
          this.diagrams = diagrams;

          // // If no active diagram is set yet, set the first diagram as active
          // // This handles the case when no specific diagram ID is provided in the URL
          // if (!this.currentDiagram && this.diagrams.length > 0) {
          //   // Check if a diagram ID is specified in the route
          //   const diagramIdParam =
          //     this.route.snapshot.paramMap.get('idDiagram');

          //   if (diagramIdParam) {
          //     // If a diagram ID is specified, find and set that diagram as active
          //     const diagramId = parseInt(diagramIdParam);
          //     const targetDiagram = this.diagrams.find(
          //       (d) => d.id === diagramId
          //     );

          //     if (targetDiagram) {
          //       debugger;
          //       this.diagramUtils.setActiveDiagram(targetDiagram);
          //     } else {
          //       debugger;
          //       // If the specified diagram doesn't exist, fall back to the first diagram
          //       this.diagramUtils.setActiveDiagram(this.diagrams[0]);
          //     }
          //   } else {
          //     debugger;
          //     // If no diagram ID is specified, set the first diagram as active
          //     this.diagramUtils.setActiveDiagram(this.diagrams[0]);
          //   }
          // }
        }
      });

    this._currentProjectSub = this.projectService
      .currentProjectChanges()
      .subscribe((project) => {
        if (project) {
          this.linkService.setProjectLinks(project);
          this.goJsService.initDiagram(this.diagram);
          this.diagramService.getPaletteDiagramDetails();
        }
      });
  }

  onDiagramSelectionChange(diagramId: number) {
    this.selectedDiagramId = diagramId;
    const activeDiagram = this.diagrams.find((d) => d.id === diagramId);
    if (activeDiagram) {
      this.diagramUtils.setActiveDiagram(activeDiagram);
    }
    this.propertyService.setPropertyData(null);

    // Update the URL to include the selected diagram ID without reloading the page
    this.router.navigate([`/editor/${this.projectId}/diagram/${diagramId}`], {
      replaceUrl: true, // Replace the current URL instead of adding a new history entry
    });
    this.selectedNodeTag = `atTag${GojsNodeCategory.Diagram}_${diagramId}`;
  }

  /**
   * Subscribes to various diagram service subjects to handle diagram actions.
   */
  private subscribeToDiagramService() {
    this._deleteDiagramSub = this.diagramService.deleteDiagramEvent.subscribe(
      () => {
        this.onDeleteDiagram();
      }
    );
    this._downloadSub = this.diagramService.downloadDiagramEvent.subscribe(
      () => {
        this.diagramService.initiateDiagramDownload(true, false);
      }
    );
    this._downloadAllSub =
      this.diagramService.downloadAllDiagramEvent.subscribe(
        (isForOnlyImage) => {
          this.diagramService.initiateDiagramDownload(false, isForOnlyImage);
          this.onDiagramSelectionChange(this.currentDiagram.id!);
        }
      );
    this._propertyDataSub = this.propertyService
      .propertyDataChanges()
      .subscribe((propertyData) => {
        if (propertyData) this.propertyData = propertyData;
      });
    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {
      if (this.diagram) this.diagram.allowDrop = diagrams.length > 0;
    });
  }

  onDeleteDiagram() {
    const dialogRef = this.dialog.open<
      DialogConfirmationComponent,
      ConfirmDialogData,
      boolean
    >(DialogConfirmationComponent, {
      width: '320px',
      data: {
        title: 'dialog.title',
        reject: 'dialog.no',
        confirm: 'dialog.yes',
      },
    });
    dialogRef.afterClosed().subscribe((isConfirm) => {
      if (isConfirm) {
        if (this.selectedDiagramId) {
          this.diagramService.deleteDiagram(this.selectedDiagramId);
          this.treeNodeService.deleteDiagram(
            `atTag${GojsNodeCategory.Diagram}_${this.selectedDiagramId}`
          );
          this.diagrams = this.diagrams.filter(
            (diagram: Diagram) => diagram.id !== this.selectedDiagramId
          );
          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);
          if (this.diagrams.length > 0) {
            this.currentDiagram = this.diagrams[0];
            this.selectedDiagramId = this.currentDiagram.id!;
            this.diagramUtils.setActiveDiagram(this.currentDiagram);

            // Update the URL to reflect the new active diagram
            this.router.navigate(
              [`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],
              {
                replaceUrl: true,
              }
            );
          } else {
            this.diagramUtils.setActiveDiagram(null);

            // If there are no diagrams left, navigate to a placeholder URL
            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {
              replaceUrl: true,
            });
          }
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.linkService.clearLinks();
    this.linkService.clearLinkToLinks();
    this.versionHistoryService.setSelectedVersion(null);
    this.projectService.handleProjectUnlock(this.projectId);
    if (this._downloadSub) this._downloadSub.unsubscribe();
    if (this._downloadAllSub) this._downloadAllSub.unsubscribe();
    if (this._colorSelectionSub) this._colorSelectionSub.unsubscribe();
    if (this._diagramSub) this._diagramSub.unsubscribe();
    if (this._currentProjDiagramsSub)
      this._currentProjDiagramsSub.unsubscribe();
    if (this._deleteDiagramSub) this._deleteDiagramSub.unsubscribe();
    if (this._propertyDataSub) this._propertyDataSub.unsubscribe();
    if (this._currentProjectSub) this._currentProjectSub.unsubscribe();
    if (this._attributeTypeSub) this._attributeTypeSub.unsubscribe();
    if (this._diagramLayoutSub) this._diagramLayoutSub.unsubscribe();
    this.propertyService.setPropertyData(null);
  }

  /**
   * Subscribes to color selection changes and updates the component state accordingly.
   */
  onColorPickerSelection(isClicked: boolean) {
    if (isClicked) {
      this.isComponentPanelExpanded = false;
      this.isLibraryPanelExpanded = false;
    }
  }

  /**
   * Subscribes to property data updates and handles updates to the diagram and palette.
   */
  onUpdateProperties(
    updatedNode:
      | GojsDiagramClassNode
      | GojsDiagramEnumerationNode
      | GojsDiagramAttributeNode
      | GojsDiagramLiteralNode
      | GojsLinkNode
      | GojsFolderNode,
    treeNode: TreeNode | null
  ): void {
    this.diagramService.getUpdatedProperties(updatedNode, treeNode);
  }

  onCreateFolder(event: Event) {
    event.stopPropagation();
    this.goJsService.createNewFolder('New Folder', this.projectId);
    if (
      this.libraryTreeComponent.dataSource &&
      this.libraryTreeComponent.dataSource.data.length > 0
    ) {
      this.libraryTreeComponent.treeControl.expand(
        this.libraryTreeComponent.dataSource.data[0]
      );
    }
  }

  // Function to open version history
  openVersionHistory() {
    this.navbarService.setShowVersionHistory(true);
    this.accessService.setProjectAccess(AccessType.Viewer);
  }

  // Function to close version history
  closeVersionHistory() {
    this.navbarService.setShowVersionHistory(false);
    // this.accessService.setProjectAccess(this.project.accessType);
  }
}
