import { Injectable } from '@angular/core';
import { ProjectFolder } from 'src/app/shared/model/class';
import { GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import {
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';
import { DiagramWrapperCategory } from 'src/app/shared/utils/constants';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { FolderService } from '../../folder/folder.service';
import { PropertyService } from '../../property/property.service';
import { SnackBarService } from '../../snackbar/snack-bar.service';
import { TreeNodeService } from '../../treeNode/tree-node.service';
import { GojsCardinalityService } from '../gojsCardinality/gojs-cardinality.service';
import { GojsCommonService } from '../gojsCommon/gojs-common.service';
@Injectable({
  providedIn: 'root',
})
export class GojsFolderService {
  private _gojsDiagram!: go.Diagram;
  private diagrams: Diagram[] = [];
  constructor(
    private folderService: FolderService,
    private snackBarService: SnackBarService,
    private treeNodeService: TreeNodeService,
    private gojsCommonService: GojsCommonService,
    private gojsCardinalityService: GojsCardinalityService,
    private diagramUtils: DiagramUtils,
    private propertyService: PropertyService
  ) {
    this.gojsCommonService.gojsDiagramChanges().subscribe((diagram) => {
      if (diagram) this._gojsDiagram = diagram;
    });
    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {
      this.diagrams = diagrams;
    });
  }

  /**
   * Handles the creation of a new folder in the GoJS palette diagram.
   *
   * This method adds a new folder node to the GoJS palette diagram and makes a service call to create the folder in the backend.
   * After the folder is created successfully, it updates the node with the new folder's ID and selects the node in the palette.
   *
   * @param idProject - The ID of the project to which the new folder will belong.
   * @param goJsPaletteDiagram - The GoJS palette diagram where the new folder will be added.
   * @param hasEditAccess - A boolean indicating whether the user has edit access to the folder.
   */
  onCreateNewFolder(
    name: string,
    idProject: number,
    hasEditAccess: boolean,
    parentTag?: string
  ) {
    this.folderService
      .createNewFolder({
        name: name,
        icon: GoJsNodeIcon.Folder,
        idProject: idProject,
      } as ProjectFolder)
      .subscribe((response) => {
        const newNodeData: GojsFolderNode = {
          name: response.name,
          icon: GoJsNodeIcon.Folder,
          allowTopLevelDrops: true,
          editable: hasEditAccess,
          // isPalette: true,
          isGroup: false,
          isFolder: true,
          category: GojsNodeCategory.Folder,
          showTablePanel: false,
          idFolder: response.id!,
          key: 0,
        };
        this.treeNodeService.addGroupNodeInTree({
          name: response.name,
          category: GojsNodeCategory.Folder,
          tag: `atTag${GojsNodeCategory.Folder}_${response.id}`,
          children: [],
          icon: GoJsNodeIcon.Folder,
          data: newNodeData,
          parentTag: parentTag ?? TreeNodeTag.Project,
          isDraggable: true,
          supportingNodes: [
            GojsNodeCategory.Class,
            GojsNodeCategory.AssociativeClass,
            GojsNodeCategory.Enumeration,
            GojsNodeCategory.Folder,
            GojsNodeCategory.Diagram,
          ],
        });
      });
  }

  /**
   * Deletes a selected folder and its associated data from both the GoJS palette and the main diagram.
   *
   * This method removes the folder from the palette and main diagram, and deletes the folder data from the backend.
   *
   * @param folderData - The folder node data to be deleted.
   * @param goJsPaletteDiagram - The GoJS palette diagram from which the folder will be removed.
   * @param goDiagram - The main GoJS diagram from which the folder and its children will be removed.
   */
  deleteSelectedFolder(nodeData: TreeNode[]): void {
    this.folderService.deleteFolders(
      nodeData.map((folder) => (folder['data'] as GojsFolderNode)?.idFolder)
    );
    nodeData.forEach((folderData) => {
      if (folderData.data) {
        this.removeDiagrams(folderData);
        const children =
          this.treeNodeService.getClassesEnumsFromFolder(folderData);
        const links = new Set<go.ObjectData>();
        children.forEach((childNode) => {
          if (childNode.data) {
            const diagramChildNodes =
              this._gojsDiagram.model.nodeDataArray.filter((node) => {
                if (
                  this.gojsCommonService.isGojsDiagramClassNode(childNode.data)
                ) {
                  return (
                    node['category'] == childNode['category'] &&
                    node['idTemplateClass'] ==
                      (childNode.data as GojsDiagramClassNode).idTemplateClass
                  );
                } else if (
                  this.gojsCommonService.isGojsDiagramEnumerationNode(
                    childNode.data
                  )
                ) {
                  return (
                    node['category'] == childNode['category'] &&
                    node['idTemplateEnumeration'] ==
                      (childNode.data as GojsDiagramEnumerationNode)
                        .idTemplateEnumeration
                  );
                } else {
                  return;
                }
              });
            if (
              this.gojsCommonService.isGojsDiagramEnumerationNode(
                childNode.data
              )
            )
              this.diagramUtils.removeAttributeType(
                (
                  childNode.data as GojsDiagramEnumerationNode
                ).idTemplateEnumeration.toString()
              );
            this._gojsDiagram.model.removeNodeDataCollection(diagramChildNodes);
          }
          if (this.gojsCommonService.isGojsDiagramClassNode(childNode.data))
            (
              this._gojsDiagram.model as go.GraphLinksModel
            ).linkDataArray.forEach((linkData) => {
              if (
                linkData['idSourceTempClass'] ==
                  (childNode.data as GojsDiagramClassNode).idTemplateClass ||
                linkData['idDestinationTempClass'] ==
                  (childNode.data as GojsDiagramClassNode).idTemplateClass
              ) {
                links.add(linkData);
              }
            });
        });
        links.forEach((linkData) => {
          this.gojsCardinalityService.removeLinkFromCurrentDiagram(
            linkData as GojsLinkNode,
            this._gojsDiagram,
            true
          );
        });
        if (this.diagrams.length > 0)
          this.snackBarService.openSnackbar('snackBar.deleteFolderMsg');
      }
    });
  }

  private removeDiagrams(node: TreeNode): void {
    // Helper function to collect and remove diagrams recursively
    const collectAndRemoveDiagrams = (node: TreeNode): void => {
      // Check for diagram wrappers in the current node
      const diagramWrapper = node.children.find(
        (child) => child.category === DiagramWrapperCategory
      );
      // If a diagram wrapper exists, remove its diagrams from `this.diagrams`
      if (diagramWrapper && Array.isArray(diagramWrapper.children)) {
        const diagramsToRemove = diagramWrapper.children.map(
          (child: TreeNode) => child.data
        ) as Diagram[];

        // Remove diagrams from `this.diagrams` during collection
        this.diagrams = this.diagrams.filter(
          (diagram) =>
            !diagramsToRemove.some(
              (diagramToRemove) => diagramToRemove.id === diagram.id
            )
        );
      }
      // Recursively process folder nodes
      for (const child of node.children) {
        if (child.category === GojsNodeCategory.Folder) {
          collectAndRemoveDiagrams(child);
        }
      }
    };

    // Start the collection and removal process
    collectAndRemoveDiagrams(node);
    this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);

    this.diagramUtils.setActiveDiagram(
      this.diagrams.length > 0 ? this.diagrams[0] : null
    );
  }
  /**
   * Updates the folder node in the GoJS palette diagram with the new data.
   * @param {GojsFolderNode} updatedNode The node to update
   * @param {go.Palette} palette The GoJS palette diagram containing the node
   * @memberOf GojsFolderService
   */
  updateFolderNode(updatedNode: GojsFolderNode, treeNode: TreeNode) {
    this.treeNodeService.editGroupTreeNode({
      ...treeNode,
      name: updatedNode.name,
      data: updatedNode,
    });
  }
  handleFolderEdit(folderNode: GojsFolderNode, treeNode: TreeNode) {
    this.folderService
      .updateFolder({
        id: folderNode.idFolder,
        name: treeNode.name,
        icon: folderNode.icon,
      })
      .subscribe(() => {
        this.updateFolderNode(
          { ...folderNode, name: treeNode.name },
          {
            ...treeNode,
            data: {
              ...treeNode.data,
              name: treeNode.name,
            } as GojsFolderNode,
          }
        );
        this.propertyService.setPropertyData(folderNode);
      });
  }
}
