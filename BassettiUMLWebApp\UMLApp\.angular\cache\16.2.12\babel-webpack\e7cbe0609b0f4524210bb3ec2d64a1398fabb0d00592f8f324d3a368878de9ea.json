{"ast": null, "code": "import _asyncToGenerator from \"D:/GitHub/Bassetti/devint-BASSETTI-GROUP-APP/BassettiUMLWebApp/UMLApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as go from 'gojs';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../class/class.service\";\nimport * as i2 from \"src/app/shared/utils/diagram-utils\";\nimport * as i3 from \"../gojsCommon/gojs-common.service\";\nimport * as i4 from \"../../property/property.service\";\nimport * as i5 from \"src/app/shared/services/shared.service\";\nimport * as i6 from \"../gojsCardinality/gojs-cardinality.service\";\nimport * as i7 from \"../gojsAttribute/gojs-attribute.service\";\nimport * as i8 from \"../../treeNode/tree-node.service\";\nimport * as i9 from \"../../data-format/data-format.service\";\nimport * as i10 from \"../../project/project.service\";\nimport * as i11 from \"../../access/access.service\";\nexport class GojsClassService {\n  constructor(classService, diagramUtils, goJsCommonService, propertyService, sharedService, goJsCardinalityService, goJsAttributeService, treeNodeService, dataFormatService, projectService, accessService) {\n    this.classService = classService;\n    this.diagramUtils = diagramUtils;\n    this.goJsCommonService = goJsCommonService;\n    this.propertyService = propertyService;\n    this.sharedService = sharedService;\n    this.goJsCardinalityService = goJsCardinalityService;\n    this.goJsAttributeService = goJsAttributeService;\n    this.treeNodeService = treeNodeService;\n    this.dataFormatService = dataFormatService;\n    this.projectService = projectService;\n    this.accessService = accessService;\n    this._hasEditAccessOnly = false;\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n    this.goJsCommonService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this._gojsDiagram = diagram;\n    });\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) this._currentProject = project;\n    });\n    this.accessService.accessTypeChanges().subscribe(response => {\n      this._hasEditAccessOnly = response != AccessType.Viewer;\n    });\n  }\n  /**\n   * For drop the class and handle create or update class\n   * @param {GojsDiagramClassNode} classData\n   * @param {go.InputEvent} event\n   * @param {go.Diagram} diagram\n   * @memberof GojsClassService\n   */\n  handleClassCreationOrUpdate(classData, diagram, isFromLibrary, event) {\n    if (!classData?.id) this.createClass(classData, diagram, isFromLibrary, event);else this.updateExistingClass(classData, isFromLibrary);\n  }\n  /**\n   * Creates a new class on the diagram.\n   * @param classData - The data of the class to be created.\n   * @param event - The GoJS input event.\n   */\n  createClass(classData, diagram, isFromLibrary, event) {\n    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();\n    const isAssociative = classData.category == GojsNodeCategory.AssociativeClass ? true : false;\n    let classObj;\n    if (parentNode && parentNode.category == GojsNodeCategory.Folder && !isFromLibrary) {\n      classObj = {\n        ...this.mapClassData(classData, isFromLibrary),\n        idFolder: parentNode['data'].idFolder,\n        isAssociative\n      };\n    } else {\n      const treeNodeData = this.treeNodeService.findNodeByTag(classData.treeNodeTag);\n      classObj = {\n        ...this.mapClassData(classData, isFromLibrary),\n        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag),\n        isAssociative\n      };\n    }\n    this.classService.createNewClass(classObj).subscribe({\n      next: value => {\n        this.addClassToDiagram(value, diagram, classData, isFromLibrary);\n        this.handleAdditionalClassCreationLogic({\n          attributes: [],\n          ...value.templateClass,\n          links: [],\n          isAssociative\n        }, classData, diagram, isFromLibrary, parentNode);\n      },\n      error: () => {\n        event?.diagram.currentTool.doCancel();\n      }\n    });\n  }\n  /**\n   * Maps class data to the DTO required by the class service.\n   * @param classData - The data of the class.\n   * @returns The mapped class DTO.\n   */\n  mapClassData(classData, isFromLibrary) {\n    const classObj = {\n      name: classData.name,\n      key: classData.key,\n      idDiagram: this.currentDiagram.id,\n      property: {\n        position: classData.position,\n        height: classData.size.height,\n        width: classData.size.width,\n        icon: classData.icon,\n        color: classData.color\n      },\n      description: classData.description,\n      tag: classData.tag,\n      volumetry: classData.volumetry,\n      isAssociative: classData.isAssociative\n    };\n    return isFromLibrary ? {\n      ...classObj,\n      idTemplateClass: classData.idTemplateClass,\n      key: +this.sharedService.generateUniqueKey()\n    } : classObj;\n  }\n  formatDiagramClassNode(classObj) {\n    return {\n      idTemplateClass: classObj.idTemplateClass,\n      idDiagram: classObj.idDiagram,\n      key: `${classObj.key}`,\n      id: classObj.id,\n      color: classObj.property?.color,\n      icon: classObj.property?.icon,\n      name: classObj.name,\n      category: classObj.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n      size: classObj.property ? new go.Size(classObj.property.width, classObj.property.height) : new go.Size(150, 100),\n      isGroup: true,\n      position: classObj.property?.position,\n      supportingLevels: [GojsNodeCategory.Attribute, GojsNodeCategory.Operation],\n      isHighlighted: true,\n      allowTopLevelDrops: true,\n      editable: this._hasEditAccessOnly,\n      description: classObj.description,\n      tag: classObj.tag,\n      volumetry: classObj.volumetry,\n      items: this.goJsAttributeService.formatAttribute(classObj.attributes, classObj.id, this._hasEditAccessOnly) ?? [],\n      showTablePanel: true,\n      treeNodeTag: `atTag${GojsNodeCategory.Class}_${classObj.idTemplateClass}`,\n      isAssociative: classObj.isAssociative\n    };\n  }\n  /**\n   * Adds the newly created class to the diagram.\n   * @param value - The data of the created class.\n   * @param classData - The data of the class.\n   */\n  addClassToDiagram(value, gojsDiagram, classData, isFromLibrary) {\n    if (isFromLibrary) {\n      gojsDiagram.model.addNodeData(classData);\n    }\n    const diagramData = this.diagramUtils.getObjectDataByKey(gojsDiagram, classData.key);\n    if (diagramData) {\n      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {\n        id: value.id,\n        idTemplateClass: value.templateClass.id,\n        isAssociative: value.isAssociative,\n        treeNodeTag: `atTag${GojsNodeCategory.Class}_${value.templateClass.id}`\n      });\n    }\n  }\n  /**\n   * Handles additional logic after creating a class.\n   * @param value - The data of the created class.\n   * @param classData - The data of the class.\n   */\n  handleAdditionalClassCreationLogic(tempCls, classData, diagram, isFromLibrary, parentNode) {\n    if (!isFromLibrary) {\n      this.treeNodeService.addGroupNodeInTree({\n        name: tempCls.name,\n        category: tempCls.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n        tag: `atTag${GojsNodeCategory.Class}_${tempCls.id}`,\n        children: [],\n        icon: tempCls.isAssociative ? GoJsNodeIcon.Associative : GoJsNodeIcon.Class,\n        data: this.dataFormatService.formatDiagramClassNode(tempCls),\n        parentTag: parentNode?.category !== GojsNodeCategory.Project ? parentNode?.tag : this.treeNodeService.getWrapperParentTag(TreeNodeTag.ClassWrapper),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.Attribute, GojsNodeCategory.Operation]\n      });\n    } else {\n      this.goJsCardinalityService.createLinksForPaletteClass(classData.idTemplateClass, diagram);\n    }\n  }\n  addAssociativeClassAutomatically(linkToLink, diagram, isFromLibrary) {\n    if (linkToLink) {\n      const treeNodeData = this.treeNodeService.findNodeByTag(`atTag${GojsNodeCategory.Class}_${linkToLink.idAssociativeClass}`);\n      if (treeNodeData && treeNodeData.data) {\n        this.createClass(treeNodeData.data, diagram, isFromLibrary);\n      }\n    }\n  }\n  /**\n   * Updates an existing class on the diagram.\n   * @param classData - The data of the class to be updated.\n   */\n  updateExistingClass(classData, isFromLibrary) {\n    this.classService.updateClass({\n      ...this.mapClassData(classData, isFromLibrary),\n      id: classData.id\n    });\n  }\n  updateTemplateClass(classNodeData, goJsDiagram) {\n    const templateClass = {\n      id: classNodeData.idTemplateClass,\n      name: classNodeData.name,\n      key: classNodeData.key,\n      color: classNodeData.color,\n      icon: classNodeData.icon,\n      description: classNodeData.description,\n      tag: classNodeData.tag,\n      volumetry: classNodeData.volumetry,\n      isAssociative: classNodeData.category == GojsNodeCategory.AssociativeClass ? true : false\n    };\n    this.classService.updateTemplateClass(templateClass, this.currentDiagram.id).subscribe(cls => {\n      this.goJsCommonService.updateNodeDataProperties(goJsDiagram.model, node => node['idTemplateClass'] === templateClass.id, {\n        name: cls.name\n      });\n      const treeNode = this.treeNodeService.findNodeByTag(classNodeData.treeNodeTag);\n      if (treeNode) this.treeNodeService.editGroupTreeNode({\n        ...treeNode,\n        name: cls.name,\n        data: {\n          ...classNodeData,\n          items: []\n        }\n      });\n    });\n  }\n  deleteTempClass(classData) {\n    this.classService.deleteTemplateClasses(classData.map(classObj => classObj.idTemplateClass));\n    classData.forEach(cls => {\n      this.goJsCommonService.removeGroupNodeWithItems(cls.idTemplateClass, cls.category, this._gojsDiagram);\n      if (cls.category == GojsNodeCategory.AssociativeClass) {\n        this.goJsCommonService.removeLinkToLink(this._gojsDiagram, link => link.idAssociativeClass === cls.idTemplateClass && link.category == GojsNodeCategory.LinkToLink);\n      }\n      this.goJsCommonService.removeAssociationLink(this._gojsDiagram, cls);\n    });\n  }\n  /**\n   *  Format the Class Data to match the structure required by GoJS for displaying the elements .\n   * @param {IClassDTO[]} classes  are the list of Classes returned\n   * @return {*}\n   * @memberof DiagramEditorComponent\n   */\n  formatClassData(classes, linkHistories, diagramId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const nodeDataArray = [];\n      const formattedLinkData = _this.goJsCardinalityService.formatLinkData(classes, linkHistories, diagramId);\n      const formatLinkLabelData = formattedLinkData.map(link => ({\n        key: `${link.id}_${GojsNodeCategory.LinkLabel}`,\n        category: GojsNodeCategory.LinkLabel,\n        idLink: link.id,\n        editable: link.editable\n      }));\n      const linkToLinkNodes = yield _this.goJsCardinalityService.generateLinkToLinkNodes(formattedLinkData, classes);\n      classes.forEach(classObj => {\n        nodeDataArray.push(_this.formatDiagramClassNode(classObj));\n      });\n      return {\n        nodeDataArray,\n        linkLabelData: formatLinkLabelData,\n        linkDataArray: [...formattedLinkData, ...linkToLinkNodes]\n      };\n    })();\n  }\n  /**\n   *\n   * For updating the class in library tree\n   * @param {GojsDiagramClassNode} response\n   * @param {go.Diagram} gojsDiagram\n   * @param {TreeNode} treeNode\n   * @memberof GojsClassService\n   */\n  handleClassUpdateInProperty(response, gojsDiagram, treeNode) {\n    let diagramNode;\n    diagramNode = gojsDiagram.model.nodeDataArray.filter(item => item['idTemplateClass'] == response.idTemplateClass);\n    this.treeNodeService.editGroupTreeNode({\n      ...treeNode,\n      name: response.name,\n      data: {\n        ...response,\n        id: response.idTemplateClass\n      },\n      tag: response.treeNodeTag ?? treeNode.tag\n    });\n    if (response && response.id && diagramNode) {\n      const properties = {\n        name: response.name,\n        color: response.color,\n        description: response.description,\n        tag: response.tag,\n        volumetry: response.volumetry\n      };\n      this.goJsCommonService.commitGroupNodeData(diagramNode, properties, gojsDiagram);\n    }\n  }\n  /**\n   * Handles the editing of a class name within the library.\n   * Updates the name of the class in both the main diagram and the palette.\n   * @param classNode - The class node being edited.\n   * @param TreeNode - The node for library.\n   */\n  handleEditClassNameInLibrary(classNode, treeNode) {\n    this.classService.updateTemplateClass({\n      id: classNode.idTemplateClass,\n      key: classNode.key?.toString().split('_')[0],\n      icon: classNode.icon,\n      color: classNode.color,\n      name: treeNode.name,\n      isAssociative: classNode.isAssociative\n    }, this.currentDiagram.id).subscribe(() => {\n      this.goJsCommonService.updateNodeDataProperties(this._gojsDiagram.model, node => node['idTemplateClass'] == classNode['idTemplateClass'], {\n        name: treeNode.name\n      });\n      this.treeNodeService.editGroupTreeNode({\n        ...treeNode,\n        data: {\n          ...treeNode.data,\n          name: treeNode.name\n        }\n      });\n      this.propertyService.setPropertyData({\n        ...classNode,\n        name: treeNode.data?.name\n      });\n    });\n  }\n  handleClassCreationFromLibrary(className, classWrapperNode, isAssociative) {\n    const parentNode = this.treeNodeService.findNodeByTag(classWrapperNode.category === GojsNodeCategory.Folder ? classWrapperNode.tag : classWrapperNode.parentTag);\n    const classObj = {\n      name: className,\n      key: 0,\n      idProject: this._currentProject.id,\n      icon: GoJsNodeIcon.Class,\n      color: 'rgba(128,128,128,0.5)',\n      description: '',\n      tag: '',\n      volumetry: '',\n      idFolder: parentNode && parentNode.category == GojsNodeCategory.Folder ? parentNode.data?.idFolder : 0,\n      isAssociative: isAssociative\n    };\n    this.classService.createNewTemplateClass(classObj).subscribe(createdClass => {\n      this.treeNodeService.addGroupNodeInTree({\n        name: createdClass.name,\n        children: [],\n        category: createdClass.isAssociative ? GojsNodeCategory.AssociativeClass : GojsNodeCategory.Class,\n        icon: createdClass.isAssociative ? GoJsNodeIcon.Associative : GoJsNodeIcon.Class,\n        tag: `atTag${GojsNodeCategory.Class}_${createdClass.id}`,\n        parentTag: parentNode && parentNode.category == GojsNodeCategory.Folder ? parentNode.tag : `${TreeNodeTag.ClassWrapper}_${TreeNodeTag.Project}`,\n        data: this.dataFormatService.formatDiagramClassNode(createdClass),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.Operation, GojsNodeCategory.Attribute]\n      });\n    });\n  }\n  static #_ = this.ɵfac = function GojsClassService_Factory(t) {\n    return new (t || GojsClassService)(i0.ɵɵinject(i1.ClassService), i0.ɵɵinject(i2.DiagramUtils), i0.ɵɵinject(i3.GojsCommonService), i0.ɵɵinject(i4.PropertyService), i0.ɵɵinject(i5.SharedService), i0.ɵɵinject(i6.GojsCardinalityService), i0.ɵɵinject(i7.GojsAttributeService), i0.ɵɵinject(i8.TreeNodeService), i0.ɵɵinject(i9.DataFormatService), i0.ɵɵinject(i10.ProjectService), i0.ɵɵinject(i11.AccessService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsClassService,\n    factory: GojsClassService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["go", "GoJsNodeIcon", "GojsNodeCategory", "AccessType", "TreeNodeTag", "GojsClassService", "constructor", "classService", "diagramUtils", "goJsCommonService", "propertyService", "sharedService", "goJsCardinalityService", "goJsAttributeService", "treeNodeService", "dataFormatService", "projectService", "accessService", "_hasEditAccessOnly", "activeDiagramChanges", "subscribe", "diagram", "currentDiagram", "gojsDiagramChanges", "_gojsDiagram", "currentProjectChanges", "project", "_currentProject", "accessTypeChanges", "response", "Viewer", "handleClassCreationOrUpdate", "classData", "isFromLibrary", "event", "id", "createClass", "updateExistingClass", "parentNode", "findCurrentDiagramParentNode", "isAssociative", "category", "AssociativeClass", "classObj", "Folder", "mapClassData", "idFolder", "treeNodeData", "findNodeByTag", "treeNodeTag", "getIdFromTag", "parentTag", "createNewClass", "next", "value", "addClassToDiagram", "handleAdditionalClassCreationLogic", "attributes", "templateClass", "links", "error", "currentTool", "doCancel", "name", "key", "idDiagram", "property", "position", "height", "size", "width", "icon", "color", "description", "tag", "volumetry", "idTemplateClass", "generateUniqueKey", "formatDiagramClassNode", "Class", "Size", "isGroup", "supportingLevels", "Attribute", "Operation", "isHighlighted", "allowTopLevelDrops", "editable", "items", "formatAttribute", "showTablePanel", "gojsDiagram", "model", "addNodeData", "diagramData", "getObjectDataByKey", "setDataProperties", "tempCls", "addGroupNodeInTree", "children", "Associative", "data", "Project", "getWrapperParentTag", "ClassWrapper", "isDraggable", "supportingNodes", "createLinksForPaletteClass", "addAssociativeClassAutomatically", "linkToLink", "idAssociativeClass", "updateClass", "updateTemplateClass", "classNodeData", "goJsDiagram", "cls", "updateNodeDataProperties", "node", "treeNode", "editGroupTreeNode", "deleteTempClass", "deleteTemplateClasses", "map", "for<PERSON>ach", "removeGroupNodeWithItems", "removeLinkToLink", "link", "LinkToLink", "removeAssociationLink", "formatClassData", "classes", "linkHistories", "diagramId", "_this", "_asyncToGenerator", "nodeDataArray", "formattedLinkData", "formatLinkData", "formatLinkLabelData", "LinkLabel", "idLink", "linkToLinkNodes", "generateLinkToLinkNodes", "push", "linkLabelData", "linkDataArray", "handleClassUpdateInProperty", "diagramNode", "filter", "item", "properties", "commitGroupNodeData", "handleEditClassNameInLibrary", "classNode", "toString", "split", "setPropertyData", "handleClassCreationFromLibrary", "className", "classWrapperNode", "idProject", "createNewTemplateClass", "createdClass", "_", "i0", "ɵɵinject", "i1", "ClassService", "i2", "DiagramUtils", "i3", "GojsCommonService", "i4", "PropertyService", "i5", "SharedService", "i6", "GojsCardinalityService", "i7", "GojsAttributeService", "i8", "TreeNodeService", "i9", "DataFormatService", "i10", "ProjectService", "i11", "AccessService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojsClass\\gojs-class.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport * as go from 'gojs';\r\nimport { DeletedLink, LinkToLink } from 'src/app/shared/model/cardinality';\r\nimport {\r\n  ClassEntity,\r\n  ClassEntityDTO,\r\n  CreatedClassEntityDTO,\r\n  TemplateClass,\r\n  TemplateClassCreation,\r\n  TemplateDTO,\r\n} from 'src/app/shared/model/class';\r\nimport { FormattedClassData, GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport {\r\n  GojsDiagramClassNode,\r\n  GojsFolderNode,\r\n  GojsNodeCategory,\r\n  UpdateProperties,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType, ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport { SharedService } from 'src/app/shared/services/shared.service';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { AccessService } from '../../access/access.service';\r\nimport { ClassService } from '../../class/class.service';\r\nimport { DataFormatService } from '../../data-format/data-format.service';\r\nimport { ProjectService } from '../../project/project.service';\r\nimport { PropertyService } from '../../property/property.service';\r\nimport { TreeNodeService } from '../../treeNode/tree-node.service';\r\nimport { GojsAttributeService } from '../gojsAttribute/gojs-attribute.service';\r\nimport { GojsCardinalityService } from '../gojsCardinality/gojs-cardinality.service';\r\nimport { GojsCommonService } from '../gojsCommon/gojs-common.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsClassService {\r\n  private currentDiagram!: Diagram;\r\n  private _gojsDiagram!: go.Diagram;\r\n  private _currentProject!: ProjectDetails;\r\n  private _hasEditAccessOnly: boolean = false;\r\n  constructor(\r\n    private classService: ClassService,\r\n    private diagramUtils: DiagramUtils,\r\n    private goJsCommonService: GojsCommonService,\r\n    private propertyService: PropertyService,\r\n    private sharedService: SharedService,\r\n    private goJsCardinalityService: GojsCardinalityService,\r\n    private goJsAttributeService: GojsAttributeService,\r\n    private treeNodeService: TreeNodeService,\r\n    private dataFormatService: DataFormatService,\r\n    private projectService: ProjectService,\r\n    private accessService: AccessService\r\n  ) {\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this._gojsDiagram = diagram;\r\n    });\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project) this._currentProject = project;\r\n    });\r\n    this.accessService.accessTypeChanges().subscribe((response) => {\r\n      this._hasEditAccessOnly = response != AccessType.Viewer;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * For drop the class and handle create or update class\r\n   * @param {GojsDiagramClassNode} classData\r\n   * @param {go.InputEvent} event\r\n   * @param {go.Diagram} diagram\r\n   * @memberof GojsClassService\r\n   */\r\n  handleClassCreationOrUpdate(\r\n    classData: GojsDiagramClassNode,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean,\r\n    event?: go.InputEvent\r\n  ) {\r\n    if (!classData?.id)\r\n      this.createClass(classData, diagram, isFromLibrary, event);\r\n    else this.updateExistingClass(classData, isFromLibrary);\r\n  }\r\n\r\n  /**\r\n   * Creates a new class on the diagram.\r\n   * @param classData - The data of the class to be created.\r\n   * @param event - The GoJS input event.\r\n   */\r\n  private createClass(\r\n    classData: GojsDiagramClassNode,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean,\r\n    event?: go.InputEvent\r\n  ) {\r\n    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();\r\n    const isAssociative =\r\n      classData.category == GojsNodeCategory.AssociativeClass ? true : false;\r\n    let classObj: ClassEntity;\r\n    if (\r\n      parentNode &&\r\n      parentNode.category == GojsNodeCategory.Folder &&\r\n      !isFromLibrary\r\n    ) {\r\n      classObj = {\r\n        ...this.mapClassData(classData, isFromLibrary),\r\n        idFolder: (parentNode['data'] as GojsFolderNode).idFolder,\r\n        isAssociative,\r\n      };\r\n    } else {\r\n      const treeNodeData = this.treeNodeService.findNodeByTag(\r\n        classData.treeNodeTag\r\n      );\r\n      classObj = {\r\n        ...this.mapClassData(classData, isFromLibrary),\r\n        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag!),\r\n        isAssociative,\r\n      };\r\n    }\r\n    this.classService.createNewClass(classObj).subscribe({\r\n      next: (value) => {\r\n        this.addClassToDiagram(value, diagram, classData, isFromLibrary);\r\n        this.handleAdditionalClassCreationLogic(\r\n          {\r\n            attributes: [],\r\n            ...value.templateClass,\r\n            links: [],\r\n            isAssociative,\r\n          },\r\n          classData,\r\n          diagram,\r\n          isFromLibrary,\r\n          parentNode!\r\n        );\r\n      },\r\n      error: () => {\r\n        event?.diagram.currentTool.doCancel();\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Maps class data to the DTO required by the class service.\r\n   * @param classData - The data of the class.\r\n   * @returns The mapped class DTO.\r\n   */\r\n  private mapClassData(\r\n    classData: GojsDiagramClassNode,\r\n    isFromLibrary: boolean\r\n  ): ClassEntity {\r\n    const classObj: ClassEntity = {\r\n      name: classData.name,\r\n      key: classData.key,\r\n      idDiagram: this.currentDiagram.id!,\r\n      property: {\r\n        position: classData.position!,\r\n        height: classData.size.height,\r\n        width: classData.size.width,\r\n        icon: classData.icon,\r\n        color: classData.color,\r\n      },\r\n      description: classData.description,\r\n      tag: classData.tag,\r\n      volumetry: classData.volumetry,\r\n      isAssociative: classData.isAssociative,\r\n    };\r\n    return isFromLibrary\r\n      ? {\r\n          ...classObj,\r\n          idTemplateClass: classData.idTemplateClass,\r\n          key: +this.sharedService.generateUniqueKey(),\r\n        }\r\n      : classObj;\r\n  }\r\n\r\n  private formatDiagramClassNode(classObj: ClassEntityDTO) {\r\n    return {\r\n      idTemplateClass: classObj.idTemplateClass!,\r\n      idDiagram: classObj.idDiagram,\r\n      key: `${classObj.key}`,\r\n      id: classObj.id,\r\n      color: classObj.property?.color,\r\n      icon: classObj.property?.icon,\r\n      name: classObj.name,\r\n      category: classObj.isAssociative\r\n        ? GojsNodeCategory.AssociativeClass\r\n        : GojsNodeCategory.Class,\r\n      size: classObj.property\r\n        ? new go.Size(classObj.property.width, classObj.property.height)\r\n        : new go.Size(150, 100),\r\n      isGroup: true,\r\n      position: classObj.property?.position,\r\n      supportingLevels: [\r\n        GojsNodeCategory.Attribute,\r\n        GojsNodeCategory.Operation,\r\n      ],\r\n      isHighlighted: true,\r\n      allowTopLevelDrops: true,\r\n      editable: this._hasEditAccessOnly,\r\n      description: classObj.description,\r\n      tag: classObj.tag,\r\n      volumetry: classObj.volumetry,\r\n      items:\r\n        this.goJsAttributeService.formatAttribute(\r\n          classObj.attributes,\r\n          classObj.id!,\r\n          this._hasEditAccessOnly\r\n        ) ?? [],\r\n      showTablePanel: true,\r\n      treeNodeTag: `atTag${GojsNodeCategory.Class}_${classObj.idTemplateClass}`,\r\n      isAssociative: classObj.isAssociative,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Adds the newly created class to the diagram.\r\n   * @param value - The data of the created class.\r\n   * @param classData - The data of the class.\r\n   */\r\n  private addClassToDiagram(\r\n    value: CreatedClassEntityDTO,\r\n    gojsDiagram: go.Diagram,\r\n    classData: GojsDiagramClassNode,\r\n    isFromLibrary: boolean\r\n  ) {\r\n    if (isFromLibrary) {\r\n      gojsDiagram.model.addNodeData(classData);\r\n    }\r\n    const diagramData = this.diagramUtils.getObjectDataByKey(\r\n      gojsDiagram,\r\n      classData.key\r\n    );\r\n    if (diagramData) {\r\n      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {\r\n        id: value.id,\r\n        idTemplateClass: value.templateClass.id,\r\n        isAssociative: value.isAssociative,\r\n        treeNodeTag: `atTag${GojsNodeCategory.Class}_${value.templateClass.id}`,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles additional logic after creating a class.\r\n   * @param value - The data of the created class.\r\n   * @param classData - The data of the class.\r\n   */\r\n  private handleAdditionalClassCreationLogic(\r\n    tempCls: TemplateClass,\r\n    classData: GojsDiagramClassNode,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean,\r\n    parentNode?: TreeNode\r\n  ) {\r\n    if (!isFromLibrary) {\r\n      this.treeNodeService.addGroupNodeInTree({\r\n        name: tempCls.name,\r\n        category: tempCls.isAssociative\r\n          ? GojsNodeCategory.AssociativeClass\r\n          : GojsNodeCategory.Class,\r\n        tag: `atTag${GojsNodeCategory.Class}_${tempCls.id}`,\r\n        children: [],\r\n        icon: tempCls.isAssociative\r\n          ? GoJsNodeIcon.Associative\r\n          : GoJsNodeIcon.Class,\r\n        data: this.dataFormatService.formatDiagramClassNode(tempCls),\r\n        parentTag:\r\n          parentNode?.category !== GojsNodeCategory.Project\r\n            ? parentNode?.tag\r\n            : this.treeNodeService.getWrapperParentTag(\r\n                TreeNodeTag.ClassWrapper\r\n              ),\r\n        isDraggable: true,\r\n        supportingNodes: [\r\n          GojsNodeCategory.Attribute,\r\n          GojsNodeCategory.Operation,\r\n        ],\r\n      });\r\n    } else {\r\n      this.goJsCardinalityService.createLinksForPaletteClass(\r\n        classData.idTemplateClass,\r\n        diagram\r\n      );\r\n    }\r\n  }\r\n\r\n  addAssociativeClassAutomatically(\r\n    linkToLink: LinkToLink,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean\r\n  ) {\r\n    if (linkToLink) {\r\n      const treeNodeData = this.treeNodeService.findNodeByTag(\r\n        `atTag${GojsNodeCategory.Class}_${linkToLink.idAssociativeClass}`\r\n      );\r\n\r\n      if (treeNodeData && treeNodeData.data) {\r\n        this.createClass(\r\n          treeNodeData.data as GojsDiagramClassNode,\r\n          diagram,\r\n          isFromLibrary\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates an existing class on the diagram.\r\n   * @param classData - The data of the class to be updated.\r\n   */\r\n  private updateExistingClass(\r\n    classData: GojsDiagramClassNode,\r\n    isFromLibrary: boolean\r\n  ): void {\r\n    this.classService.updateClass({\r\n      ...this.mapClassData(classData, isFromLibrary),\r\n      id: classData.id,\r\n    });\r\n  }\r\n\r\n  updateTemplateClass(\r\n    classNodeData: GojsDiagramClassNode,\r\n    goJsDiagram: go.Diagram\r\n  ) {\r\n    const templateClass: TemplateDTO = {\r\n      id: classNodeData.idTemplateClass,\r\n      name: classNodeData.name,\r\n      key: classNodeData.key,\r\n      color: classNodeData.color,\r\n      icon: classNodeData.icon,\r\n      description: classNodeData.description,\r\n      tag: classNodeData.tag,\r\n      volumetry: classNodeData.volumetry,\r\n      isAssociative:\r\n        classNodeData.category == GojsNodeCategory.AssociativeClass\r\n          ? true\r\n          : false,\r\n    };\r\n    this.classService\r\n      .updateTemplateClass(templateClass, this.currentDiagram.id!)\r\n      .subscribe((cls) => {\r\n        this.goJsCommonService.updateNodeDataProperties(\r\n          goJsDiagram.model,\r\n          (node) => node['idTemplateClass'] === templateClass.id,\r\n          { name: cls.name }\r\n        );\r\n        const treeNode = this.treeNodeService.findNodeByTag(\r\n          classNodeData.treeNodeTag\r\n        );\r\n        if (treeNode)\r\n          this.treeNodeService.editGroupTreeNode({\r\n            ...treeNode,\r\n            name: cls.name,\r\n            data: {\r\n              ...classNodeData,\r\n              items: [],\r\n            },\r\n          });\r\n      });\r\n  }\r\n\r\n  deleteTempClass(classData: GojsDiagramClassNode[]) {\r\n    this.classService.deleteTemplateClasses(\r\n      classData.map((classObj) => classObj.idTemplateClass)\r\n    );\r\n    classData.forEach((cls) => {\r\n      this.goJsCommonService.removeGroupNodeWithItems(\r\n        cls.idTemplateClass,\r\n        cls.category,\r\n        this._gojsDiagram\r\n      );\r\n      if (cls.category == GojsNodeCategory.AssociativeClass) {\r\n        this.goJsCommonService.removeLinkToLink(\r\n          this._gojsDiagram,\r\n          (link) =>\r\n            link.idAssociativeClass === cls.idTemplateClass &&\r\n            link.category == GojsNodeCategory.LinkToLink\r\n        );\r\n      }\r\n      this.goJsCommonService.removeAssociationLink(this._gojsDiagram, cls);\r\n    });\r\n  }\r\n\r\n  /**\r\n   *  Format the Class Data to match the structure required by GoJS for displaying the elements .\r\n   * @param {IClassDTO[]} classes  are the list of Classes returned\r\n   * @return {*}\r\n   * @memberof DiagramEditorComponent\r\n   */\r\n  async formatClassData(\r\n    classes: ClassEntityDTO[],\r\n    linkHistories: DeletedLink[],\r\n    diagramId: number\r\n  ): Promise<FormattedClassData> {\r\n    const nodeDataArray: GojsDiagramClassNode[] = [];\r\n    const formattedLinkData = this.goJsCardinalityService.formatLinkData(\r\n      classes,\r\n      linkHistories,\r\n      diagramId\r\n    );\r\n    const formatLinkLabelData = formattedLinkData.map((link) => ({\r\n      key: `${link.id}_${GojsNodeCategory.LinkLabel}`,\r\n      category: GojsNodeCategory.LinkLabel,\r\n      idLink: link.id,\r\n      editable: link.editable,\r\n    }));\r\n    const linkToLinkNodes =\r\n      await this.goJsCardinalityService.generateLinkToLinkNodes(\r\n        formattedLinkData,\r\n        classes\r\n      );\r\n    classes.forEach((classObj) => {\r\n      nodeDataArray.push(this.formatDiagramClassNode(classObj));\r\n    });\r\n    return {\r\n      nodeDataArray,\r\n      linkLabelData: formatLinkLabelData,\r\n      linkDataArray: [...formattedLinkData, ...linkToLinkNodes],\r\n    };\r\n  }\r\n\r\n  /**\r\n   *\r\n   * For updating the class in library tree\r\n   * @param {GojsDiagramClassNode} response\r\n   * @param {go.Diagram} gojsDiagram\r\n   * @param {TreeNode} treeNode\r\n   * @memberof GojsClassService\r\n   */\r\n  handleClassUpdateInProperty(\r\n    response: GojsDiagramClassNode,\r\n    gojsDiagram: go.Diagram,\r\n    treeNode: TreeNode\r\n  ): void {\r\n    let diagramNode: go.ObjectData[];\r\n    diagramNode = gojsDiagram.model.nodeDataArray.filter(\r\n      (item) => item['idTemplateClass'] == response.idTemplateClass\r\n    );\r\n    this.treeNodeService.editGroupTreeNode({\r\n      ...treeNode,\r\n      name: response.name,\r\n      data: { ...response, id: response.idTemplateClass },\r\n      tag: response.treeNodeTag ?? treeNode.tag,\r\n    });\r\n    if (response && response.id && diagramNode) {\r\n      const properties: UpdateProperties = {\r\n        name: response.name,\r\n        color: response.color,\r\n        description: response.description,\r\n        tag: response.tag,\r\n        volumetry: response.volumetry,\r\n      };\r\n      this.goJsCommonService.commitGroupNodeData(\r\n        diagramNode,\r\n        properties,\r\n        gojsDiagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles the editing of a class name within the library.\r\n   * Updates the name of the class in both the main diagram and the palette.\r\n   * @param classNode - The class node being edited.\r\n   * @param TreeNode - The node for library.\r\n   */\r\n  handleEditClassNameInLibrary(\r\n    classNode: GojsDiagramClassNode,\r\n    treeNode: TreeNode\r\n  ): void {\r\n    this.classService\r\n      .updateTemplateClass(\r\n        {\r\n          id: classNode.idTemplateClass!,\r\n          key: classNode.key?.toString().split('_')[0],\r\n          icon: classNode.icon,\r\n          color: classNode.color,\r\n          name: treeNode.name,\r\n          isAssociative: classNode.isAssociative,\r\n        },\r\n        this.currentDiagram.id!\r\n      )\r\n      .subscribe(() => {\r\n        this.goJsCommonService.updateNodeDataProperties(\r\n          this._gojsDiagram.model,\r\n          (node) => node['idTemplateClass'] == classNode['idTemplateClass'],\r\n          { name: treeNode.name }\r\n        );\r\n        this.treeNodeService.editGroupTreeNode({\r\n          ...treeNode,\r\n          data: {\r\n            ...treeNode.data,\r\n            name: treeNode.name,\r\n          } as GojsDiagramClassNode,\r\n        });\r\n        this.propertyService.setPropertyData({\r\n          ...classNode,\r\n          name: treeNode.data?.name!,\r\n        });\r\n      });\r\n  }\r\n\r\n  handleClassCreationFromLibrary(\r\n    className: string,\r\n    classWrapperNode: TreeNode,\r\n    isAssociative: boolean\r\n  ) {\r\n    const parentNode = this.treeNodeService.findNodeByTag(\r\n      classWrapperNode.category === GojsNodeCategory.Folder\r\n        ? classWrapperNode.tag!\r\n        : classWrapperNode.parentTag!\r\n    );\r\n    const classObj: TemplateClassCreation = {\r\n      name: className,\r\n      key: 0,\r\n      idProject: this._currentProject.id,\r\n      icon: GoJsNodeIcon.Class,\r\n      color: 'rgba(128,128,128,0.5)',\r\n      description: '',\r\n      tag: '',\r\n      volumetry: '',\r\n      idFolder:\r\n        parentNode && parentNode.category == GojsNodeCategory.Folder\r\n          ? (parentNode.data as GojsFolderNode)?.idFolder\r\n          : 0,\r\n      isAssociative: isAssociative,\r\n    };\r\n    this.classService\r\n      .createNewTemplateClass(classObj)\r\n      .subscribe((createdClass) => {\r\n        this.treeNodeService.addGroupNodeInTree({\r\n          name: createdClass.name,\r\n          children: [],\r\n          category: createdClass.isAssociative\r\n            ? GojsNodeCategory.AssociativeClass\r\n            : GojsNodeCategory.Class,\r\n          icon: createdClass.isAssociative\r\n            ? GoJsNodeIcon.Associative\r\n            : GoJsNodeIcon.Class,\r\n          tag: `atTag${GojsNodeCategory.Class}_${createdClass.id}`,\r\n          parentTag:\r\n            parentNode && parentNode.category == GojsNodeCategory.Folder\r\n              ? parentNode.tag\r\n              : `${TreeNodeTag.ClassWrapper}_${TreeNodeTag.Project}`,\r\n          data: this.dataFormatService.formatDiagramClassNode(createdClass),\r\n          isDraggable: true,\r\n          supportingNodes: [\r\n            GojsNodeCategory.Operation,\r\n            GojsNodeCategory.Attribute,\r\n          ],\r\n        });\r\n      });\r\n  }\r\n}\r\n"], "mappings": ";AACA,OAAO,KAAKA,EAAE,MAAM,MAAM;AAU1B,SAA6BC,YAAY,QAAQ,6BAA6B;AAE9E,SAGEC,gBAAgB,QAEX,2BAA2B;AAClC,SAASC,UAAU,QAAwB,8BAA8B;AACzE,SAAmBC,WAAW,QAAQ,+BAA+B;;;;;;;;;;;;;AAerE,OAAM,MAAOC,gBAAgB;EAK3BC,YACUC,YAA0B,EAC1BC,YAA0B,EAC1BC,iBAAoC,EACpCC,eAAgC,EAChCC,aAA4B,EAC5BC,sBAA8C,EAC9CC,oBAA0C,EAC1CC,eAAgC,EAChCC,iBAAoC,EACpCC,cAA8B,EAC9BC,aAA4B;IAV5B,KAAAV,YAAY,GAAZA,YAAY;IACZ,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IAZf,KAAAC,kBAAkB,GAAY,KAAK;IAczC,IAAI,CAACV,YAAY,CAACW,oBAAoB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;IACF,IAAI,CAACZ,iBAAiB,CAACc,kBAAkB,EAAE,CAACH,SAAS,CAAEC,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACG,YAAY,GAAGH,OAAO;IAC1C,CAAC,CAAC;IACF,IAAI,CAACL,cAAc,CAACS,qBAAqB,EAAE,CAACL,SAAS,CAAEM,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACC,eAAe,GAAGD,OAAO;IAC7C,CAAC,CAAC;IACF,IAAI,CAACT,aAAa,CAACW,iBAAiB,EAAE,CAACR,SAAS,CAAES,QAAQ,IAAI;MAC5D,IAAI,CAACX,kBAAkB,GAAGW,QAAQ,IAAI1B,UAAU,CAAC2B,MAAM;IACzD,CAAC,CAAC;EACJ;EAEA;;;;;;;EAOAC,2BAA2BA,CACzBC,SAA+B,EAC/BX,OAAmB,EACnBY,aAAsB,EACtBC,KAAqB;IAErB,IAAI,CAACF,SAAS,EAAEG,EAAE,EAChB,IAAI,CAACC,WAAW,CAACJ,SAAS,EAAEX,OAAO,EAAEY,aAAa,EAAEC,KAAK,CAAC,CAAC,KACxD,IAAI,CAACG,mBAAmB,CAACL,SAAS,EAAEC,aAAa,CAAC;EACzD;EAEA;;;;;EAKQG,WAAWA,CACjBJ,SAA+B,EAC/BX,OAAmB,EACnBY,aAAsB,EACtBC,KAAqB;IAErB,MAAMI,UAAU,GAAG,IAAI,CAACxB,eAAe,CAACyB,4BAA4B,EAAE;IACtE,MAAMC,aAAa,GACjBR,SAAS,CAACS,QAAQ,IAAIvC,gBAAgB,CAACwC,gBAAgB,GAAG,IAAI,GAAG,KAAK;IACxE,IAAIC,QAAqB;IACzB,IACEL,UAAU,IACVA,UAAU,CAACG,QAAQ,IAAIvC,gBAAgB,CAAC0C,MAAM,IAC9C,CAACX,aAAa,EACd;MACAU,QAAQ,GAAG;QACT,GAAG,IAAI,CAACE,YAAY,CAACb,SAAS,EAAEC,aAAa,CAAC;QAC9Ca,QAAQ,EAAGR,UAAU,CAAC,MAAM,CAAoB,CAACQ,QAAQ;QACzDN;OACD;KACF,MAAM;MACL,MAAMO,YAAY,GAAG,IAAI,CAACjC,eAAe,CAACkC,aAAa,CACrDhB,SAAS,CAACiB,WAAW,CACtB;MACDN,QAAQ,GAAG;QACT,GAAG,IAAI,CAACE,YAAY,CAACb,SAAS,EAAEC,aAAa,CAAC;QAC9Ca,QAAQ,EAAE,IAAI,CAACtC,YAAY,CAAC0C,YAAY,CAACH,YAAY,EAAEI,SAAU,CAAC;QAClEX;OACD;;IAEH,IAAI,CAACjC,YAAY,CAAC6C,cAAc,CAACT,QAAQ,CAAC,CAACvB,SAAS,CAAC;MACnDiC,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACC,iBAAiB,CAACD,KAAK,EAAEjC,OAAO,EAAEW,SAAS,EAAEC,aAAa,CAAC;QAChE,IAAI,CAACuB,kCAAkC,CACrC;UACEC,UAAU,EAAE,EAAE;UACd,GAAGH,KAAK,CAACI,aAAa;UACtBC,KAAK,EAAE,EAAE;UACTnB;SACD,EACDR,SAAS,EACTX,OAAO,EACPY,aAAa,EACbK,UAAW,CACZ;MACH,CAAC;MACDsB,KAAK,EAAEA,CAAA,KAAK;QACV1B,KAAK,EAAEb,OAAO,CAACwC,WAAW,CAACC,QAAQ,EAAE;MACvC;KACD,CAAC;EACJ;EAEA;;;;;EAKQjB,YAAYA,CAClBb,SAA+B,EAC/BC,aAAsB;IAEtB,MAAMU,QAAQ,GAAgB;MAC5BoB,IAAI,EAAE/B,SAAS,CAAC+B,IAAI;MACpBC,GAAG,EAAEhC,SAAS,CAACgC,GAAG;MAClBC,SAAS,EAAE,IAAI,CAAC3C,cAAc,CAACa,EAAG;MAClC+B,QAAQ,EAAE;QACRC,QAAQ,EAAEnC,SAAS,CAACmC,QAAS;QAC7BC,MAAM,EAAEpC,SAAS,CAACqC,IAAI,CAACD,MAAM;QAC7BE,KAAK,EAAEtC,SAAS,CAACqC,IAAI,CAACC,KAAK;QAC3BC,IAAI,EAAEvC,SAAS,CAACuC,IAAI;QACpBC,KAAK,EAAExC,SAAS,CAACwC;OAClB;MACDC,WAAW,EAAEzC,SAAS,CAACyC,WAAW;MAClCC,GAAG,EAAE1C,SAAS,CAAC0C,GAAG;MAClBC,SAAS,EAAE3C,SAAS,CAAC2C,SAAS;MAC9BnC,aAAa,EAAER,SAAS,CAACQ;KAC1B;IACD,OAAOP,aAAa,GAChB;MACE,GAAGU,QAAQ;MACXiC,eAAe,EAAE5C,SAAS,CAAC4C,eAAe;MAC1CZ,GAAG,EAAE,CAAC,IAAI,CAACrD,aAAa,CAACkE,iBAAiB;KAC3C,GACDlC,QAAQ;EACd;EAEQmC,sBAAsBA,CAACnC,QAAwB;IACrD,OAAO;MACLiC,eAAe,EAAEjC,QAAQ,CAACiC,eAAgB;MAC1CX,SAAS,EAAEtB,QAAQ,CAACsB,SAAS;MAC7BD,GAAG,EAAE,GAAGrB,QAAQ,CAACqB,GAAG,EAAE;MACtB7B,EAAE,EAAEQ,QAAQ,CAACR,EAAE;MACfqC,KAAK,EAAE7B,QAAQ,CAACuB,QAAQ,EAAEM,KAAK;MAC/BD,IAAI,EAAE5B,QAAQ,CAACuB,QAAQ,EAAEK,IAAI;MAC7BR,IAAI,EAAEpB,QAAQ,CAACoB,IAAI;MACnBtB,QAAQ,EAAEE,QAAQ,CAACH,aAAa,GAC5BtC,gBAAgB,CAACwC,gBAAgB,GACjCxC,gBAAgB,CAAC6E,KAAK;MAC1BV,IAAI,EAAE1B,QAAQ,CAACuB,QAAQ,GACnB,IAAIlE,EAAE,CAACgF,IAAI,CAACrC,QAAQ,CAACuB,QAAQ,CAACI,KAAK,EAAE3B,QAAQ,CAACuB,QAAQ,CAACE,MAAM,CAAC,GAC9D,IAAIpE,EAAE,CAACgF,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;MACzBC,OAAO,EAAE,IAAI;MACbd,QAAQ,EAAExB,QAAQ,CAACuB,QAAQ,EAAEC,QAAQ;MACrCe,gBAAgB,EAAE,CAChBhF,gBAAgB,CAACiF,SAAS,EAC1BjF,gBAAgB,CAACkF,SAAS,CAC3B;MACDC,aAAa,EAAE,IAAI;MACnBC,kBAAkB,EAAE,IAAI;MACxBC,QAAQ,EAAE,IAAI,CAACrE,kBAAkB;MACjCuD,WAAW,EAAE9B,QAAQ,CAAC8B,WAAW;MACjCC,GAAG,EAAE/B,QAAQ,CAAC+B,GAAG;MACjBC,SAAS,EAAEhC,QAAQ,CAACgC,SAAS;MAC7Ba,KAAK,EACH,IAAI,CAAC3E,oBAAoB,CAAC4E,eAAe,CACvC9C,QAAQ,CAACc,UAAU,EACnBd,QAAQ,CAACR,EAAG,EACZ,IAAI,CAACjB,kBAAkB,CACxB,IAAI,EAAE;MACTwE,cAAc,EAAE,IAAI;MACpBzC,WAAW,EAAE,QAAQ/C,gBAAgB,CAAC6E,KAAK,IAAIpC,QAAQ,CAACiC,eAAe,EAAE;MACzEpC,aAAa,EAAEG,QAAQ,CAACH;KACzB;EACH;EAEA;;;;;EAKQe,iBAAiBA,CACvBD,KAA4B,EAC5BqC,WAAuB,EACvB3D,SAA+B,EAC/BC,aAAsB;IAEtB,IAAIA,aAAa,EAAE;MACjB0D,WAAW,CAACC,KAAK,CAACC,WAAW,CAAC7D,SAAS,CAAC;;IAE1C,MAAM8D,WAAW,GAAG,IAAI,CAACtF,YAAY,CAACuF,kBAAkB,CACtDJ,WAAW,EACX3D,SAAS,CAACgC,GAAG,CACd;IACD,IAAI8B,WAAW,EAAE;MACf,IAAI,CAACrF,iBAAiB,CAACuF,iBAAiB,CAACL,WAAW,CAACC,KAAK,EAAEE,WAAW,EAAE;QACvE3D,EAAE,EAAEmB,KAAK,CAACnB,EAAE;QACZyC,eAAe,EAAEtB,KAAK,CAACI,aAAa,CAACvB,EAAE;QACvCK,aAAa,EAAEc,KAAK,CAACd,aAAa;QAClCS,WAAW,EAAE,QAAQ/C,gBAAgB,CAAC6E,KAAK,IAAIzB,KAAK,CAACI,aAAa,CAACvB,EAAE;OACtE,CAAC;;EAEN;EAEA;;;;;EAKQqB,kCAAkCA,CACxCyC,OAAsB,EACtBjE,SAA+B,EAC/BX,OAAmB,EACnBY,aAAsB,EACtBK,UAAqB;IAErB,IAAI,CAACL,aAAa,EAAE;MAClB,IAAI,CAACnB,eAAe,CAACoF,kBAAkB,CAAC;QACtCnC,IAAI,EAAEkC,OAAO,CAAClC,IAAI;QAClBtB,QAAQ,EAAEwD,OAAO,CAACzD,aAAa,GAC3BtC,gBAAgB,CAACwC,gBAAgB,GACjCxC,gBAAgB,CAAC6E,KAAK;QAC1BL,GAAG,EAAE,QAAQxE,gBAAgB,CAAC6E,KAAK,IAAIkB,OAAO,CAAC9D,EAAE,EAAE;QACnDgE,QAAQ,EAAE,EAAE;QACZ5B,IAAI,EAAE0B,OAAO,CAACzD,aAAa,GACvBvC,YAAY,CAACmG,WAAW,GACxBnG,YAAY,CAAC8E,KAAK;QACtBsB,IAAI,EAAE,IAAI,CAACtF,iBAAiB,CAAC+D,sBAAsB,CAACmB,OAAO,CAAC;QAC5D9C,SAAS,EACPb,UAAU,EAAEG,QAAQ,KAAKvC,gBAAgB,CAACoG,OAAO,GAC7ChE,UAAU,EAAEoC,GAAG,GACf,IAAI,CAAC5D,eAAe,CAACyF,mBAAmB,CACtCnG,WAAW,CAACoG,YAAY,CACzB;QACPC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,CACfxG,gBAAgB,CAACiF,SAAS,EAC1BjF,gBAAgB,CAACkF,SAAS;OAE7B,CAAC;KACH,MAAM;MACL,IAAI,CAACxE,sBAAsB,CAAC+F,0BAA0B,CACpD3E,SAAS,CAAC4C,eAAe,EACzBvD,OAAO,CACR;;EAEL;EAEAuF,gCAAgCA,CAC9BC,UAAsB,EACtBxF,OAAmB,EACnBY,aAAsB;IAEtB,IAAI4E,UAAU,EAAE;MACd,MAAM9D,YAAY,GAAG,IAAI,CAACjC,eAAe,CAACkC,aAAa,CACrD,QAAQ9C,gBAAgB,CAAC6E,KAAK,IAAI8B,UAAU,CAACC,kBAAkB,EAAE,CAClE;MAED,IAAI/D,YAAY,IAAIA,YAAY,CAACsD,IAAI,EAAE;QACrC,IAAI,CAACjE,WAAW,CACdW,YAAY,CAACsD,IAA4B,EACzChF,OAAO,EACPY,aAAa,CACd;;;EAGP;EAEA;;;;EAIQI,mBAAmBA,CACzBL,SAA+B,EAC/BC,aAAsB;IAEtB,IAAI,CAAC1B,YAAY,CAACwG,WAAW,CAAC;MAC5B,GAAG,IAAI,CAAClE,YAAY,CAACb,SAAS,EAAEC,aAAa,CAAC;MAC9CE,EAAE,EAAEH,SAAS,CAACG;KACf,CAAC;EACJ;EAEA6E,mBAAmBA,CACjBC,aAAmC,EACnCC,WAAuB;IAEvB,MAAMxD,aAAa,GAAgB;MACjCvB,EAAE,EAAE8E,aAAa,CAACrC,eAAe;MACjCb,IAAI,EAAEkD,aAAa,CAAClD,IAAI;MACxBC,GAAG,EAAEiD,aAAa,CAACjD,GAAG;MACtBQ,KAAK,EAAEyC,aAAa,CAACzC,KAAK;MAC1BD,IAAI,EAAE0C,aAAa,CAAC1C,IAAI;MACxBE,WAAW,EAAEwC,aAAa,CAACxC,WAAW;MACtCC,GAAG,EAAEuC,aAAa,CAACvC,GAAG;MACtBC,SAAS,EAAEsC,aAAa,CAACtC,SAAS;MAClCnC,aAAa,EACXyE,aAAa,CAACxE,QAAQ,IAAIvC,gBAAgB,CAACwC,gBAAgB,GACvD,IAAI,GACJ;KACP;IACD,IAAI,CAACnC,YAAY,CACdyG,mBAAmB,CAACtD,aAAa,EAAE,IAAI,CAACpC,cAAc,CAACa,EAAG,CAAC,CAC3Df,SAAS,CAAE+F,GAAG,IAAI;MACjB,IAAI,CAAC1G,iBAAiB,CAAC2G,wBAAwB,CAC7CF,WAAW,CAACtB,KAAK,EAChByB,IAAI,IAAKA,IAAI,CAAC,iBAAiB,CAAC,KAAK3D,aAAa,CAACvB,EAAE,EACtD;QAAE4B,IAAI,EAAEoD,GAAG,CAACpD;MAAI,CAAE,CACnB;MACD,MAAMuD,QAAQ,GAAG,IAAI,CAACxG,eAAe,CAACkC,aAAa,CACjDiE,aAAa,CAAChE,WAAW,CAC1B;MACD,IAAIqE,QAAQ,EACV,IAAI,CAACxG,eAAe,CAACyG,iBAAiB,CAAC;QACrC,GAAGD,QAAQ;QACXvD,IAAI,EAAEoD,GAAG,CAACpD,IAAI;QACdsC,IAAI,EAAE;UACJ,GAAGY,aAAa;UAChBzB,KAAK,EAAE;;OAEV,CAAC;IACN,CAAC,CAAC;EACN;EAEAgC,eAAeA,CAACxF,SAAiC;IAC/C,IAAI,CAACzB,YAAY,CAACkH,qBAAqB,CACrCzF,SAAS,CAAC0F,GAAG,CAAE/E,QAAQ,IAAKA,QAAQ,CAACiC,eAAe,CAAC,CACtD;IACD5C,SAAS,CAAC2F,OAAO,CAAER,GAAG,IAAI;MACxB,IAAI,CAAC1G,iBAAiB,CAACmH,wBAAwB,CAC7CT,GAAG,CAACvC,eAAe,EACnBuC,GAAG,CAAC1E,QAAQ,EACZ,IAAI,CAACjB,YAAY,CAClB;MACD,IAAI2F,GAAG,CAAC1E,QAAQ,IAAIvC,gBAAgB,CAACwC,gBAAgB,EAAE;QACrD,IAAI,CAACjC,iBAAiB,CAACoH,gBAAgB,CACrC,IAAI,CAACrG,YAAY,EAChBsG,IAAI,IACHA,IAAI,CAAChB,kBAAkB,KAAKK,GAAG,CAACvC,eAAe,IAC/CkD,IAAI,CAACrF,QAAQ,IAAIvC,gBAAgB,CAAC6H,UAAU,CAC/C;;MAEH,IAAI,CAACtH,iBAAiB,CAACuH,qBAAqB,CAAC,IAAI,CAACxG,YAAY,EAAE2F,GAAG,CAAC;IACtE,CAAC,CAAC;EACJ;EAEA;;;;;;EAMMc,eAAeA,CACnBC,OAAyB,EACzBC,aAA4B,EAC5BC,SAAiB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAEjB,MAAMC,aAAa,GAA2B,EAAE;MAChD,MAAMC,iBAAiB,GAAGH,KAAI,CAACzH,sBAAsB,CAAC6H,cAAc,CAClEP,OAAO,EACPC,aAAa,EACbC,SAAS,CACV;MACD,MAAMM,mBAAmB,GAAGF,iBAAiB,CAACd,GAAG,CAAEI,IAAI,KAAM;QAC3D9D,GAAG,EAAE,GAAG8D,IAAI,CAAC3F,EAAE,IAAIjC,gBAAgB,CAACyI,SAAS,EAAE;QAC/ClG,QAAQ,EAAEvC,gBAAgB,CAACyI,SAAS;QACpCC,MAAM,EAAEd,IAAI,CAAC3F,EAAE;QACfoD,QAAQ,EAAEuC,IAAI,CAACvC;OAChB,CAAC,CAAC;MACH,MAAMsD,eAAe,SACbR,KAAI,CAACzH,sBAAsB,CAACkI,uBAAuB,CACvDN,iBAAiB,EACjBN,OAAO,CACR;MACHA,OAAO,CAACP,OAAO,CAAEhF,QAAQ,IAAI;QAC3B4F,aAAa,CAACQ,IAAI,CAACV,KAAI,CAACvD,sBAAsB,CAACnC,QAAQ,CAAC,CAAC;MAC3D,CAAC,CAAC;MACF,OAAO;QACL4F,aAAa;QACbS,aAAa,EAAEN,mBAAmB;QAClCO,aAAa,EAAE,CAAC,GAAGT,iBAAiB,EAAE,GAAGK,eAAe;OACzD;IAAC;EACJ;EAEA;;;;;;;;EAQAK,2BAA2BA,CACzBrH,QAA8B,EAC9B8D,WAAuB,EACvB2B,QAAkB;IAElB,IAAI6B,WAA4B;IAChCA,WAAW,GAAGxD,WAAW,CAACC,KAAK,CAAC2C,aAAa,CAACa,MAAM,CACjDC,IAAI,IAAKA,IAAI,CAAC,iBAAiB,CAAC,IAAIxH,QAAQ,CAAC+C,eAAe,CAC9D;IACD,IAAI,CAAC9D,eAAe,CAACyG,iBAAiB,CAAC;MACrC,GAAGD,QAAQ;MACXvD,IAAI,EAAElC,QAAQ,CAACkC,IAAI;MACnBsC,IAAI,EAAE;QAAE,GAAGxE,QAAQ;QAAEM,EAAE,EAAEN,QAAQ,CAAC+C;MAAe,CAAE;MACnDF,GAAG,EAAE7C,QAAQ,CAACoB,WAAW,IAAIqE,QAAQ,CAAC5C;KACvC,CAAC;IACF,IAAI7C,QAAQ,IAAIA,QAAQ,CAACM,EAAE,IAAIgH,WAAW,EAAE;MAC1C,MAAMG,UAAU,GAAqB;QACnCvF,IAAI,EAAElC,QAAQ,CAACkC,IAAI;QACnBS,KAAK,EAAE3C,QAAQ,CAAC2C,KAAK;QACrBC,WAAW,EAAE5C,QAAQ,CAAC4C,WAAW;QACjCC,GAAG,EAAE7C,QAAQ,CAAC6C,GAAG;QACjBC,SAAS,EAAE9C,QAAQ,CAAC8C;OACrB;MACD,IAAI,CAAClE,iBAAiB,CAAC8I,mBAAmB,CACxCJ,WAAW,EACXG,UAAU,EACV3D,WAAW,CACZ;;EAEL;EAEA;;;;;;EAMA6D,4BAA4BA,CAC1BC,SAA+B,EAC/BnC,QAAkB;IAElB,IAAI,CAAC/G,YAAY,CACdyG,mBAAmB,CAClB;MACE7E,EAAE,EAAEsH,SAAS,CAAC7E,eAAgB;MAC9BZ,GAAG,EAAEyF,SAAS,CAACzF,GAAG,EAAE0F,QAAQ,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5CpF,IAAI,EAAEkF,SAAS,CAAClF,IAAI;MACpBC,KAAK,EAAEiF,SAAS,CAACjF,KAAK;MACtBT,IAAI,EAAEuD,QAAQ,CAACvD,IAAI;MACnBvB,aAAa,EAAEiH,SAAS,CAACjH;KAC1B,EACD,IAAI,CAAClB,cAAc,CAACa,EAAG,CACxB,CACAf,SAAS,CAAC,MAAK;MACd,IAAI,CAACX,iBAAiB,CAAC2G,wBAAwB,CAC7C,IAAI,CAAC5F,YAAY,CAACoE,KAAK,EACtByB,IAAI,IAAKA,IAAI,CAAC,iBAAiB,CAAC,IAAIoC,SAAS,CAAC,iBAAiB,CAAC,EACjE;QAAE1F,IAAI,EAAEuD,QAAQ,CAACvD;MAAI,CAAE,CACxB;MACD,IAAI,CAACjD,eAAe,CAACyG,iBAAiB,CAAC;QACrC,GAAGD,QAAQ;QACXjB,IAAI,EAAE;UACJ,GAAGiB,QAAQ,CAACjB,IAAI;UAChBtC,IAAI,EAAEuD,QAAQ,CAACvD;;OAElB,CAAC;MACF,IAAI,CAACrD,eAAe,CAACkJ,eAAe,CAAC;QACnC,GAAGH,SAAS;QACZ1F,IAAI,EAAEuD,QAAQ,CAACjB,IAAI,EAAEtC;OACtB,CAAC;IACJ,CAAC,CAAC;EACN;EAEA8F,8BAA8BA,CAC5BC,SAAiB,EACjBC,gBAA0B,EAC1BvH,aAAsB;IAEtB,MAAMF,UAAU,GAAG,IAAI,CAACxB,eAAe,CAACkC,aAAa,CACnD+G,gBAAgB,CAACtH,QAAQ,KAAKvC,gBAAgB,CAAC0C,MAAM,GACjDmH,gBAAgB,CAACrF,GAAI,GACrBqF,gBAAgB,CAAC5G,SAAU,CAChC;IACD,MAAMR,QAAQ,GAA0B;MACtCoB,IAAI,EAAE+F,SAAS;MACf9F,GAAG,EAAE,CAAC;MACNgG,SAAS,EAAE,IAAI,CAACrI,eAAe,CAACQ,EAAE;MAClCoC,IAAI,EAAEtE,YAAY,CAAC8E,KAAK;MACxBP,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE,EAAE;MACPC,SAAS,EAAE,EAAE;MACb7B,QAAQ,EACNR,UAAU,IAAIA,UAAU,CAACG,QAAQ,IAAIvC,gBAAgB,CAAC0C,MAAM,GACvDN,UAAU,CAAC+D,IAAuB,EAAEvD,QAAQ,GAC7C,CAAC;MACPN,aAAa,EAAEA;KAChB;IACD,IAAI,CAACjC,YAAY,CACd0J,sBAAsB,CAACtH,QAAQ,CAAC,CAChCvB,SAAS,CAAE8I,YAAY,IAAI;MAC1B,IAAI,CAACpJ,eAAe,CAACoF,kBAAkB,CAAC;QACtCnC,IAAI,EAAEmG,YAAY,CAACnG,IAAI;QACvBoC,QAAQ,EAAE,EAAE;QACZ1D,QAAQ,EAAEyH,YAAY,CAAC1H,aAAa,GAChCtC,gBAAgB,CAACwC,gBAAgB,GACjCxC,gBAAgB,CAAC6E,KAAK;QAC1BR,IAAI,EAAE2F,YAAY,CAAC1H,aAAa,GAC5BvC,YAAY,CAACmG,WAAW,GACxBnG,YAAY,CAAC8E,KAAK;QACtBL,GAAG,EAAE,QAAQxE,gBAAgB,CAAC6E,KAAK,IAAImF,YAAY,CAAC/H,EAAE,EAAE;QACxDgB,SAAS,EACPb,UAAU,IAAIA,UAAU,CAACG,QAAQ,IAAIvC,gBAAgB,CAAC0C,MAAM,GACxDN,UAAU,CAACoC,GAAG,GACd,GAAGtE,WAAW,CAACoG,YAAY,IAAIpG,WAAW,CAACkG,OAAO,EAAE;QAC1DD,IAAI,EAAE,IAAI,CAACtF,iBAAiB,CAAC+D,sBAAsB,CAACoF,YAAY,CAAC;QACjEzD,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,CACfxG,gBAAgB,CAACkF,SAAS,EAC1BlF,gBAAgB,CAACiF,SAAS;OAE7B,CAAC;IACJ,CAAC,CAAC;EACN;EAAC,QAAAgF,CAAA,G;qBAtgBU9J,gBAAgB,EAAA+J,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,sBAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,oBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,eAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,iBAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,cAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBvL,gBAAgB;IAAAwL,OAAA,EAAhBxL,gBAAgB,CAAAyL,IAAA;IAAAC,UAAA,EAFf;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}