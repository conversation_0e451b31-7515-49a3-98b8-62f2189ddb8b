{"ast": null, "code": "import go from 'gojs';\nimport { AttributeType } from 'src/app/shared/model/attribute';\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../enumeration/enumeration.service\";\nimport * as i2 from \"src/app/shared/utils/diagram-utils\";\nimport * as i3 from \"../gojsCommon/gojs-common.service\";\nimport * as i4 from \"../gojsLiteral/gojs-literal.service\";\nimport * as i5 from \"src/app/shared/services/shared.service\";\nimport * as i6 from \"../../treeNode/tree-node.service\";\nimport * as i7 from \"../../data-format/data-format.service\";\nimport * as i8 from \"../../property/property.service\";\nimport * as i9 from \"../../project/project.service\";\nexport class GojsEnumerationService {\n  constructor(enumerationService, diagramUtils, goJsCommonService, goJsLiteralService, sharedService, treeNodeService, dataFormatService, propertyService, projectService) {\n    this.enumerationService = enumerationService;\n    this.diagramUtils = diagramUtils;\n    this.goJsCommonService = goJsCommonService;\n    this.goJsLiteralService = goJsLiteralService;\n    this.sharedService = sharedService;\n    this.treeNodeService = treeNodeService;\n    this.dataFormatService = dataFormatService;\n    this.propertyService = propertyService;\n    this.projectService = projectService;\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n    this.goJsCommonService.gojsDiagramChanges().subscribe(diagram => {\n      if (diagram) this._gojsDiagram = diagram;\n    });\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project) this._currentProject = project;\n    });\n  }\n  handleEnumCreationOrUpdate(enumData, diagram, isFromLibrary, event) {\n    if (!enumData?.['id']) this.createEnum(enumData, diagram, isFromLibrary, event);else this.updateExistingEnum(enumData, isFromLibrary);\n  }\n  createEnum(enumData, diagram, isFromLibrary, event) {\n    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();\n    let enumObj;\n    if (parentNode && parentNode.category == GojsNodeCategory.Folder && !isFromLibrary) {\n      enumObj = {\n        ...this.mapEnumDataToDTO(enumData, isFromLibrary),\n        idFolder: parentNode['data'].idFolder\n      };\n    } else {\n      const treeNodeData = this.treeNodeService.findNodeByTag(enumData.treeNodeTag);\n      enumObj = {\n        ...this.mapEnumDataToDTO(enumData, isFromLibrary),\n        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag)\n      };\n    }\n    this.enumerationService.createEnumeration(enumObj).subscribe({\n      next: value => {\n        this.addEnumToDiagram(value, diagram, enumData, isFromLibrary);\n        if (value.templateEnumeration) this.handleAdditionalEnumCreationLogic(value.templateEnumeration, isFromLibrary, parentNode);\n      },\n      error: () => {\n        event?.diagram.currentTool.doCancel();\n      }\n    });\n  }\n  mapEnumDataToDTO(enumData, isFromLibrary) {\n    const enumObj = {\n      name: enumData['name'],\n      key: enumData['key'],\n      idDiagram: this.currentDiagram.id,\n      property: {\n        position: enumData['position'],\n        height: enumData['size'].height,\n        width: enumData['size'].width,\n        icon: enumData['icon'],\n        color: enumData['color']\n      },\n      description: enumData['description'],\n      tag: enumData['tag'],\n      volumetry: enumData['volumetry']\n    };\n    return isFromLibrary ? {\n      ...enumObj,\n      idTemplateEnumeration: enumData.idTemplateEnumeration,\n      key: +this.sharedService.generateUniqueKey()\n    } : enumObj;\n  }\n  addEnumToDiagram(value, gojsDiagram, enumData, isFromLibrary) {\n    if (isFromLibrary) {\n      gojsDiagram.model.addNodeData(enumData);\n    }\n    const diagramData = this.diagramUtils.getObjectDataByKey(gojsDiagram, enumData['key']);\n    if (diagramData) {\n      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {\n        id: value.id,\n        idTemplateEnumeration: value.templateEnumeration?.id,\n        treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${value.templateEnumeration?.id}`\n      });\n    }\n  }\n  handleAdditionalEnumCreationLogic(tempEnum, isFromLibrary, parentNode) {\n    if (!isFromLibrary) {\n      this.treeNodeService.addGroupNodeInTree({\n        name: tempEnum.name,\n        category: GojsNodeCategory.Enumeration,\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\n        children: [],\n        icon: GoJsNodeIcon.Enumeration,\n        data: this.dataFormatService.formatDiagramEnumData(tempEnum),\n        parentTag: parentNode?.category !== GojsNodeCategory.Project ? parentNode?.tag : this.treeNodeService.getWrapperParentTag(TreeNodeTag.EnumerationWrapper),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral]\n      });\n      this.diagramUtils.addAttributeTypes({\n        id: tempEnum.id?.toString(),\n        name: tempEnum?.name,\n        isEnumeration: true\n      });\n    }\n  }\n  updateExistingEnum(enumData, isFromLibrary) {\n    this.enumerationService.updateEnumeration({\n      ...this.mapEnumDataToDTO(enumData, isFromLibrary),\n      id: enumData['id']\n    });\n  }\n  updateEnumerationFromDiagram(enumNodeData, goJsDiagram) {\n    const templateEnumObj = {\n      id: enumNodeData.idTemplateEnumeration,\n      name: enumNodeData.name,\n      key: enumNodeData.key,\n      color: enumNodeData.color,\n      icon: enumNodeData.icon,\n      description: enumNodeData.description,\n      tag: enumNodeData.tag,\n      volumetry: enumNodeData.volumetry\n    };\n    this.enumerationService.updateTempEnumeration(templateEnumObj, this.currentDiagram.id).subscribe(updatedTempEnum => {\n      this.diagramUtils.updateEnumerationType(updatedTempEnum.id?.toString(), updatedTempEnum.name, goJsDiagram);\n      this.goJsCommonService.updateNodeDataProperties(goJsDiagram.model, node => node['idTemplateEnumeration'] == updatedTempEnum.id, {\n        name: updatedTempEnum.name\n      });\n      const treeNode = this.treeNodeService.findNodeByTag(enumNodeData.treeNodeTag);\n      if (treeNode) this.treeNodeService.editGroupTreeNode({\n        ...treeNode,\n        name: updatedTempEnum.name,\n        data: enumNodeData\n      });\n    });\n  }\n  deleteTempEnumeration(enumData) {\n    this.enumerationService.deleteTemplateEnums(enumData.map(enumObj => enumObj.idTemplateEnumeration));\n    enumData.forEach(enumObj => {\n      this.diagramUtils.removeAttributeType(enumObj.idTemplateEnumeration.toString());\n      this.goJsCommonService.removeGroupNodeWithItems(enumObj?.idTemplateEnumeration, enumObj.category, this._gojsDiagram);\n      this.goJsCommonService.updateNodeDataItemsProperties(this._gojsDiagram.model, item => item.dataType == enumObj.idTemplateEnumeration.toString(), {\n        dataType: `0_${AttributeType[AttributeType.Undefined]}`\n      });\n    });\n  }\n  formatDiagramEnumerationData(enumerations, hasEditAccess) {\n    const nodeDataArray = enumerations.map(enumObj => ({\n      idTemplateEnumeration: enumObj.idTemplateEnumeration,\n      key: enumObj.key,\n      id: enumObj.id,\n      color: enumObj.property?.color,\n      icon: enumObj.property?.icon,\n      name: enumObj.name,\n      category: GojsNodeCategory.Enumeration,\n      size: enumObj.property ? new go.Size(enumObj.property.width, enumObj.property.height) : new go.Size(150, 100),\n      isGroup: true,\n      position: enumObj.property?.position,\n      supportingLevels: [GojsNodeCategory.EnumerationLiteral],\n      isHighlighted: true,\n      allowTopLevelDrops: true,\n      editable: hasEditAccess,\n      showTablePanel: true,\n      description: enumObj.description,\n      tag: enumObj.tag,\n      volumetry: enumObj.volumetry,\n      items: this.goJsLiteralService.formatLiteralData(enumObj.enumerationLiterals, enumObj.id),\n      treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${enumObj.idTemplateEnumeration}`\n    }));\n    return nodeDataArray;\n  }\n  updateEnumNode(updatedNode, gojsDiagram, treeNode) {\n    let diagramNode;\n    diagramNode = this._gojsDiagram.model.nodeDataArray.filter(item => item['idTemplateEnumeration'] == updatedNode.idTemplateEnumeration);\n    this.treeNodeService.editGroupTreeNode({\n      ...treeNode,\n      name: updatedNode.name,\n      data: {\n        ...updatedNode\n      },\n      tag: updatedNode.treeNodeTag ?? treeNode.tag\n    });\n    if (updatedNode && updatedNode.id && diagramNode) {\n      const properties = {\n        name: updatedNode.name,\n        color: updatedNode.color,\n        description: updatedNode.description,\n        tag: updatedNode.tag,\n        volumetry: updatedNode.volumetry\n      };\n      this.goJsCommonService.commitGroupNodeData(diagramNode, properties, gojsDiagram);\n    }\n  }\n  /**\n   * Handles the renaming of an enumeration in the library.\n   *\n   * This method updates the name of an enumeration node in both the main diagram and the palette.\n   * It first sends an update request to the backend to modify the enumeration's name and other properties.\n   * Once the update is successful, it propagates the changes to the relevant nodes in the diagram and palette.\n   *\n   * @param enumNode - The GoJS node representing the enumeration to be renamed.\n   * @param enumName - The new name for the enumeration.\n   * @param diagram - The main GoJS diagram where the enumeration is used.\n   * @param palette - The GoJS palette diagram where the enumeration node resides.\n   */\n  handleEditEnumNameInLibrary(enumNode, treeNode) {\n    this.enumerationService.updateTempEnumeration({\n      id: enumNode.idTemplateEnumeration,\n      key: enumNode.key?.toString().split('_')[0],\n      icon: enumNode.icon,\n      color: enumNode.color,\n      name: treeNode.name\n    }, this.currentDiagram.id).subscribe(updatedTempEnum => {\n      this.diagramUtils.updateEnumerationType(updatedTempEnum.id?.toString(), updatedTempEnum.name, this._gojsDiagram);\n      // Update the name in the main diagram if the enumeration is used.\n      this.goJsCommonService.updateNodeDataProperties(this._gojsDiagram.model, node => node['idTemplateEnumeration'] == enumNode['idTemplateEnumeration'], {\n        name: treeNode.name\n      });\n      // Update the name in the palette.\n      this.treeNodeService.editGroupTreeNode({\n        ...treeNode,\n        data: {\n          ...treeNode.data,\n          name: treeNode.name\n        }\n      });\n      this.propertyService.setPropertyData({\n        ...enumNode,\n        name: treeNode.name\n      });\n    });\n  }\n  handleEnumerationCreationFromLibrary(enumName, wrapperNode) {\n    const parentNode = this.treeNodeService.findNodeByTag(wrapperNode.category === GojsNodeCategory.Folder ? wrapperNode.tag : wrapperNode.parentTag);\n    const enumObj = {\n      name: enumName,\n      key: 0,\n      icon: GoJsNodeIcon.Enumeration,\n      color: 'rgba(128,128,128,0.5)',\n      idProject: this._currentProject.id,\n      description: '',\n      tag: '',\n      volumetry: '',\n      idFolder: parentNode && parentNode.category == GojsNodeCategory.Folder ? parentNode.data?.idFolder : 0\n    };\n    this.enumerationService.createNewTemplateEnumeration(enumObj).subscribe(createdEnum => {\n      this.treeNodeService.addGroupNodeInTree({\n        name: createdEnum.name,\n        children: [],\n        category: GojsNodeCategory.Enumeration,\n        icon: createdEnum.icon,\n        tag: `atTag${GojsNodeCategory.Enumeration}_${createdEnum.id}`,\n        parentTag: parentNode && parentNode.category == GojsNodeCategory.Folder ? parentNode.tag : `${TreeNodeTag.EnumerationWrapper}_${TreeNodeTag.Project}`,\n        data: this.dataFormatService.formatTempEnumData(createdEnum),\n        isDraggable: true,\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral]\n      });\n      this.diagramUtils.addAttributeTypes({\n        id: createdEnum.id?.toString(),\n        name: createdEnum.name,\n        isEnumeration: true\n      });\n    });\n  }\n  static #_ = this.ɵfac = function GojsEnumerationService_Factory(t) {\n    return new (t || GojsEnumerationService)(i0.ɵɵinject(i1.EnumerationService), i0.ɵɵinject(i2.DiagramUtils), i0.ɵɵinject(i3.GojsCommonService), i0.ɵɵinject(i4.GojsLiteralService), i0.ɵɵinject(i5.SharedService), i0.ɵɵinject(i6.TreeNodeService), i0.ɵɵinject(i7.DataFormatService), i0.ɵɵinject(i8.PropertyService), i0.ɵɵinject(i9.ProjectService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GojsEnumerationService,\n    factory: GojsEnumerationService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["go", "AttributeType", "GoJsNodeIcon", "GojsNodeCategory", "TreeNodeTag", "GojsEnumerationService", "constructor", "enumerationService", "diagramUtils", "goJsCommonService", "goJsLiteralService", "sharedService", "treeNodeService", "dataFormatService", "propertyService", "projectService", "activeDiagramChanges", "subscribe", "diagram", "currentDiagram", "gojsDiagramChanges", "_gojsDiagram", "currentProjectChanges", "project", "_currentProject", "handleEnumCreationOrUpdate", "enumData", "isFromLibrary", "event", "createEnum", "updateExistingEnum", "parentNode", "findCurrentDiagramParentNode", "enumObj", "category", "Folder", "mapEnumDataToDTO", "idFolder", "treeNodeData", "findNodeByTag", "treeNodeTag", "getIdFromTag", "parentTag", "createEnumeration", "next", "value", "addEnumToDiagram", "templateEnumeration", "handleAdditionalEnumCreationLogic", "error", "currentTool", "doCancel", "name", "key", "idDiagram", "id", "property", "position", "height", "width", "icon", "color", "description", "tag", "volumetry", "idTemplateEnumeration", "generateUniqueKey", "gojsDiagram", "model", "addNodeData", "diagramData", "getObjectDataByKey", "setDataProperties", "Enumeration", "tempEnum", "addGroupNodeInTree", "children", "data", "formatDiagramEnumData", "Project", "getWrapperParentTag", "EnumerationWrapper", "isDraggable", "supportingNodes", "EnumerationLiteral", "addAttributeTypes", "toString", "isEnumeration", "updateEnumeration", "updateEnumerationFromDiagram", "enumNodeData", "goJsDiagram", "templateEnumObj", "updateTempEnumeration", "updatedTempEnum", "updateEnumerationType", "updateNodeDataProperties", "node", "treeNode", "editGroupTreeNode", "deleteTempEnumeration", "deleteTemplateEnums", "map", "for<PERSON>ach", "removeAttributeType", "removeGroupNodeWithItems", "updateNodeDataItemsProperties", "item", "dataType", "Undefined", "formatDiagramEnumerationData", "enumerations", "hasEditAccess", "nodeDataArray", "size", "Size", "isGroup", "supportingLevels", "isHighlighted", "allowTopLevelDrops", "editable", "showTablePanel", "items", "formatLiteralData", "enumerationLiterals", "updateEnumNode", "updatedNode", "diagramNode", "filter", "properties", "commitGroupNodeData", "handleEditEnumNameInLibrary", "enumNode", "split", "setPropertyData", "handleEnumerationCreationFromLibrary", "enumName", "wrapperNode", "idProject", "createNewTemplateEnumeration", "createdEnum", "formatTempEnumData", "_", "i0", "ɵɵinject", "i1", "EnumerationService", "i2", "DiagramUtils", "i3", "GojsCommonService", "i4", "GojsLiteralService", "i5", "SharedService", "i6", "TreeNodeService", "i7", "DataFormatService", "i8", "PropertyService", "i9", "ProjectService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\gojs\\gojsEnumeration\\gojs-enumeration.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport go from 'gojs';\r\nimport { AttributeType } from 'src/app/shared/model/attribute';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport {\r\n  CreatedEnumeration,\r\n  Enumeration,\r\n  EnumerationDTO,\r\n  TemplateEnumeration,\r\n  TemplateEnumerationCreation,\r\n} from 'src/app/shared/model/enumeration';\r\nimport {\r\n  GojsDiagramEnumerationNode,\r\n  GojsFolderNode,\r\n  GojsNodeCategory,\r\n  UpdateProperties,\r\n} from 'src/app/shared/model/gojs';\r\nimport { ProjectDetails } from 'src/app/shared/model/project';\r\nimport { TreeNode, TreeNodeTag } from 'src/app/shared/model/treeNode';\r\nimport { SharedService } from 'src/app/shared/services/shared.service';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { DataFormatService } from '../../data-format/data-format.service';\r\nimport { EnumerationService } from '../../enumeration/enumeration.service';\r\nimport { ProjectService } from '../../project/project.service';\r\nimport { PropertyService } from '../../property/property.service';\r\nimport { TreeNodeService } from '../../treeNode/tree-node.service';\r\nimport { GojsCommonService } from '../gojsCommon/gojs-common.service';\r\nimport { GojsLiteralService } from '../gojsLiteral/gojs-literal.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class GojsEnumerationService {\r\n  private currentDiagram!: Diagram;\r\n  private _gojsDiagram!: go.Diagram;\r\n  private _currentProject!: ProjectDetails;\r\n  constructor(\r\n    private enumerationService: EnumerationService,\r\n    private diagramUtils: DiagramUtils,\r\n    private goJsCommonService: GojsCommonService,\r\n    private goJsLiteralService: GojsLiteralService,\r\n    private sharedService: SharedService,\r\n    private treeNodeService: TreeNodeService,\r\n    private dataFormatService: DataFormatService,\r\n    private propertyService: PropertyService,\r\n    private projectService: ProjectService\r\n  ) {\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n    this.goJsCommonService.gojsDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this._gojsDiagram = diagram;\r\n    });\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project) this._currentProject = project;\r\n    });\r\n  }\r\n\r\n  handleEnumCreationOrUpdate(\r\n    enumData: GojsDiagramEnumerationNode,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean,\r\n    event?: go.InputEvent\r\n  ) {\r\n    if (!enumData?.['id'])\r\n      this.createEnum(enumData, diagram, isFromLibrary, event);\r\n    else this.updateExistingEnum(enumData, isFromLibrary);\r\n  }\r\n\r\n  private createEnum(\r\n    enumData: GojsDiagramEnumerationNode,\r\n    diagram: go.Diagram,\r\n    isFromLibrary: boolean,\r\n    event?: go.InputEvent\r\n  ) {\r\n    const parentNode = this.treeNodeService.findCurrentDiagramParentNode();\r\n    let enumObj: Enumeration;\r\n    if (\r\n      parentNode &&\r\n      parentNode.category == GojsNodeCategory.Folder &&\r\n      !isFromLibrary\r\n    ) {\r\n      enumObj = {\r\n        ...this.mapEnumDataToDTO(enumData, isFromLibrary),\r\n        idFolder: (parentNode['data'] as GojsFolderNode).idFolder,\r\n      };\r\n    } else {\r\n      const treeNodeData = this.treeNodeService.findNodeByTag(\r\n        enumData.treeNodeTag\r\n      );\r\n      enumObj = {\r\n        ...this.mapEnumDataToDTO(enumData, isFromLibrary),\r\n        idFolder: this.diagramUtils.getIdFromTag(treeNodeData?.parentTag!),\r\n      };\r\n    }\r\n    this.enumerationService.createEnumeration(enumObj).subscribe({\r\n      next: (value) => {\r\n        this.addEnumToDiagram(value, diagram, enumData, isFromLibrary);\r\n        if (value.templateEnumeration)\r\n          this.handleAdditionalEnumCreationLogic(\r\n            value.templateEnumeration,\r\n            isFromLibrary,\r\n            parentNode!\r\n          );\r\n      },\r\n      error: () => {\r\n        event?.diagram.currentTool.doCancel();\r\n      },\r\n    });\r\n  }\r\n\r\n  private mapEnumDataToDTO(\r\n    enumData: GojsDiagramEnumerationNode,\r\n    isFromLibrary: boolean\r\n  ): CreatedEnumeration {\r\n    const enumObj: CreatedEnumeration = {\r\n      name: enumData['name'],\r\n      key: enumData['key'],\r\n      idDiagram: this.currentDiagram.id!,\r\n      property: {\r\n        position: enumData['position']!,\r\n        height: enumData['size'].height,\r\n        width: enumData['size'].width,\r\n        icon: enumData['icon'],\r\n        color: enumData['color'],\r\n      },\r\n      description: enumData['description'],\r\n      tag: enumData['tag'],\r\n      volumetry: enumData['volumetry'],\r\n    };\r\n    return isFromLibrary\r\n      ? {\r\n          ...enumObj,\r\n          idTemplateEnumeration: enumData.idTemplateEnumeration,\r\n          key: +this.sharedService.generateUniqueKey(),\r\n        }\r\n      : enumObj;\r\n  }\r\n\r\n  private addEnumToDiagram(\r\n    value: CreatedEnumeration,\r\n    gojsDiagram: go.Diagram,\r\n    enumData: go.ObjectData,\r\n    isFromLibrary: boolean\r\n  ) {\r\n    if (isFromLibrary) {\r\n      gojsDiagram.model.addNodeData(enumData);\r\n    }\r\n    const diagramData = this.diagramUtils.getObjectDataByKey(\r\n      gojsDiagram,\r\n      enumData['key']\r\n    );\r\n    if (diagramData) {\r\n      this.goJsCommonService.setDataProperties(gojsDiagram.model, diagramData, {\r\n        id: value.id,\r\n        idTemplateEnumeration: value.templateEnumeration?.id,\r\n        treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${value.templateEnumeration?.id}`,\r\n      });\r\n    }\r\n  }\r\n\r\n  private handleAdditionalEnumCreationLogic(\r\n    tempEnum: TemplateEnumeration,\r\n    isFromLibrary: boolean,\r\n    parentNode?: TreeNode\r\n  ) {\r\n    if (!isFromLibrary) {\r\n      this.treeNodeService.addGroupNodeInTree({\r\n        name: tempEnum.name,\r\n        category: GojsNodeCategory.Enumeration,\r\n        tag: `atTag${GojsNodeCategory.Enumeration}_${tempEnum.id}`,\r\n        children: [],\r\n        icon: GoJsNodeIcon.Enumeration,\r\n        data: this.dataFormatService.formatDiagramEnumData(tempEnum),\r\n        parentTag:\r\n          parentNode?.category !== GojsNodeCategory.Project\r\n            ? parentNode?.tag\r\n            : this.treeNodeService.getWrapperParentTag(\r\n                TreeNodeTag.EnumerationWrapper\r\n              ),\r\n        isDraggable: true,\r\n        supportingNodes: [GojsNodeCategory.EnumerationLiteral],\r\n      });\r\n      this.diagramUtils.addAttributeTypes({\r\n        id: tempEnum.id?.toString()!,\r\n        name: tempEnum?.name,\r\n        isEnumeration: true,\r\n      });\r\n    }\r\n  }\r\n\r\n  updateExistingEnum(\r\n    enumData: GojsDiagramEnumerationNode,\r\n    isFromLibrary: boolean\r\n  ): void {\r\n    this.enumerationService.updateEnumeration({\r\n      ...this.mapEnumDataToDTO(enumData, isFromLibrary),\r\n      id: enumData['id'],\r\n    });\r\n  }\r\n\r\n  updateEnumerationFromDiagram(\r\n    enumNodeData: GojsDiagramEnumerationNode,\r\n    goJsDiagram: go.Diagram\r\n  ) {\r\n    const templateEnumObj = {\r\n      id: enumNodeData.idTemplateEnumeration,\r\n      name: enumNodeData.name,\r\n      key: enumNodeData.key,\r\n      color: enumNodeData.color,\r\n      icon: enumNodeData.icon,\r\n      description: enumNodeData.description,\r\n      tag: enumNodeData.tag,\r\n      volumetry: enumNodeData.volumetry,\r\n    };\r\n    this.enumerationService\r\n      .updateTempEnumeration(\r\n        templateEnumObj as TemplateEnumeration,\r\n        this.currentDiagram.id!\r\n      )\r\n      .subscribe((updatedTempEnum) => {\r\n        this.diagramUtils.updateEnumerationType(\r\n          updatedTempEnum.id?.toString()!,\r\n          updatedTempEnum.name,\r\n          goJsDiagram\r\n        );\r\n        this.goJsCommonService.updateNodeDataProperties(\r\n          goJsDiagram.model,\r\n          (node) => node['idTemplateEnumeration'] == updatedTempEnum.id,\r\n          {\r\n            name: updatedTempEnum.name,\r\n          }\r\n        );\r\n\r\n        const treeNode = this.treeNodeService.findNodeByTag(\r\n          enumNodeData.treeNodeTag\r\n        );\r\n        if (treeNode)\r\n          this.treeNodeService.editGroupTreeNode({\r\n            ...treeNode,\r\n            name: updatedTempEnum.name,\r\n            data: enumNodeData,\r\n          });\r\n      });\r\n  }\r\n\r\n  deleteTempEnumeration(enumData: GojsDiagramEnumerationNode[]) {\r\n    this.enumerationService.deleteTemplateEnums(\r\n      enumData.map((enumObj) => enumObj.idTemplateEnumeration)\r\n    );\r\n    enumData.forEach((enumObj) => {\r\n      this.diagramUtils.removeAttributeType(\r\n        enumObj.idTemplateEnumeration.toString()\r\n      );\r\n      this.goJsCommonService.removeGroupNodeWithItems(\r\n        enumObj?.idTemplateEnumeration,\r\n        enumObj.category,\r\n        this._gojsDiagram\r\n      );\r\n      this.goJsCommonService.updateNodeDataItemsProperties(\r\n        this._gojsDiagram.model,\r\n        (item) => item.dataType == enumObj.idTemplateEnumeration.toString(),\r\n        { dataType: `0_${AttributeType[AttributeType.Undefined]}` }\r\n      );\r\n    });\r\n  }\r\n\r\n  formatDiagramEnumerationData(\r\n    enumerations: EnumerationDTO[],\r\n    hasEditAccess: boolean\r\n  ): GojsDiagramEnumerationNode[] {\r\n    const nodeDataArray = enumerations.map((enumObj) => ({\r\n      idTemplateEnumeration: enumObj.idTemplateEnumeration!,\r\n      key: enumObj.key,\r\n      id: enumObj.id,\r\n      color: enumObj.property?.color,\r\n      icon: enumObj.property?.icon,\r\n      name: enumObj.name,\r\n      category: GojsNodeCategory.Enumeration,\r\n      size: enumObj.property\r\n        ? new go.Size(enumObj.property.width, enumObj.property.height)\r\n        : new go.Size(150, 100),\r\n      isGroup: true,\r\n      position: enumObj.property?.position,\r\n      supportingLevels: [GojsNodeCategory.EnumerationLiteral],\r\n      isHighlighted: true,\r\n      allowTopLevelDrops: true,\r\n      editable: hasEditAccess,\r\n      showTablePanel: true,\r\n      description: enumObj.description,\r\n      tag: enumObj.tag,\r\n      volumetry: enumObj.volumetry,\r\n      items: this.goJsLiteralService.formatLiteralData(\r\n        enumObj.enumerationLiterals,\r\n        enumObj.id!\r\n      ),\r\n      treeNodeTag: `atTag${GojsNodeCategory.Enumeration}_${enumObj.idTemplateEnumeration}`,\r\n    }));\r\n    return nodeDataArray;\r\n  }\r\n\r\n  updateEnumNode(\r\n    updatedNode: GojsDiagramEnumerationNode,\r\n    gojsDiagram: go.Diagram,\r\n    treeNode: TreeNode\r\n  ) {\r\n    let diagramNode: go.ObjectData[];\r\n    diagramNode = this._gojsDiagram.model.nodeDataArray.filter(\r\n      (item) =>\r\n        item['idTemplateEnumeration'] == updatedNode.idTemplateEnumeration\r\n    );\r\n    this.treeNodeService.editGroupTreeNode({\r\n      ...treeNode,\r\n      name: updatedNode.name,\r\n      data: { ...updatedNode },\r\n      tag: updatedNode.treeNodeTag ?? treeNode.tag,\r\n    });\r\n    if (updatedNode && updatedNode.id && diagramNode) {\r\n      const properties: UpdateProperties = {\r\n        name: updatedNode.name,\r\n        color: updatedNode.color,\r\n        description: updatedNode.description,\r\n        tag: updatedNode.tag,\r\n        volumetry: updatedNode.volumetry,\r\n      };\r\n      this.goJsCommonService.commitGroupNodeData(\r\n        diagramNode,\r\n        properties,\r\n        gojsDiagram\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles the renaming of an enumeration in the library.\r\n   *\r\n   * This method updates the name of an enumeration node in both the main diagram and the palette.\r\n   * It first sends an update request to the backend to modify the enumeration's name and other properties.\r\n   * Once the update is successful, it propagates the changes to the relevant nodes in the diagram and palette.\r\n   *\r\n   * @param enumNode - The GoJS node representing the enumeration to be renamed.\r\n   * @param enumName - The new name for the enumeration.\r\n   * @param diagram - The main GoJS diagram where the enumeration is used.\r\n   * @param palette - The GoJS palette diagram where the enumeration node resides.\r\n   */\r\n  handleEditEnumNameInLibrary(\r\n    enumNode: GojsDiagramEnumerationNode,\r\n    treeNode: TreeNode\r\n  ): void {\r\n    this.enumerationService\r\n      .updateTempEnumeration(\r\n        {\r\n          id: enumNode.idTemplateEnumeration,\r\n          key: enumNode.key?.toString().split('_')[0],\r\n          icon: enumNode.icon,\r\n          color: enumNode.color,\r\n          name: treeNode.name,\r\n        },\r\n        this.currentDiagram.id!\r\n      )\r\n      .subscribe((updatedTempEnum) => {\r\n        this.diagramUtils.updateEnumerationType(\r\n          updatedTempEnum.id?.toString()!,\r\n          updatedTempEnum.name,\r\n          this._gojsDiagram\r\n        );\r\n        // Update the name in the main diagram if the enumeration is used.\r\n        this.goJsCommonService.updateNodeDataProperties(\r\n          this._gojsDiagram.model,\r\n          (node) =>\r\n            node['idTemplateEnumeration'] == enumNode['idTemplateEnumeration'],\r\n          {\r\n            name: treeNode.name,\r\n          }\r\n        );\r\n\r\n        // Update the name in the palette.\r\n        this.treeNodeService.editGroupTreeNode({\r\n          ...treeNode,\r\n          data: {\r\n            ...treeNode.data,\r\n            name: treeNode.name,\r\n          } as GojsDiagramEnumerationNode,\r\n        });\r\n        this.propertyService.setPropertyData({\r\n          ...enumNode,\r\n          name: treeNode.name,\r\n        });\r\n      });\r\n  }\r\n\r\n  handleEnumerationCreationFromLibrary(\r\n    enumName: string,\r\n    wrapperNode: TreeNode\r\n  ): void {\r\n    const parentNode = this.treeNodeService.findNodeByTag(\r\n      wrapperNode.category === GojsNodeCategory.Folder\r\n        ? wrapperNode.tag!\r\n        : wrapperNode.parentTag!\r\n    );\r\n\r\n    const enumObj: TemplateEnumerationCreation = {\r\n      name: enumName,\r\n      key: 0,\r\n      icon: GoJsNodeIcon.Enumeration,\r\n      color: 'rgba(128,128,128,0.5)',\r\n      idProject: this._currentProject.id,\r\n      description: '',\r\n      tag: '',\r\n      volumetry: '',\r\n      idFolder:\r\n        parentNode && parentNode.category == GojsNodeCategory.Folder\r\n          ? (parentNode.data as GojsFolderNode)?.idFolder\r\n          : 0,\r\n    };\r\n\r\n    this.enumerationService\r\n      .createNewTemplateEnumeration(enumObj)\r\n      .subscribe((createdEnum) => {\r\n        this.treeNodeService.addGroupNodeInTree({\r\n          name: createdEnum.name,\r\n          children: [],\r\n          category: GojsNodeCategory.Enumeration,\r\n          icon: createdEnum.icon,\r\n          tag: `atTag${GojsNodeCategory.Enumeration}_${createdEnum.id}`,\r\n          parentTag:\r\n            parentNode && parentNode.category == GojsNodeCategory.Folder\r\n              ? parentNode.tag\r\n              : `${TreeNodeTag.EnumerationWrapper}_${TreeNodeTag.Project}`,\r\n          data: this.dataFormatService.formatTempEnumData(createdEnum),\r\n          isDraggable: true,\r\n          supportingNodes: [GojsNodeCategory.EnumerationLiteral],\r\n        });\r\n\r\n        this.diagramUtils.addAttributeTypes({\r\n          id: createdEnum.id?.toString()!,\r\n          name: createdEnum.name,\r\n          isEnumeration: true,\r\n        });\r\n      });\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,MAAM;AACrB,SAASC,aAAa,QAAQ,gCAAgC;AAC9D,SAASC,YAAY,QAAQ,6BAA6B;AAS1D,SAGEC,gBAAgB,QAEX,2BAA2B;AAElC,SAAmBC,WAAW,QAAQ,+BAA+B;;;;;;;;;;;AAarE,OAAM,MAAOC,sBAAsB;EAIjCC,YACUC,kBAAsC,EACtCC,YAA0B,EAC1BC,iBAAoC,EACpCC,kBAAsC,EACtCC,aAA4B,EAC5BC,eAAgC,EAChCC,iBAAoC,EACpCC,eAAgC,EAChCC,cAA8B;IAR9B,KAAAR,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IAEtB,IAAI,CAACP,YAAY,CAACQ,oBAAoB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;IACF,IAAI,CAACT,iBAAiB,CAACW,kBAAkB,EAAE,CAACH,SAAS,CAAEC,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACG,YAAY,GAAGH,OAAO;IAC1C,CAAC,CAAC;IACF,IAAI,CAACH,cAAc,CAACO,qBAAqB,EAAE,CAACL,SAAS,CAAEM,OAAO,IAAI;MAChE,IAAIA,OAAO,EAAE,IAAI,CAACC,eAAe,GAAGD,OAAO;IAC7C,CAAC,CAAC;EACJ;EAEAE,0BAA0BA,CACxBC,QAAoC,EACpCR,OAAmB,EACnBS,aAAsB,EACtBC,KAAqB;IAErB,IAAI,CAACF,QAAQ,GAAG,IAAI,CAAC,EACnB,IAAI,CAACG,UAAU,CAACH,QAAQ,EAAER,OAAO,EAAES,aAAa,EAAEC,KAAK,CAAC,CAAC,KACtD,IAAI,CAACE,kBAAkB,CAACJ,QAAQ,EAAEC,aAAa,CAAC;EACvD;EAEQE,UAAUA,CAChBH,QAAoC,EACpCR,OAAmB,EACnBS,aAAsB,EACtBC,KAAqB;IAErB,MAAMG,UAAU,GAAG,IAAI,CAACnB,eAAe,CAACoB,4BAA4B,EAAE;IACtE,IAAIC,OAAoB;IACxB,IACEF,UAAU,IACVA,UAAU,CAACG,QAAQ,IAAI/B,gBAAgB,CAACgC,MAAM,IAC9C,CAACR,aAAa,EACd;MACAM,OAAO,GAAG;QACR,GAAG,IAAI,CAACG,gBAAgB,CAACV,QAAQ,EAAEC,aAAa,CAAC;QACjDU,QAAQ,EAAGN,UAAU,CAAC,MAAM,CAAoB,CAACM;OAClD;KACF,MAAM;MACL,MAAMC,YAAY,GAAG,IAAI,CAAC1B,eAAe,CAAC2B,aAAa,CACrDb,QAAQ,CAACc,WAAW,CACrB;MACDP,OAAO,GAAG;QACR,GAAG,IAAI,CAACG,gBAAgB,CAACV,QAAQ,EAAEC,aAAa,CAAC;QACjDU,QAAQ,EAAE,IAAI,CAAC7B,YAAY,CAACiC,YAAY,CAACH,YAAY,EAAEI,SAAU;OAClE;;IAEH,IAAI,CAACnC,kBAAkB,CAACoC,iBAAiB,CAACV,OAAO,CAAC,CAAChB,SAAS,CAAC;MAC3D2B,IAAI,EAAGC,KAAK,IAAI;QACd,IAAI,CAACC,gBAAgB,CAACD,KAAK,EAAE3B,OAAO,EAAEQ,QAAQ,EAAEC,aAAa,CAAC;QAC9D,IAAIkB,KAAK,CAACE,mBAAmB,EAC3B,IAAI,CAACC,iCAAiC,CACpCH,KAAK,CAACE,mBAAmB,EACzBpB,aAAa,EACbI,UAAW,CACZ;MACL,CAAC;MACDkB,KAAK,EAAEA,CAAA,KAAK;QACVrB,KAAK,EAAEV,OAAO,CAACgC,WAAW,CAACC,QAAQ,EAAE;MACvC;KACD,CAAC;EACJ;EAEQf,gBAAgBA,CACtBV,QAAoC,EACpCC,aAAsB;IAEtB,MAAMM,OAAO,GAAuB;MAClCmB,IAAI,EAAE1B,QAAQ,CAAC,MAAM,CAAC;MACtB2B,GAAG,EAAE3B,QAAQ,CAAC,KAAK,CAAC;MACpB4B,SAAS,EAAE,IAAI,CAACnC,cAAc,CAACoC,EAAG;MAClCC,QAAQ,EAAE;QACRC,QAAQ,EAAE/B,QAAQ,CAAC,UAAU,CAAE;QAC/BgC,MAAM,EAAEhC,QAAQ,CAAC,MAAM,CAAC,CAACgC,MAAM;QAC/BC,KAAK,EAAEjC,QAAQ,CAAC,MAAM,CAAC,CAACiC,KAAK;QAC7BC,IAAI,EAAElC,QAAQ,CAAC,MAAM,CAAC;QACtBmC,KAAK,EAAEnC,QAAQ,CAAC,OAAO;OACxB;MACDoC,WAAW,EAAEpC,QAAQ,CAAC,aAAa,CAAC;MACpCqC,GAAG,EAAErC,QAAQ,CAAC,KAAK,CAAC;MACpBsC,SAAS,EAAEtC,QAAQ,CAAC,WAAW;KAChC;IACD,OAAOC,aAAa,GAChB;MACE,GAAGM,OAAO;MACVgC,qBAAqB,EAAEvC,QAAQ,CAACuC,qBAAqB;MACrDZ,GAAG,EAAE,CAAC,IAAI,CAAC1C,aAAa,CAACuD,iBAAiB;KAC3C,GACDjC,OAAO;EACb;EAEQa,gBAAgBA,CACtBD,KAAyB,EACzBsB,WAAuB,EACvBzC,QAAuB,EACvBC,aAAsB;IAEtB,IAAIA,aAAa,EAAE;MACjBwC,WAAW,CAACC,KAAK,CAACC,WAAW,CAAC3C,QAAQ,CAAC;;IAEzC,MAAM4C,WAAW,GAAG,IAAI,CAAC9D,YAAY,CAAC+D,kBAAkB,CACtDJ,WAAW,EACXzC,QAAQ,CAAC,KAAK,CAAC,CAChB;IACD,IAAI4C,WAAW,EAAE;MACf,IAAI,CAAC7D,iBAAiB,CAAC+D,iBAAiB,CAACL,WAAW,CAACC,KAAK,EAAEE,WAAW,EAAE;QACvEf,EAAE,EAAEV,KAAK,CAACU,EAAE;QACZU,qBAAqB,EAAEpB,KAAK,CAACE,mBAAmB,EAAEQ,EAAE;QACpDf,WAAW,EAAE,QAAQrC,gBAAgB,CAACsE,WAAW,IAAI5B,KAAK,CAACE,mBAAmB,EAAEQ,EAAE;OACnF,CAAC;;EAEN;EAEQP,iCAAiCA,CACvC0B,QAA6B,EAC7B/C,aAAsB,EACtBI,UAAqB;IAErB,IAAI,CAACJ,aAAa,EAAE;MAClB,IAAI,CAACf,eAAe,CAAC+D,kBAAkB,CAAC;QACtCvB,IAAI,EAAEsB,QAAQ,CAACtB,IAAI;QACnBlB,QAAQ,EAAE/B,gBAAgB,CAACsE,WAAW;QACtCV,GAAG,EAAE,QAAQ5D,gBAAgB,CAACsE,WAAW,IAAIC,QAAQ,CAACnB,EAAE,EAAE;QAC1DqB,QAAQ,EAAE,EAAE;QACZhB,IAAI,EAAE1D,YAAY,CAACuE,WAAW;QAC9BI,IAAI,EAAE,IAAI,CAAChE,iBAAiB,CAACiE,qBAAqB,CAACJ,QAAQ,CAAC;QAC5DhC,SAAS,EACPX,UAAU,EAAEG,QAAQ,KAAK/B,gBAAgB,CAAC4E,OAAO,GAC7ChD,UAAU,EAAEgC,GAAG,GACf,IAAI,CAACnD,eAAe,CAACoE,mBAAmB,CACtC5E,WAAW,CAAC6E,kBAAkB,CAC/B;QACPC,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,CAAChF,gBAAgB,CAACiF,kBAAkB;OACtD,CAAC;MACF,IAAI,CAAC5E,YAAY,CAAC6E,iBAAiB,CAAC;QAClC9B,EAAE,EAAEmB,QAAQ,CAACnB,EAAE,EAAE+B,QAAQ,EAAG;QAC5BlC,IAAI,EAAEsB,QAAQ,EAAEtB,IAAI;QACpBmC,aAAa,EAAE;OAChB,CAAC;;EAEN;EAEAzD,kBAAkBA,CAChBJ,QAAoC,EACpCC,aAAsB;IAEtB,IAAI,CAACpB,kBAAkB,CAACiF,iBAAiB,CAAC;MACxC,GAAG,IAAI,CAACpD,gBAAgB,CAACV,QAAQ,EAAEC,aAAa,CAAC;MACjD4B,EAAE,EAAE7B,QAAQ,CAAC,IAAI;KAClB,CAAC;EACJ;EAEA+D,4BAA4BA,CAC1BC,YAAwC,EACxCC,WAAuB;IAEvB,MAAMC,eAAe,GAAG;MACtBrC,EAAE,EAAEmC,YAAY,CAACzB,qBAAqB;MACtCb,IAAI,EAAEsC,YAAY,CAACtC,IAAI;MACvBC,GAAG,EAAEqC,YAAY,CAACrC,GAAG;MACrBQ,KAAK,EAAE6B,YAAY,CAAC7B,KAAK;MACzBD,IAAI,EAAE8B,YAAY,CAAC9B,IAAI;MACvBE,WAAW,EAAE4B,YAAY,CAAC5B,WAAW;MACrCC,GAAG,EAAE2B,YAAY,CAAC3B,GAAG;MACrBC,SAAS,EAAE0B,YAAY,CAAC1B;KACzB;IACD,IAAI,CAACzD,kBAAkB,CACpBsF,qBAAqB,CACpBD,eAAsC,EACtC,IAAI,CAACzE,cAAc,CAACoC,EAAG,CACxB,CACAtC,SAAS,CAAE6E,eAAe,IAAI;MAC7B,IAAI,CAACtF,YAAY,CAACuF,qBAAqB,CACrCD,eAAe,CAACvC,EAAE,EAAE+B,QAAQ,EAAG,EAC/BQ,eAAe,CAAC1C,IAAI,EACpBuC,WAAW,CACZ;MACD,IAAI,CAAClF,iBAAiB,CAACuF,wBAAwB,CAC7CL,WAAW,CAACvB,KAAK,EAChB6B,IAAI,IAAKA,IAAI,CAAC,uBAAuB,CAAC,IAAIH,eAAe,CAACvC,EAAE,EAC7D;QACEH,IAAI,EAAE0C,eAAe,CAAC1C;OACvB,CACF;MAED,MAAM8C,QAAQ,GAAG,IAAI,CAACtF,eAAe,CAAC2B,aAAa,CACjDmD,YAAY,CAAClD,WAAW,CACzB;MACD,IAAI0D,QAAQ,EACV,IAAI,CAACtF,eAAe,CAACuF,iBAAiB,CAAC;QACrC,GAAGD,QAAQ;QACX9C,IAAI,EAAE0C,eAAe,CAAC1C,IAAI;QAC1ByB,IAAI,EAAEa;OACP,CAAC;IACN,CAAC,CAAC;EACN;EAEAU,qBAAqBA,CAAC1E,QAAsC;IAC1D,IAAI,CAACnB,kBAAkB,CAAC8F,mBAAmB,CACzC3E,QAAQ,CAAC4E,GAAG,CAAErE,OAAO,IAAKA,OAAO,CAACgC,qBAAqB,CAAC,CACzD;IACDvC,QAAQ,CAAC6E,OAAO,CAAEtE,OAAO,IAAI;MAC3B,IAAI,CAACzB,YAAY,CAACgG,mBAAmB,CACnCvE,OAAO,CAACgC,qBAAqB,CAACqB,QAAQ,EAAE,CACzC;MACD,IAAI,CAAC7E,iBAAiB,CAACgG,wBAAwB,CAC7CxE,OAAO,EAAEgC,qBAAqB,EAC9BhC,OAAO,CAACC,QAAQ,EAChB,IAAI,CAACb,YAAY,CAClB;MACD,IAAI,CAACZ,iBAAiB,CAACiG,6BAA6B,CAClD,IAAI,CAACrF,YAAY,CAAC+C,KAAK,EACtBuC,IAAI,IAAKA,IAAI,CAACC,QAAQ,IAAI3E,OAAO,CAACgC,qBAAqB,CAACqB,QAAQ,EAAE,EACnE;QAAEsB,QAAQ,EAAE,KAAK3G,aAAa,CAACA,aAAa,CAAC4G,SAAS,CAAC;MAAE,CAAE,CAC5D;IACH,CAAC,CAAC;EACJ;EAEAC,4BAA4BA,CAC1BC,YAA8B,EAC9BC,aAAsB;IAEtB,MAAMC,aAAa,GAAGF,YAAY,CAACT,GAAG,CAAErE,OAAO,KAAM;MACnDgC,qBAAqB,EAAEhC,OAAO,CAACgC,qBAAsB;MACrDZ,GAAG,EAAEpB,OAAO,CAACoB,GAAG;MAChBE,EAAE,EAAEtB,OAAO,CAACsB,EAAE;MACdM,KAAK,EAAE5B,OAAO,CAACuB,QAAQ,EAAEK,KAAK;MAC9BD,IAAI,EAAE3B,OAAO,CAACuB,QAAQ,EAAEI,IAAI;MAC5BR,IAAI,EAAEnB,OAAO,CAACmB,IAAI;MAClBlB,QAAQ,EAAE/B,gBAAgB,CAACsE,WAAW;MACtCyC,IAAI,EAAEjF,OAAO,CAACuB,QAAQ,GAClB,IAAIxD,EAAE,CAACmH,IAAI,CAAClF,OAAO,CAACuB,QAAQ,CAACG,KAAK,EAAE1B,OAAO,CAACuB,QAAQ,CAACE,MAAM,CAAC,GAC5D,IAAI1D,EAAE,CAACmH,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;MACzBC,OAAO,EAAE,IAAI;MACb3D,QAAQ,EAAExB,OAAO,CAACuB,QAAQ,EAAEC,QAAQ;MACpC4D,gBAAgB,EAAE,CAAClH,gBAAgB,CAACiF,kBAAkB,CAAC;MACvDkC,aAAa,EAAE,IAAI;MACnBC,kBAAkB,EAAE,IAAI;MACxBC,QAAQ,EAAER,aAAa;MACvBS,cAAc,EAAE,IAAI;MACpB3D,WAAW,EAAE7B,OAAO,CAAC6B,WAAW;MAChCC,GAAG,EAAE9B,OAAO,CAAC8B,GAAG;MAChBC,SAAS,EAAE/B,OAAO,CAAC+B,SAAS;MAC5B0D,KAAK,EAAE,IAAI,CAAChH,kBAAkB,CAACiH,iBAAiB,CAC9C1F,OAAO,CAAC2F,mBAAmB,EAC3B3F,OAAO,CAACsB,EAAG,CACZ;MACDf,WAAW,EAAE,QAAQrC,gBAAgB,CAACsE,WAAW,IAAIxC,OAAO,CAACgC,qBAAqB;KACnF,CAAC,CAAC;IACH,OAAOgD,aAAa;EACtB;EAEAY,cAAcA,CACZC,WAAuC,EACvC3D,WAAuB,EACvB+B,QAAkB;IAElB,IAAI6B,WAA4B;IAChCA,WAAW,GAAG,IAAI,CAAC1G,YAAY,CAAC+C,KAAK,CAAC6C,aAAa,CAACe,MAAM,CACvDrB,IAAI,IACHA,IAAI,CAAC,uBAAuB,CAAC,IAAImB,WAAW,CAAC7D,qBAAqB,CACrE;IACD,IAAI,CAACrD,eAAe,CAACuF,iBAAiB,CAAC;MACrC,GAAGD,QAAQ;MACX9C,IAAI,EAAE0E,WAAW,CAAC1E,IAAI;MACtByB,IAAI,EAAE;QAAE,GAAGiD;MAAW,CAAE;MACxB/D,GAAG,EAAE+D,WAAW,CAACtF,WAAW,IAAI0D,QAAQ,CAACnC;KAC1C,CAAC;IACF,IAAI+D,WAAW,IAAIA,WAAW,CAACvE,EAAE,IAAIwE,WAAW,EAAE;MAChD,MAAME,UAAU,GAAqB;QACnC7E,IAAI,EAAE0E,WAAW,CAAC1E,IAAI;QACtBS,KAAK,EAAEiE,WAAW,CAACjE,KAAK;QACxBC,WAAW,EAAEgE,WAAW,CAAChE,WAAW;QACpCC,GAAG,EAAE+D,WAAW,CAAC/D,GAAG;QACpBC,SAAS,EAAE8D,WAAW,CAAC9D;OACxB;MACD,IAAI,CAACvD,iBAAiB,CAACyH,mBAAmB,CACxCH,WAAW,EACXE,UAAU,EACV9D,WAAW,CACZ;;EAEL;EAEA;;;;;;;;;;;;EAYAgE,2BAA2BA,CACzBC,QAAoC,EACpClC,QAAkB;IAElB,IAAI,CAAC3F,kBAAkB,CACpBsF,qBAAqB,CACpB;MACEtC,EAAE,EAAE6E,QAAQ,CAACnE,qBAAqB;MAClCZ,GAAG,EAAE+E,QAAQ,CAAC/E,GAAG,EAAEiC,QAAQ,EAAE,CAAC+C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3CzE,IAAI,EAAEwE,QAAQ,CAACxE,IAAI;MACnBC,KAAK,EAAEuE,QAAQ,CAACvE,KAAK;MACrBT,IAAI,EAAE8C,QAAQ,CAAC9C;KAChB,EACD,IAAI,CAACjC,cAAc,CAACoC,EAAG,CACxB,CACAtC,SAAS,CAAE6E,eAAe,IAAI;MAC7B,IAAI,CAACtF,YAAY,CAACuF,qBAAqB,CACrCD,eAAe,CAACvC,EAAE,EAAE+B,QAAQ,EAAG,EAC/BQ,eAAe,CAAC1C,IAAI,EACpB,IAAI,CAAC/B,YAAY,CAClB;MACD;MACA,IAAI,CAACZ,iBAAiB,CAACuF,wBAAwB,CAC7C,IAAI,CAAC3E,YAAY,CAAC+C,KAAK,EACtB6B,IAAI,IACHA,IAAI,CAAC,uBAAuB,CAAC,IAAImC,QAAQ,CAAC,uBAAuB,CAAC,EACpE;QACEhF,IAAI,EAAE8C,QAAQ,CAAC9C;OAChB,CACF;MAED;MACA,IAAI,CAACxC,eAAe,CAACuF,iBAAiB,CAAC;QACrC,GAAGD,QAAQ;QACXrB,IAAI,EAAE;UACJ,GAAGqB,QAAQ,CAACrB,IAAI;UAChBzB,IAAI,EAAE8C,QAAQ,CAAC9C;;OAElB,CAAC;MACF,IAAI,CAACtC,eAAe,CAACwH,eAAe,CAAC;QACnC,GAAGF,QAAQ;QACXhF,IAAI,EAAE8C,QAAQ,CAAC9C;OAChB,CAAC;IACJ,CAAC,CAAC;EACN;EAEAmF,oCAAoCA,CAClCC,QAAgB,EAChBC,WAAqB;IAErB,MAAM1G,UAAU,GAAG,IAAI,CAACnB,eAAe,CAAC2B,aAAa,CACnDkG,WAAW,CAACvG,QAAQ,KAAK/B,gBAAgB,CAACgC,MAAM,GAC5CsG,WAAW,CAAC1E,GAAI,GAChB0E,WAAW,CAAC/F,SAAU,CAC3B;IAED,MAAMT,OAAO,GAAgC;MAC3CmB,IAAI,EAAEoF,QAAQ;MACdnF,GAAG,EAAE,CAAC;MACNO,IAAI,EAAE1D,YAAY,CAACuE,WAAW;MAC9BZ,KAAK,EAAE,uBAAuB;MAC9B6E,SAAS,EAAE,IAAI,CAAClH,eAAe,CAAC+B,EAAE;MAClCO,WAAW,EAAE,EAAE;MACfC,GAAG,EAAE,EAAE;MACPC,SAAS,EAAE,EAAE;MACb3B,QAAQ,EACNN,UAAU,IAAIA,UAAU,CAACG,QAAQ,IAAI/B,gBAAgB,CAACgC,MAAM,GACvDJ,UAAU,CAAC8C,IAAuB,EAAExC,QAAQ,GAC7C;KACP;IAED,IAAI,CAAC9B,kBAAkB,CACpBoI,4BAA4B,CAAC1G,OAAO,CAAC,CACrChB,SAAS,CAAE2H,WAAW,IAAI;MACzB,IAAI,CAAChI,eAAe,CAAC+D,kBAAkB,CAAC;QACtCvB,IAAI,EAAEwF,WAAW,CAACxF,IAAI;QACtBwB,QAAQ,EAAE,EAAE;QACZ1C,QAAQ,EAAE/B,gBAAgB,CAACsE,WAAW;QACtCb,IAAI,EAAEgF,WAAW,CAAChF,IAAI;QACtBG,GAAG,EAAE,QAAQ5D,gBAAgB,CAACsE,WAAW,IAAImE,WAAW,CAACrF,EAAE,EAAE;QAC7Db,SAAS,EACPX,UAAU,IAAIA,UAAU,CAACG,QAAQ,IAAI/B,gBAAgB,CAACgC,MAAM,GACxDJ,UAAU,CAACgC,GAAG,GACd,GAAG3D,WAAW,CAAC6E,kBAAkB,IAAI7E,WAAW,CAAC2E,OAAO,EAAE;QAChEF,IAAI,EAAE,IAAI,CAAChE,iBAAiB,CAACgI,kBAAkB,CAACD,WAAW,CAAC;QAC5D1D,WAAW,EAAE,IAAI;QACjBC,eAAe,EAAE,CAAChF,gBAAgB,CAACiF,kBAAkB;OACtD,CAAC;MAEF,IAAI,CAAC5E,YAAY,CAAC6E,iBAAiB,CAAC;QAClC9B,EAAE,EAAEqF,WAAW,CAACrF,EAAE,EAAE+B,QAAQ,EAAG;QAC/BlC,IAAI,EAAEwF,WAAW,CAACxF,IAAI;QACtBmC,aAAa,EAAE;OAChB,CAAC;IACJ,CAAC,CAAC;EACN;EAAC,QAAAuD,CAAA,G;qBAxZUzI,sBAAsB,EAAA0I,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,kBAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,iBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,eAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAtB9J,sBAAsB;IAAA+J,OAAA,EAAtB/J,sBAAsB,CAAAgK,IAAA;IAAAC,UAAA,EAFrB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}