import { SelectionModel } from '@angular/cdk/collections';
import { Injectable } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { GoJsNodeIcon } from 'src/app/shared/model/common';
import { Diagram } from 'src/app/shared/model/diagram';
import {
  ConfirmDialogData,
  DiagramDialogData,
  DiagramDialogResult,
} from 'src/app/shared/model/dialog';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { AccessType } from 'src/app/shared/model/project';
import {
  ContextMenuAction,
  TreeNode,
  TreeNodeTag,
} from 'src/app/shared/model/treeNode';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { DiagramDialogComponent } from '../../components/diagram-dialog/diagram-dialog.component';
import { DialogConfirmationComponent } from '../../components/dialog-confirmation/dialog-confirmation.component';
import { AccessService } from '../access/access.service';
import { DiagramService } from '../diagram/diagram.service';
import { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';
import { GojsClassService } from '../gojs/gojsClass/gojs-class.service';
import { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';
import { GojsFolderService } from '../gojs/gojsFolder/gojs-folder.service';
import { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';
import { ProjectService } from '../project/project.service';
import { PropertyService } from '../property/property.service';
import { SnackBarService } from '../snackbar/snack-bar.service';
import { TreeNodeService } from '../treeNode/tree-node.service';

@Injectable({
  providedIn: 'root',
})
export class ContextMenuActionService {
  currentDiagram!: Diagram;
  diagrams: Diagram[] = [];
  private projectId: number = -1;
  private baseFileName = 'class-diagram';
  private _hasEditAccessOnly: boolean = false;
  constructor(
    private dialog: MatDialog,
    private snackBarService: SnackBarService,
    private projectService: ProjectService,
    private diagramService: DiagramService,
    private diagramUtils: DiagramUtils,
    private treeNodeService: TreeNodeService,
    private goJsClassService: GojsClassService,
    private gojsEnumerationService: GojsEnumerationService,
    private gojsLiteralService: GojsLiteralService,
    private gojsAttributeService: GojsAttributeService,
    private gojsFolderService: GojsFolderService,
    private propertyService: PropertyService,
    private _accessService: AccessService,
    private router: Router
  ) {
    this.projectService.currentProjectChanges().subscribe((project) => {
      if (project && project.id) {
        this.projectId = project.id;
      }
    });
    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {
      if (diagram) this.currentDiagram = diagram;
    });
    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {
      this.diagrams = diagrams;
    });
    this._accessService.accessTypeChanges().subscribe((access) => {
      this._hasEditAccessOnly = access != AccessType.Viewer;
    });
  }

  executeAction(
    actionName: ContextMenuAction,
    node: TreeNode,
    selection: SelectionModel<TreeNode>
  ): void {
    switch (actionName) {
      case ContextMenuAction.AddDiagram:
        this.openDiagramDialog(
          {
            name: node.data?.name!,
            targetedNode: node,
          },
          true
        );
        break;
      case ContextMenuAction.EditDiagram:
        this.openDiagramDialog(
          { name: node.data?.name!, id: node.data?.id! },
          false
        );
        break;
      case ContextMenuAction.DeleteDiagram:
        this.onDeleteDiagram(node);
        break;
      case ContextMenuAction.AddLiteral:
        if (node.data && 'idTemplateEnumeration' in node.data) {
          this.openNodeCreationDialog(
            {
              headerTitle: 'literal.header',
              placeholder: 'literal.placeholder',
              text: 'Literal',
              btnText: '',
              isCreation: true,
            },
            node,
            actionName
          );
        }
        break;
      case ContextMenuAction.DeleteFolder:
      case ContextMenuAction.DeleteClass:
      case ContextMenuAction.DeleteEnumeration:
      case ContextMenuAction.DeleteAttribute:
      case ContextMenuAction.DeleteMethod:
      case ContextMenuAction.DeleteLiteral:
        // Check if the node is already in the selection to avoid duplication
        const nodesToDelete = selection.isSelected(node)
          ? [...selection.selected]
          : [...selection.selected, node];
        this.deleteSelectedNodes(nodesToDelete);
        break;
      case ContextMenuAction.AddAttribute:
        if (node.data && 'idTemplateClass' in node.data) {
          this.openNodeCreationDialog(
            {
              headerTitle: 'attribute.header',
              placeholder: 'attribute.placeholder',
              text: 'Attribute',
              btnText: '',
              isCreation: true,
              isAttribute: true,
            },
            node,
            actionName
          );
        }
        break;
      case ContextMenuAction.AddOperation:
        if (node.data && 'idTemplateClass' in node.data) {
          this.openNodeCreationDialog(
            {
              headerTitle: 'method.header',
              placeholder: 'method.placeholder',
              text: 'Method',
              btnText: '',
              isCreation: true,
              isAttribute: true,
            },
            node,
            actionName
          );
        }
        break;
      case ContextMenuAction.AddFolder:
        this.openNodeCreationDialog(
          {
            headerTitle: 'folder.header',
            placeholder: 'folder.placeholder',
            text: 'New Folder',
            btnText: '',
            isCreation: true,
          },
          node,
          actionName
        );
        break;
      case ContextMenuAction.AddClass:
      case ContextMenuAction.AddAssociative:
        this.openNodeCreationDialog(
          {
            headerTitle: 'class.header',
            placeholder: 'class.placeholder',
            text: 'Class',
            btnText: '',
            isCreation: true,
          },
          node,
          actionName
        );
        break;
      case ContextMenuAction.AddEnumeration:
        this.openNodeCreationDialog(
          {
            headerTitle: 'enumeration.header',
            placeholder: 'enumeration.placeholder',
            text: 'Enumeration',
            btnText: '',
            isCreation: true,
          },
          node,
          actionName
        );

        break;
      default:
        break;
    }
  }

  private openNodeCreationDialog(
    data: DiagramDialogData,
    node: TreeNode,
    actionName: ContextMenuAction
  ): void {
    const dialogRef = this.dialog.open<
      DiagramDialogComponent,
      DiagramDialogData,
      DiagramDialogResult
    >(DiagramDialogComponent, {
      width: '300px',
      data: {
        text: data.text || '',
        headerTitle: data.headerTitle,
        placeholder: data.placeholder,
        btnText: 'diagram.create',
        isCreation: true,
        isAttribute: data.isAttribute,
      },
    });

    dialogRef.afterClosed().subscribe((createdData) => {
      if (createdData) {
        switch (actionName) {
          case ContextMenuAction.AddFolder:
            this.gojsFolderService.onCreateNewFolder(
              createdData.name,
              this.projectId,
              this._hasEditAccessOnly,
              node.tag
            );
            break;
          case ContextMenuAction.AddClass:
          case ContextMenuAction.AddAssociative:
            this.goJsClassService.handleClassCreationFromLibrary(
              createdData.name,
              node,
              actionName === ContextMenuAction.AddAssociative
            );
            break;
          case ContextMenuAction.AddEnumeration:
            this.gojsEnumerationService.handleEnumerationCreationFromLibrary(
              createdData.name,
              node
            );
            break;
          case ContextMenuAction.AddAttribute:
            if (node.data && 'idTemplateClass' in node.data) {
              this.gojsAttributeService.addAttributeOrMethodFromLibrary(
                createdData.name,
                GojsNodeCategory.Attribute,
                node.data.idTemplateClass,
                createdData.dataType!
              );
            }
            break;
          case ContextMenuAction.AddOperation:
            if (node.data && 'idTemplateClass' in node.data) {
              this.gojsAttributeService.addAttributeOrMethodFromLibrary(
                createdData.name,
                GojsNodeCategory.Operation,
                node.data.idTemplateClass,
                createdData.dataType!
              );
            }
            break;
          case ContextMenuAction.AddLiteral:
            this.gojsLiteralService.addLiteralInLibrary(
              createdData.name,
              node.data as GojsDiagramEnumerationNode
            );
            break;
        }
      }
    });
  }
  private openDiagramDialog(
    data: { name: string; id?: number; targetedNode?: TreeNode },
    isCreation: boolean
  ): void {
    const dialogRef = this.dialog.open<
      DiagramDialogComponent,
      DiagramDialogData,
      DiagramDialogResult
    >(DiagramDialogComponent, {
      width: '300px',
      data: {
        text: isCreation ? this.generateFileName() : data.name || '',
        headerTitle: isCreation
          ? 'diagram.createDiagram'
          : 'diagram.editDiagram',
        placeholder: 'diagram.diagramPlaceholderText',
        btnText: isCreation ? 'diagram.create' : 'diagram.save',
        isCreation: isCreation,
      },
    });

    dialogRef.afterClosed().subscribe((diagramResult) => {
      if (!diagramResult) return;
      if (diagramResult.isCreation) {
        // const parentNode = this.treeNodeService.findCurrentDiagramParentNode(
        //   `atTag${GojsNodeCategory.Diagram}_${this.currentDiagram.id}`
        // );
        this.diagramService
          .createDiagram({
            idProject: this.projectId,
            name: diagramResult.name,
            idFolder:
              data.targetedNode &&
              data.targetedNode.category == GojsNodeCategory.Folder
                ? (data.targetedNode.data as GojsFolderNode)?.idFolder
                : 0,
          })
          .subscribe((createdDiagram: Diagram) => {
            this.diagrams.push(createdDiagram);
            this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);
            this.diagramUtils.setActiveDiagram(createdDiagram);
            this.treeNodeService.addGroupNodeInTree({
              name: createdDiagram.name,
              children: [],
              category: GojsNodeCategory.Diagram,
              icon: GoJsNodeIcon.Diagram,
              tag: `atTag${GojsNodeCategory.Diagram}_${createdDiagram.id}`,
              parentTag:
                data.targetedNode &&
                data.targetedNode.category == GojsNodeCategory.Project
                  ? `${TreeNodeTag.DiagramWrapper}_${TreeNodeTag.Project}`
                  : `${data.targetedNode?.tag}`,
              data: createdDiagram,
              isDraggable: true,
              supportingNodes: [GojsNodeCategory.Diagram],
            });
            this.propertyService.setPropertyData(null);
            if (createdDiagram.id) {
              this.currentDiagram = createdDiagram;

              // Update the URL to reflect the newly created diagram
              // This is especially important when this is the first diagram in a project
              // and the URL currently has the placeholder diagram ID (0)
              this.router.navigate(
                [`/editor/${this.projectId}/diagram/${createdDiagram.id}`],
                {
                  replaceUrl: true, // Replace the current URL instead of adding a new history entry
                }
              );
            }
          });
      } else {
        this.updateDiagram(data.id!, diagramResult.name);
      }
    });
  }
  private updateDiagram(diagramId: number, diagramName: string) {
    if (this._hasEditAccessOnly) {
      this.diagramService
        .updateDiagram({
          id: diagramId,
          name: diagramName,
        })
        .subscribe((updatedDiagram) => {
          const index = this.diagrams.findIndex(
            (value) => value.id == this.currentDiagram!.id!
          );
          if (index !== -1) {
            this.treeNodeService.editGroupTreeNode({
              name: updatedDiagram.name,
              children: [],
              category: GojsNodeCategory.Diagram,
              icon: GoJsNodeIcon.Diagram,
              tag: `atTag${GojsNodeCategory.Diagram}_${updatedDiagram.id}`,
              data: { ...this.diagrams[index], name: updatedDiagram.name },
              isDraggable: true,
              supportingNodes: [GojsNodeCategory.Diagram],
            });
            this.diagrams[index].name = updatedDiagram.name;
          }

          this.diagramUtils.setActiveDiagram(this.diagrams[index]);
          this.snackBarService.openSnackbar('snackBar.diagramUpdateMsg');
        });
    }
  }

  private generateFileName(): string {
    const existingNames = new Set(this.diagrams.map((diagram) => diagram.name));
    let newFileName = this.baseFileName;
    let count = 1;
    while (existingNames.has(newFileName)) {
      newFileName = `${this.baseFileName}${count}`;
      count++;
    }
    return newFileName;
  }

  onDeleteDiagram(node: TreeNode) {
    const dialogRef = this.dialog.open<
      DialogConfirmationComponent,
      ConfirmDialogData,
      boolean
    >(DialogConfirmationComponent, {
      width: '320px',
      data: {
        title: 'dialog.title',
        reject: 'dialog.no',
        confirm: 'dialog.yes',
      },
    });
    dialogRef.afterClosed().subscribe((isConfirm) => {
      if (isConfirm) {
        if (node && node.data?.id) {
          this.diagramService.deleteDiagram(node.data?.id);
          this.treeNodeService.deleteGroupTreeNode(node);
          this.diagrams = this.diagrams.filter(
            (diagram: Diagram) => diagram.id !== node.data?.id
          );
          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);
          if (this.diagrams.length > 0) {
            this.currentDiagram = this.diagrams[0];
            this.diagramUtils.setActiveDiagram(this.currentDiagram);

            // Update the URL to reflect the new active diagram
            this.router.navigate(
              [`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],
              {
                replaceUrl: true,
              }
            );
          } else {
            this.diagramUtils.setActiveDiagram(null);

            // If there are no diagrams left, navigate to a placeholder URL
            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {
              replaceUrl: true,
            });
          }
        }
      }
    });
  }

  renameNode(node: TreeNode): void {
    if (node.name.trim().length === 0) return;
    switch (node.category) {
      case GojsNodeCategory.Diagram:
        this.updateDiagram(node.data?.id!, node.name);
        break;
      case GojsNodeCategory.Class:
      case GojsNodeCategory.AssociativeClass:
        this.goJsClassService.handleEditClassNameInLibrary(
          node.data as GojsDiagramClassNode,
          node
        );
        break;
      case GojsNodeCategory.Enumeration:
        this.gojsEnumerationService.handleEditEnumNameInLibrary(
          node.data as GojsDiagramEnumerationNode,
          node
        );
        break;
      case GojsNodeCategory.Folder:
        this.gojsFolderService.handleFolderEdit(
          node.data as GojsFolderNode,
          node
        );
        break;
      case GojsNodeCategory.Attribute:
      case GojsNodeCategory.Operation:
        this.gojsAttributeService.handleEditAttributeNameInLibrary(
          node.data as GojsDiagramAttributeNode,
          node
        );
        break;
      case GojsNodeCategory.EnumerationLiteral:
        this.gojsLiteralService.handleEditAttributeNameInLibrary(
          node.data as GojsDiagramLiteralNode,
          node
        );
        break;
    }
  }

  deleteSelectedNodes(selectedNodes: TreeNode[]) {
    this.doDeleteSelectedNodes(selectedNodes);
    selectedNodes.forEach((node) => {
      this.treeNodeService.deleteGroupTreeNode(node);
    });
    this.propertyService.setPropertyData(null);
  }

  private doDeleteSelectedNodes(selectedNodes: TreeNode[]) {
    const classNodes: GojsDiagramClassNode[] = [],
      enumNodes: GojsDiagramEnumerationNode[] = [],
      attributeNodes: go.ObjectData[] = [],
      literalNodes: GojsDiagramLiteralNode[] = [],
      folderNodes: TreeNode[] = [];
    selectedNodes.forEach((node) => {
      switch (node.category) {
        case GojsNodeCategory.Class:
        case GojsNodeCategory.AssociativeClass:
          if (
            !this.treeNodeService.nodeExistOrNot(node.parentTag!, selectedNodes)
          )
            classNodes.push(node.data! as GojsDiagramClassNode);
          break;
        case GojsNodeCategory.Enumeration:
          if (
            !this.treeNodeService.nodeExistOrNot(node.parentTag!, selectedNodes)
          )
            enumNodes.push(node.data! as GojsDiagramEnumerationNode);
          break;
        case GojsNodeCategory.Attribute:
        case GojsNodeCategory.Operation:
          if (!this.checkNodeExists(node.parentTag!, selectedNodes)) {
            attributeNodes.push(node.data!);
          }
          break;
        case GojsNodeCategory.EnumerationLiteral:
          if (!this.checkNodeExists(node.parentTag!, selectedNodes))
            literalNodes.push(node.data! as GojsDiagramLiteralNode);
          break;
        case GojsNodeCategory.Folder:
          if (!this.treeNodeService.nodeExistOrNot(node.tag!, selectedNodes))
            folderNodes.push(node);
          break;

        default:
          break;
      }
    });
    if (classNodes.length > 0) {
      this.goJsClassService.deleteTempClass(classNodes);
    }
    if (enumNodes.length > 0) {
      this.gojsEnumerationService.deleteTempEnumeration(enumNodes);
    }
    if (attributeNodes.length > 0) {
      this.gojsAttributeService.deleteMultipleAttrInPalette(attributeNodes);
    }
    if (literalNodes.length > 0) {
      this.gojsLiteralService.deletePaletteLiteral(literalNodes);
    }
    if (folderNodes.length > 0) {
      this.gojsFolderService.deleteSelectedFolder(folderNodes);
    }
  }

  private checkNodeExists(tag: string, nodes: TreeNode[]): boolean {
    return nodes.some((node) => node.tag === tag);
  }
}
