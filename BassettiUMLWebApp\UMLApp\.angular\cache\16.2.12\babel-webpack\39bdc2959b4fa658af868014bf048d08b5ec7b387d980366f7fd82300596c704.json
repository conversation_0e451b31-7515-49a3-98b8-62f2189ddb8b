{"ast": null, "code": "import go from 'gojs';\nimport { BehaviorSubject } from 'rxjs';\nimport { AttributeType } from '../model/attribute';\nimport { GojsNodeCategory } from '../model/gojs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/shared.service\";\n/**\n * Service for managing and manipulating diagrams, links, and attribute types in a GoJS-based diagram editor.\n * It provides utility methods for managing diagram-related data, such as nodes, links, and attribute types,\n * as well as handling active diagrams and their associated details.\n */\nexport class DiagramUtils {\n  constructor(shared) {\n    this.shared = shared;\n    // Private variables for tracking diagram data and attributes\n    this._deletedLinks = [];\n    this._currentProjectDiagramsSubject = new BehaviorSubject([]);\n    this._activeDiagramSubject = new BehaviorSubject(null);\n    this._attributeTypes = new BehaviorSubject([]);\n    this._activeDiagramDetailsSubject = new BehaviorSubject(null);\n    this._attributeTypes.next(Object.keys(AttributeType).filter(key => isNaN(Number(key)) && AttributeType[key] !== AttributeType.Enumeration).map(key => {\n      return {\n        id: `${AttributeType[key]}_${key}`,\n        name: key,\n        isEnumeration: false\n      };\n    }));\n  }\n  /**\n   * Adds a deleted link to the internal collection.\n   *\n   * This method stores a deleted link by appending it to the `_deletedLinks` array.\n   *\n   * @param link - The `DeletedLink` object representing the deleted link.\n   */\n  addDeletedLink(link) {\n    this._deletedLinks.push(link);\n  }\n  /**\n   * Removes a deleted link from the internal collection by its ID.\n   *\n   * This method filters out the deleted link matching the specified `idDeletedLink`\n   * from the `_deletedLinks` array.\n   *\n   * @param idDeletedLink - The unique identifier of the deleted link to be removed.\n   */\n  removeDeletedLink(idDeletedLink) {\n    this._deletedLinks = this._deletedLinks.filter(link => link.id != idDeletedLink);\n  }\n  /**\n   * Retrieves the list of deleted links.\n   *\n   * This method returns all the deleted links stored in the `_deletedLinks` array.\n   *\n   * @returns An array of `DeletedLink` objects representing the deleted links.\n   */\n  getDeletedLinks() {\n    return this._deletedLinks;\n  }\n  /**\n   * Sets the list of diagrams associated with the current project.\n   *\n   * This method emits the provided `diagrams` to the `_currentProjectDiagramsSubject`.\n   *\n   * @param diagrams - An array of `Diagram` objects representing the current project diagrams.\n   */\n  setCurrentProjectDiagrams(diagrams) {\n    this._currentProjectDiagramsSubject.next(diagrams);\n  }\n  /**\n   * Sets the active diagram for the current project.\n   *\n   * This method emits the specified `diagram` to the `_activeDiagramSubject`.\n   *\n   * @param diagram - The `Diagram` object to be set as the active diagram.\n   */\n  setActiveDiagram(diagram) {\n    this._activeDiagramSubject.next(diagram);\n  }\n  /**\n   * Subscribes to changes in the current project diagrams.\n   *\n   * This method provides an observable stream of `Diagram[]` values, allowing consumers\n   * to react to updates in the current project diagrams.\n   *\n   * @returns An `Observable` emitting the latest list of `Diagram` objects.\n   */\n  currentProjectDiagramsChanges() {\n    return this._currentProjectDiagramsSubject.asObservable();\n  }\n  /**\n   * Subscribes to changes in the active diagram.\n   *\n   * This method provides an observable stream of `Diagram` values, allowing consumers\n   * to react to updates in the active diagram.\n   *\n   * @returns An `Observable` emitting the current active `Diagram` or `null` if none is set.\n   */\n  activeDiagramChanges() {\n    return this._activeDiagramSubject.asObservable().pipe(distinctUntilChanged((prev, curr) => {\n      // Compare diagrams by ID to prevent duplicate emissions\n      return prev?.id === curr?.id;\n    }));\n  }\n  /**\n   * Sets the details for the active diagram.\n   *\n   * This method emits the specified `diagram` details to the `_activeDiagramDetailsSubject`.\n   *\n   * @param diagram - An object containing details for the active diagram.\n   */\n  setActiveDiagramDetails(diagram) {\n    this._activeDiagramDetailsSubject.next(diagram);\n  }\n  /**\n   * Subscribes to changes in the active diagram details.\n   *\n   * This method provides an observable stream of values, allowing consumers to\n   * react to updates in the active diagram details.\n   *\n   * @returns An `Observable` emitting the current active diagram details or `null` if none are set.\n   */\n  activeDiagramDetailsChanges() {\n    return this._activeDiagramDetailsSubject.asObservable();\n  }\n  /**\n   * Retrieves the node details from a diagram or palette by its key.\n   * @param diagram The active diagram or palette.\n   * @param key The key used to find the node in the model.\n   * @returns The node data or null if not found.\n   */\n  getObjectDataByKey(diagram, key) {\n    return diagram.model.findNodeDataForKey(key);\n  }\n  /**\n   * Finds a node by its template class ID.\n   * @param idTemplateClass The ID of the template class to search for.\n   * @returns The found node data, or undefined if not found.\n   */\n  findNodeByIdTemplateClass(diagram, idTemplateClass) {\n    return diagram.model.nodeDataArray.find(nodeData => nodeData['idTemplateClass'] === idTemplateClass);\n  }\n  // Methods for managing attribute types\n  getAttributeTypes() {\n    return this._attributeTypes.asObservable();\n  }\n  getAttributeTypeById(idAttributeType) {\n    const attributeTypes = this._attributeTypes.getValue();\n    return attributeTypes.find(attr => attr.id === idAttributeType);\n  }\n  updateAttributeType(idAttributeType, updatedName) {\n    const attributeTypes = this._attributeTypes.getValue();\n    const attributeType = attributeTypes.find(attr => attr.id === idAttributeType);\n    if (attributeType) {\n      attributeType.name = updatedName;\n      this._attributeTypes.next(attributeTypes);\n    }\n  }\n  /**\n   * Adds new options to the attribute types list.\n   * @param option The attribute option or an array of options to add.\n   */\n  addAttributeTypes(option) {\n    const attributeTypes = this._attributeTypes.getValue();\n    if (Array.isArray(option)) {\n      this._attributeTypes.next([...attributeTypes, ...option]);\n    } else {\n      this._attributeTypes.next([...attributeTypes, option]);\n    }\n  }\n  /**\n   * Removes an attribute option by its ID.\n   * @param optionId The ID of the option to remove.\n   */\n  removeAttributeType(optionId) {\n    const attributeTypes = this._attributeTypes.getValue().filter(option => option.id !== optionId);\n    this._attributeTypes.next(attributeTypes);\n  }\n  /**\n   * Clears all enumeration type options.\n   */\n  clearEnumTypeOptions() {\n    const attributeTypes = this._attributeTypes.getValue().filter(o => !o.isEnumeration);\n    this._attributeTypes.next(attributeTypes);\n  }\n  /**\n   * Updates an enumeration type by modifying its name and updating relevant nodes in the diagram.\n   * @param idTempEnumeration The ID of the template enumeration.\n   * @param modifiedName The new name for the enumeration.\n   * @param goJsDiagram The active GoJS diagram to update.\n   */\n  updateEnumerationType(idTempEnumeration, modifiedName, goJsDiagram) {\n    this.updateAttributeType(idTempEnumeration, modifiedName);\n    goJsDiagram.model.nodeDataArray.forEach(nodeData => {\n      if (nodeData && nodeData['items'] && (nodeData['category'] == GojsNodeCategory.Class || nodeData['category'] == GojsNodeCategory.AssociativeClass)) {\n        nodeData['items'].forEach(attr => {\n          if (attr.idTemplateEnumeration == +idTempEnumeration) {\n            attr.dataType = idTempEnumeration;\n            attr.idTemplateEnumeration = +idTempEnumeration;\n          }\n        });\n        goJsDiagram.model.updateTargetBindings(nodeData);\n      }\n    });\n  }\n  /**\n   * Initializes a GoJS diagram's model with optional node and link data.\n   * @param diagram The GoJS diagram to initialize.\n   * @param nodeDataArray An optional array of node data.\n   * @param linkDataArray An optional array of link data.\n   */\n  initializeDiagramModelData(diagram, nodeDataArray, linkDataArray) {\n    diagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);\n    diagram.model.copiesArrays = true;\n    diagram.model.copiesArrayObjects = true;\n    diagram.model.linkFromPortIdProperty = 'fromPort';\n    diagram.model.linkToPortIdProperty = 'toPort';\n    diagram.model.linkLabelKeysProperty = 'labelKeys';\n    diagram.model.linkKeyProperty = 'key';\n    diagram.model.nodeKeyProperty = 'key';\n    // Assign the makeUniqueKeyFunction for node data\n    diagram.model.makeUniqueKeyFunction = (model, nodeData) => {\n      return this.shared.generateUniqueKey();\n    };\n    // Assign the makeUniqueLinkKeyFunction\n    diagram.model.makeUniqueLinkKeyFunction = (model, linkData) => {\n      return this.shared.generateUniqueKey();\n    };\n  }\n  base64ToBlob(base64, mimeType = 'image/jpeg') {\n    const byteCharacters = atob(base64.split(',')[1]);\n    const byteNumbers = Array.from(byteCharacters, char => char.charCodeAt(0));\n    const byteArray = new Uint8Array(byteNumbers);\n    return new Blob([byteArray], {\n      type: mimeType\n    });\n  }\n  getIdFromTag(tag) {\n    if (tag) {\n      const match = tag.match(/.*_(\\d+)$/);\n      if (match) {\n        const id = match[1]; // Extracted ID\n        return parseInt(id);\n      }\n    }\n    return 0;\n  }\n  static #_ = this.ɵfac = function DiagramUtils_Factory(t) {\n    return new (t || DiagramUtils)(i0.ɵɵinject(i1.SharedService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: DiagramUtils,\n    factory: DiagramUtils.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["go", "BehaviorSubject", "AttributeType", "GojsNodeCategory", "DiagramUtils", "constructor", "shared", "_deletedLinks", "_currentProjectDiagramsSubject", "_activeDiagramSubject", "_attributeTypes", "_activeDiagramDetailsSubject", "next", "Object", "keys", "filter", "key", "isNaN", "Number", "Enumeration", "map", "id", "name", "isEnumeration", "addDeletedLink", "link", "push", "removeDeletedLink", "idDeletedLink", "getDeletedLinks", "setCurrentProjectDiagrams", "diagrams", "setActiveDiagram", "diagram", "currentProjectDiagramsChanges", "asObservable", "activeDiagramChanges", "pipe", "distinctUntilChanged", "prev", "curr", "setActiveDiagramDetails", "activeDiagramDetailsChanges", "getObjectDataByKey", "model", "findNodeDataForKey", "findNodeByIdTemplateClass", "idTemplateClass", "nodeDataArray", "find", "nodeData", "getAttributeTypes", "getAttributeTypeById", "idAttributeType", "attributeTypes", "getValue", "attr", "updateAttributeType", "updatedName", "attributeType", "addAttributeTypes", "option", "Array", "isArray", "removeAttributeType", "optionId", "clearEnumTypeOptions", "o", "updateEnumerationType", "idTempEnumeration", "modifiedName", "goJsDiagram", "for<PERSON>ach", "Class", "AssociativeClass", "idTemplateEnumeration", "dataType", "updateTargetBindings", "initializeDiagramModelData", "linkDataArray", "GraphLinksModel", "copiesArrays", "copiesArrayObjects", "linkFromPortIdProperty", "linkToPortIdProperty", "linkLabelKeysProperty", "linkKeyProperty", "nodeKeyProperty", "makeUniqueKeyFunction", "generateUniqueKey", "makeUniqueLinkKeyFunction", "linkData", "base64ToBlob", "base64", "mimeType", "byteCharacters", "atob", "split", "byteNumbers", "from", "char", "charCodeAt", "byteArray", "Uint8Array", "Blob", "type", "getIdFromTag", "tag", "match", "parseInt", "_", "i0", "ɵɵinject", "i1", "SharedService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\shared\\utils\\diagram-utils.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport go from 'gojs';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { AttributeOption, AttributeType } from '../model/attribute';\r\nimport { DeletedLink } from '../model/cardinality';\r\nimport { Diagram } from '../model/diagram';\r\nimport { GojsDiagramAttributeNode, GojsNodeCategory } from '../model/gojs';\r\nimport { SharedService } from '../services/shared.service';\r\n\r\n/**\r\n * Service for managing and manipulating diagrams, links, and attribute types in a GoJS-based diagram editor.\r\n * It provides utility methods for managing diagram-related data, such as nodes, links, and attribute types,\r\n * as well as handling active diagrams and their associated details.\r\n */\r\n@Injectable({\r\n  providedIn: 'root', // This makes the service available globally in the Angular app\r\n})\r\nexport class DiagramUtils {\r\n  // Private variables for tracking diagram data and attributes\r\n  private _deletedLinks: DeletedLink[] = [];\r\n  private _currentProjectDiagramsSubject = new BehaviorSubject<Diagram[]>([]);\r\n  private _activeDiagramSubject = new BehaviorSubject<Diagram | null>(null);\r\n  private _attributeTypes = new BehaviorSubject<AttributeOption[]>([]);\r\n  private _activeDiagramDetailsSubject = new BehaviorSubject<any | null>(null);\r\n  constructor(private shared: SharedService) {\r\n    this._attributeTypes.next(\r\n      Object.keys(AttributeType)\r\n        .filter(\r\n          (key) =>\r\n            isNaN(Number(key)) &&\r\n            AttributeType[key as keyof typeof AttributeType] !==\r\n              AttributeType.Enumeration\r\n        )\r\n        .map((key) => {\r\n          return {\r\n            id: `${AttributeType[key as keyof typeof AttributeType]}_${key}`,\r\n            name: key,\r\n            isEnumeration: false,\r\n          } as AttributeOption;\r\n        })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Adds a deleted link to the internal collection.\r\n   *\r\n   * This method stores a deleted link by appending it to the `_deletedLinks` array.\r\n   *\r\n   * @param link - The `DeletedLink` object representing the deleted link.\r\n   */\r\n  addDeletedLink(link: DeletedLink): void {\r\n    this._deletedLinks.push(link);\r\n  }\r\n\r\n  /**\r\n   * Removes a deleted link from the internal collection by its ID.\r\n   *\r\n   * This method filters out the deleted link matching the specified `idDeletedLink`\r\n   * from the `_deletedLinks` array.\r\n   *\r\n   * @param idDeletedLink - The unique identifier of the deleted link to be removed.\r\n   */\r\n  removeDeletedLink(idDeletedLink: number): void {\r\n    this._deletedLinks = this._deletedLinks.filter(\r\n      (link) => link.id != idDeletedLink\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retrieves the list of deleted links.\r\n   *\r\n   * This method returns all the deleted links stored in the `_deletedLinks` array.\r\n   *\r\n   * @returns An array of `DeletedLink` objects representing the deleted links.\r\n   */\r\n  getDeletedLinks(): DeletedLink[] {\r\n    return this._deletedLinks;\r\n  }\r\n\r\n  /**\r\n   * Sets the list of diagrams associated with the current project.\r\n   *\r\n   * This method emits the provided `diagrams` to the `_currentProjectDiagramsSubject`.\r\n   *\r\n   * @param diagrams - An array of `Diagram` objects representing the current project diagrams.\r\n   */\r\n  setCurrentProjectDiagrams(diagrams: Diagram[]): void {\r\n    this._currentProjectDiagramsSubject.next(diagrams);\r\n  }\r\n\r\n  /**\r\n   * Sets the active diagram for the current project.\r\n   *\r\n   * This method emits the specified `diagram` to the `_activeDiagramSubject`.\r\n   *\r\n   * @param diagram - The `Diagram` object to be set as the active diagram.\r\n   */\r\n  setActiveDiagram(diagram: Diagram | null): void {\r\n    this._activeDiagramSubject.next(diagram);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to changes in the current project diagrams.\r\n   *\r\n   * This method provides an observable stream of `Diagram[]` values, allowing consumers\r\n   * to react to updates in the current project diagrams.\r\n   *\r\n   * @returns An `Observable` emitting the latest list of `Diagram` objects.\r\n   */\r\n  currentProjectDiagramsChanges(): Observable<Diagram[]> {\r\n    return this._currentProjectDiagramsSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Subscribes to changes in the active diagram.\r\n   *\r\n   * This method provides an observable stream of `Diagram` values, allowing consumers\r\n   * to react to updates in the active diagram.\r\n   *\r\n   * @returns An `Observable` emitting the current active `Diagram` or `null` if none is set.\r\n   */\r\n  activeDiagramChanges(): Observable<Diagram | null> {\r\n    return this._activeDiagramSubject.asObservable().pipe(\r\n      distinctUntilChanged((prev, curr) => {\r\n        // Compare diagrams by ID to prevent duplicate emissions\r\n        return prev?.id === curr?.id;\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Sets the details for the active diagram.\r\n   *\r\n   * This method emits the specified `diagram` details to the `_activeDiagramDetailsSubject`.\r\n   *\r\n   * @param diagram - An object containing details for the active diagram.\r\n   */\r\n  setActiveDiagramDetails(diagram: any): void {\r\n    this._activeDiagramDetailsSubject.next(diagram);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to changes in the active diagram details.\r\n   *\r\n   * This method provides an observable stream of values, allowing consumers to\r\n   * react to updates in the active diagram details.\r\n   *\r\n   * @returns An `Observable` emitting the current active diagram details or `null` if none are set.\r\n   */\r\n  activeDiagramDetailsChanges(): Observable<any | null> {\r\n    return this._activeDiagramDetailsSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Retrieves the node details from a diagram or palette by its key.\r\n   * @param diagram The active diagram or palette.\r\n   * @param key The key used to find the node in the model.\r\n   * @returns The node data or null if not found.\r\n   */\r\n  getObjectDataByKey(\r\n    diagram: go.Diagram | go.Palette,\r\n    key: any\r\n  ): go.ObjectData | null {\r\n    return diagram.model.findNodeDataForKey(key);\r\n  }\r\n\r\n  /**\r\n   * Finds a node by its template class ID.\r\n   * @param idTemplateClass The ID of the template class to search for.\r\n   * @returns The found node data, or undefined if not found.\r\n   */\r\n  findNodeByIdTemplateClass(\r\n    diagram: go.Diagram,\r\n    idTemplateClass: number\r\n  ): go.ObjectData | undefined {\r\n    return diagram.model.nodeDataArray.find(\r\n      (nodeData) => nodeData['idTemplateClass'] === idTemplateClass\r\n    );\r\n  }\r\n\r\n  // Methods for managing attribute types\r\n  getAttributeTypes(): Observable<AttributeOption[]> {\r\n    return this._attributeTypes.asObservable();\r\n  }\r\n\r\n  getAttributeTypeById(idAttributeType: string) {\r\n    const attributeTypes = this._attributeTypes.getValue();\r\n    return attributeTypes.find((attr) => attr.id === idAttributeType);\r\n  }\r\n\r\n  updateAttributeType(idAttributeType: string, updatedName: string): void {\r\n    const attributeTypes = this._attributeTypes.getValue();\r\n    const attributeType = attributeTypes.find(\r\n      (attr) => attr.id === idAttributeType\r\n    );\r\n    if (attributeType) {\r\n      attributeType.name = updatedName;\r\n      this._attributeTypes.next(attributeTypes);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds new options to the attribute types list.\r\n   * @param option The attribute option or an array of options to add.\r\n   */\r\n  addAttributeTypes(option: AttributeOption | AttributeOption[]): void {\r\n    const attributeTypes = this._attributeTypes.getValue();\r\n    if (Array.isArray(option)) {\r\n      this._attributeTypes.next([...attributeTypes, ...option]);\r\n    } else {\r\n      this._attributeTypes.next([...attributeTypes, option]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes an attribute option by its ID.\r\n   * @param optionId The ID of the option to remove.\r\n   */\r\n  removeAttributeType(optionId: string): void {\r\n    const attributeTypes = this._attributeTypes\r\n      .getValue()\r\n      .filter((option) => option.id !== optionId);\r\n    this._attributeTypes.next(attributeTypes);\r\n  }\r\n\r\n  /**\r\n   * Clears all enumeration type options.\r\n   */\r\n  clearEnumTypeOptions(): void {\r\n    const attributeTypes = this._attributeTypes\r\n      .getValue()\r\n      .filter((o) => !o.isEnumeration);\r\n    this._attributeTypes.next(attributeTypes);\r\n  }\r\n\r\n  /**\r\n   * Updates an enumeration type by modifying its name and updating relevant nodes in the diagram.\r\n   * @param idTempEnumeration The ID of the template enumeration.\r\n   * @param modifiedName The new name for the enumeration.\r\n   * @param goJsDiagram The active GoJS diagram to update.\r\n   */\r\n  updateEnumerationType(\r\n    idTempEnumeration: string,\r\n    modifiedName: string,\r\n    goJsDiagram: go.Diagram\r\n  ): void {\r\n    this.updateAttributeType(idTempEnumeration, modifiedName);\r\n\r\n    goJsDiagram.model.nodeDataArray.forEach((nodeData) => {\r\n      if (\r\n        nodeData &&\r\n        nodeData['items'] &&\r\n        (nodeData['category'] == GojsNodeCategory.Class ||\r\n          nodeData['category'] == GojsNodeCategory.AssociativeClass)\r\n      ) {\r\n        nodeData['items'].forEach((attr: GojsDiagramAttributeNode) => {\r\n          if (attr.idTemplateEnumeration == +idTempEnumeration) {\r\n            attr.dataType = idTempEnumeration;\r\n            attr.idTemplateEnumeration = +idTempEnumeration;\r\n          }\r\n        });\r\n        goJsDiagram.model.updateTargetBindings(nodeData);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Initializes a GoJS diagram's model with optional node and link data.\r\n   * @param diagram The GoJS diagram to initialize.\r\n   * @param nodeDataArray An optional array of node data.\r\n   * @param linkDataArray An optional array of link data.\r\n   */\r\n  initializeDiagramModelData(\r\n    diagram: go.Diagram,\r\n    nodeDataArray?: Array<go.ObjectData>,\r\n    linkDataArray?: Array<go.ObjectData>\r\n  ): void {\r\n    diagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);\r\n    (diagram.model as go.GraphLinksModel).copiesArrays = true;\r\n    (diagram.model as go.GraphLinksModel).copiesArrayObjects = true;\r\n    (diagram.model as go.GraphLinksModel).linkFromPortIdProperty = 'fromPort';\r\n    (diagram.model as go.GraphLinksModel).linkToPortIdProperty = 'toPort';\r\n    (diagram.model as go.GraphLinksModel).linkLabelKeysProperty = 'labelKeys';\r\n    (diagram.model as go.GraphLinksModel).linkKeyProperty = 'key';\r\n    (diagram.model as go.GraphLinksModel).nodeKeyProperty = 'key';\r\n\r\n    // Assign the makeUniqueKeyFunction for node data\r\n    diagram.model.makeUniqueKeyFunction = (model, nodeData) => {\r\n      return this.shared.generateUniqueKey();\r\n    };\r\n\r\n    // Assign the makeUniqueLinkKeyFunction\r\n    (diagram.model as go.GraphLinksModel).makeUniqueLinkKeyFunction = (\r\n      model,\r\n      linkData\r\n    ) => {\r\n      return this.shared.generateUniqueKey();\r\n    };\r\n  }\r\n\r\n  base64ToBlob(base64: string, mimeType: string = 'image/jpeg'): Blob {\r\n    const byteCharacters = atob(base64.split(',')[1]);\r\n    const byteNumbers = Array.from(byteCharacters, (char) =>\r\n      char.charCodeAt(0)\r\n    );\r\n    const byteArray = new Uint8Array(byteNumbers);\r\n    return new Blob([byteArray], { type: mimeType });\r\n  }\r\n  getIdFromTag(tag: string): number {\r\n    if (tag) {\r\n      const match = tag.match(/.*_(\\d+)$/);\r\n      if (match) {\r\n        const id = match[1]; // Extracted ID\r\n        return parseInt(id);\r\n      }\r\n    }\r\n    return 0;\r\n  }\r\n}\r\n"], "mappings": "AACA,OAAOA,EAAE,MAAM,MAAM;AACrB,SAASC,eAAe,QAAoB,MAAM;AAClD,SAA0BC,aAAa,QAAQ,oBAAoB;AAGnE,SAAmCC,gBAAgB,QAAQ,eAAe;;;AAG1E;;;;;AAQA,OAAM,MAAOC,YAAY;EAOvBC,YAAoBC,MAAqB;IAArB,KAAAA,MAAM,GAANA,MAAM;IAN1B;IACQ,KAAAC,aAAa,GAAkB,EAAE;IACjC,KAAAC,8BAA8B,GAAG,IAAIP,eAAe,CAAY,EAAE,CAAC;IACnE,KAAAQ,qBAAqB,GAAG,IAAIR,eAAe,CAAiB,IAAI,CAAC;IACjE,KAAAS,eAAe,GAAG,IAAIT,eAAe,CAAoB,EAAE,CAAC;IAC5D,KAAAU,4BAA4B,GAAG,IAAIV,eAAe,CAAa,IAAI,CAAC;IAE1E,IAAI,CAACS,eAAe,CAACE,IAAI,CACvBC,MAAM,CAACC,IAAI,CAACZ,aAAa,CAAC,CACvBa,MAAM,CACJC,GAAG,IACFC,KAAK,CAACC,MAAM,CAACF,GAAG,CAAC,CAAC,IAClBd,aAAa,CAACc,GAAiC,CAAC,KAC9Cd,aAAa,CAACiB,WAAW,CAC9B,CACAC,GAAG,CAAEJ,GAAG,IAAI;MACX,OAAO;QACLK,EAAE,EAAE,GAAGnB,aAAa,CAACc,GAAiC,CAAC,IAAIA,GAAG,EAAE;QAChEM,IAAI,EAAEN,GAAG;QACTO,aAAa,EAAE;OACG;IACtB,CAAC,CAAC,CACL;EACH;EAEA;;;;;;;EAOAC,cAAcA,CAACC,IAAiB;IAC9B,IAAI,CAAClB,aAAa,CAACmB,IAAI,CAACD,IAAI,CAAC;EAC/B;EAEA;;;;;;;;EAQAE,iBAAiBA,CAACC,aAAqB;IACrC,IAAI,CAACrB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACQ,MAAM,CAC3CU,IAAI,IAAKA,IAAI,CAACJ,EAAE,IAAIO,aAAa,CACnC;EACH;EAEA;;;;;;;EAOAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACtB,aAAa;EAC3B;EAEA;;;;;;;EAOAuB,yBAAyBA,CAACC,QAAmB;IAC3C,IAAI,CAACvB,8BAA8B,CAACI,IAAI,CAACmB,QAAQ,CAAC;EACpD;EAEA;;;;;;;EAOAC,gBAAgBA,CAACC,OAAuB;IACtC,IAAI,CAACxB,qBAAqB,CAACG,IAAI,CAACqB,OAAO,CAAC;EAC1C;EAEA;;;;;;;;EAQAC,6BAA6BA,CAAA;IAC3B,OAAO,IAAI,CAAC1B,8BAA8B,CAAC2B,YAAY,EAAE;EAC3D;EAEA;;;;;;;;EAQAC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3B,qBAAqB,CAAC0B,YAAY,EAAE,CAACE,IAAI,CACnDC,oBAAoB,CAAC,CAACC,IAAI,EAAEC,IAAI,KAAI;MAClC;MACA,OAAOD,IAAI,EAAElB,EAAE,KAAKmB,IAAI,EAAEnB,EAAE;IAC9B,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAoB,uBAAuBA,CAACR,OAAY;IAClC,IAAI,CAACtB,4BAA4B,CAACC,IAAI,CAACqB,OAAO,CAAC;EACjD;EAEA;;;;;;;;EAQAS,2BAA2BA,CAAA;IACzB,OAAO,IAAI,CAAC/B,4BAA4B,CAACwB,YAAY,EAAE;EACzD;EAEA;;;;;;EAMAQ,kBAAkBA,CAChBV,OAAgC,EAChCjB,GAAQ;IAER,OAAOiB,OAAO,CAACW,KAAK,CAACC,kBAAkB,CAAC7B,GAAG,CAAC;EAC9C;EAEA;;;;;EAKA8B,yBAAyBA,CACvBb,OAAmB,EACnBc,eAAuB;IAEvB,OAAOd,OAAO,CAACW,KAAK,CAACI,aAAa,CAACC,IAAI,CACpCC,QAAQ,IAAKA,QAAQ,CAAC,iBAAiB,CAAC,KAAKH,eAAe,CAC9D;EACH;EAEA;EACAI,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACzC,eAAe,CAACyB,YAAY,EAAE;EAC5C;EAEAiB,oBAAoBA,CAACC,eAAuB;IAC1C,MAAMC,cAAc,GAAG,IAAI,CAAC5C,eAAe,CAAC6C,QAAQ,EAAE;IACtD,OAAOD,cAAc,CAACL,IAAI,CAAEO,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAKgC,eAAe,CAAC;EACnE;EAEAI,mBAAmBA,CAACJ,eAAuB,EAAEK,WAAmB;IAC9D,MAAMJ,cAAc,GAAG,IAAI,CAAC5C,eAAe,CAAC6C,QAAQ,EAAE;IACtD,MAAMI,aAAa,GAAGL,cAAc,CAACL,IAAI,CACtCO,IAAI,IAAKA,IAAI,CAACnC,EAAE,KAAKgC,eAAe,CACtC;IACD,IAAIM,aAAa,EAAE;MACjBA,aAAa,CAACrC,IAAI,GAAGoC,WAAW;MAChC,IAAI,CAAChD,eAAe,CAACE,IAAI,CAAC0C,cAAc,CAAC;;EAE7C;EAEA;;;;EAIAM,iBAAiBA,CAACC,MAA2C;IAC3D,MAAMP,cAAc,GAAG,IAAI,CAAC5C,eAAe,CAAC6C,QAAQ,EAAE;IACtD,IAAIO,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzB,IAAI,CAACnD,eAAe,CAACE,IAAI,CAAC,CAAC,GAAG0C,cAAc,EAAE,GAAGO,MAAM,CAAC,CAAC;KAC1D,MAAM;MACL,IAAI,CAACnD,eAAe,CAACE,IAAI,CAAC,CAAC,GAAG0C,cAAc,EAAEO,MAAM,CAAC,CAAC;;EAE1D;EAEA;;;;EAIAG,mBAAmBA,CAACC,QAAgB;IAClC,MAAMX,cAAc,GAAG,IAAI,CAAC5C,eAAe,CACxC6C,QAAQ,EAAE,CACVxC,MAAM,CAAE8C,MAAM,IAAKA,MAAM,CAACxC,EAAE,KAAK4C,QAAQ,CAAC;IAC7C,IAAI,CAACvD,eAAe,CAACE,IAAI,CAAC0C,cAAc,CAAC;EAC3C;EAEA;;;EAGAY,oBAAoBA,CAAA;IAClB,MAAMZ,cAAc,GAAG,IAAI,CAAC5C,eAAe,CACxC6C,QAAQ,EAAE,CACVxC,MAAM,CAAEoD,CAAC,IAAK,CAACA,CAAC,CAAC5C,aAAa,CAAC;IAClC,IAAI,CAACb,eAAe,CAACE,IAAI,CAAC0C,cAAc,CAAC;EAC3C;EAEA;;;;;;EAMAc,qBAAqBA,CACnBC,iBAAyB,EACzBC,YAAoB,EACpBC,WAAuB;IAEvB,IAAI,CAACd,mBAAmB,CAACY,iBAAiB,EAAEC,YAAY,CAAC;IAEzDC,WAAW,CAAC3B,KAAK,CAACI,aAAa,CAACwB,OAAO,CAAEtB,QAAQ,IAAI;MACnD,IACEA,QAAQ,IACRA,QAAQ,CAAC,OAAO,CAAC,KAChBA,QAAQ,CAAC,UAAU,CAAC,IAAI/C,gBAAgB,CAACsE,KAAK,IAC7CvB,QAAQ,CAAC,UAAU,CAAC,IAAI/C,gBAAgB,CAACuE,gBAAgB,CAAC,EAC5D;QACAxB,QAAQ,CAAC,OAAO,CAAC,CAACsB,OAAO,CAAEhB,IAA8B,IAAI;UAC3D,IAAIA,IAAI,CAACmB,qBAAqB,IAAI,CAACN,iBAAiB,EAAE;YACpDb,IAAI,CAACoB,QAAQ,GAAGP,iBAAiB;YACjCb,IAAI,CAACmB,qBAAqB,GAAG,CAACN,iBAAiB;;QAEnD,CAAC,CAAC;QACFE,WAAW,CAAC3B,KAAK,CAACiC,oBAAoB,CAAC3B,QAAQ,CAAC;;IAEpD,CAAC,CAAC;EACJ;EAEA;;;;;;EAMA4B,0BAA0BA,CACxB7C,OAAmB,EACnBe,aAAoC,EACpC+B,aAAoC;IAEpC9C,OAAO,CAACW,KAAK,GAAG,IAAI5C,EAAE,CAACgF,eAAe,CAAChC,aAAa,EAAE+B,aAAa,CAAC;IACnE9C,OAAO,CAACW,KAA4B,CAACqC,YAAY,GAAG,IAAI;IACxDhD,OAAO,CAACW,KAA4B,CAACsC,kBAAkB,GAAG,IAAI;IAC9DjD,OAAO,CAACW,KAA4B,CAACuC,sBAAsB,GAAG,UAAU;IACxElD,OAAO,CAACW,KAA4B,CAACwC,oBAAoB,GAAG,QAAQ;IACpEnD,OAAO,CAACW,KAA4B,CAACyC,qBAAqB,GAAG,WAAW;IACxEpD,OAAO,CAACW,KAA4B,CAAC0C,eAAe,GAAG,KAAK;IAC5DrD,OAAO,CAACW,KAA4B,CAAC2C,eAAe,GAAG,KAAK;IAE7D;IACAtD,OAAO,CAACW,KAAK,CAAC4C,qBAAqB,GAAG,CAAC5C,KAAK,EAAEM,QAAQ,KAAI;MACxD,OAAO,IAAI,CAAC5C,MAAM,CAACmF,iBAAiB,EAAE;IACxC,CAAC;IAED;IACCxD,OAAO,CAACW,KAA4B,CAAC8C,yBAAyB,GAAG,CAChE9C,KAAK,EACL+C,QAAQ,KACN;MACF,OAAO,IAAI,CAACrF,MAAM,CAACmF,iBAAiB,EAAE;IACxC,CAAC;EACH;EAEAG,YAAYA,CAACC,MAAc,EAAEC,QAAA,GAAmB,YAAY;IAC1D,MAAMC,cAAc,GAAGC,IAAI,CAACH,MAAM,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,MAAMC,WAAW,GAAGpC,KAAK,CAACqC,IAAI,CAACJ,cAAc,EAAGK,IAAI,IAClDA,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CACnB;IACD,MAAMC,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;IAC7C,OAAO,IAAIM,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;MAAEG,IAAI,EAAEX;IAAQ,CAAE,CAAC;EAClD;EACAY,YAAYA,CAACC,GAAW;IACtB,IAAIA,GAAG,EAAE;MACP,MAAMC,KAAK,GAAGD,GAAG,CAACC,KAAK,CAAC,WAAW,CAAC;MACpC,IAAIA,KAAK,EAAE;QACT,MAAMvF,EAAE,GAAGuF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,OAAOC,QAAQ,CAACxF,EAAE,CAAC;;;IAGvB,OAAO,CAAC;EACV;EAAC,QAAAyF,CAAA,G;qBA5SU1G,YAAY,EAAA2G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZ/G,YAAY;IAAAgH,OAAA,EAAZhH,YAAY,CAAAiH,IAAA;IAAAC,UAAA,EAFX;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}