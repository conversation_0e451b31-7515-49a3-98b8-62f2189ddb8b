import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subject, debounceTime, takeUntil } from 'rxjs';
import { colorGroups, colorList } from 'src/app/shared/configs/colorConfig';
import {
  Attribute,
  AttributeOption,
  AttributeType,
} from 'src/app/shared/model/attribute';
import { PropertyFormData } from 'src/app/shared/model/common';
import { Folder } from 'src/app/shared/model/folder';
import {
  GojsDiagramAttributeNode,
  GojsDiagramClassNode,
  GojsDiagramEnumerationNode,
  GojsDiagramLiteralNode,
  GojsFolderNode,
  GojsLinkNode,
  GojsNodeCategory,
} from 'src/app/shared/model/gojs';
import { Literal } from 'src/app/shared/model/literal';
import { DiagramUtils } from 'src/app/shared/utils/diagram-utils';
import { AttributeService } from '../../services/attribute/attribute.service';
import { CardinalityService } from '../../services/cardinality/cardinality.service';
import { ClassService } from '../../services/class/class.service';
import { EnumerationService } from '../../services/enumeration/enumeration.service';
import { FolderService } from '../../services/folder/folder.service';
import { GojsCommonService } from '../../services/gojs/gojsCommon/gojs-common.service';
import { LiteralService } from '../../services/literal/literal.service';
import { PropertyService } from '../../services/property/property.service';

@Component({
  selector: 'app-properties',
  templateUrl: './properties.component.html',
  styleUrls: ['./properties.component.scss'],
})
export class PropertiesComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  color!: string;
  isGroupNode: boolean = false;
  isAttributeNode: boolean = false;
  showPropertyForm: boolean = false;
  isLinkNode: boolean = false;
  showColorPicker: boolean = false;
  cardinalities: string[] = ['0..1', '1', '*', '1..*'];
  private inputSubject = new Subject<PropertyFormData>();
  private destroy$ = new Subject<void>();
  private readonly debounceTimeMs = 400;
  public presetValues: string[] = [];
  public colorGroupsConfig: any = {};
  @Input('nodeData') nodeProperty!:
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsLinkNode
    | GojsFolderNode;
  @Input('editAccess') haveEditAccess: boolean = false;
  @Input('idDiagram') currentDiagramId: number = 0;
  @Output('onChangePropertyData') updateNodePropertyEmitter = new EventEmitter<
    | GojsDiagramClassNode
    | GojsDiagramEnumerationNode
    | GojsDiagramAttributeNode
    | GojsDiagramLiteralNode
    | GojsLinkNode
    | GojsFolderNode
  >();
  @Output('isColorPickerSelected') colorPickerSelectEmitter =
    new EventEmitter<boolean>();
  @Input('dataTypes') attributeTypes: AttributeOption[] = [];
  constructor(
    private fb: FormBuilder,
    private attributeService: AttributeService,
    private enumerationService: EnumerationService,
    private folderService: FolderService,
    private classService: ClassService,
    private literalService: LiteralService,
    private propertyService: PropertyService,
    private commonGojsService: GojsCommonService,
    private linkService: CardinalityService,
    private diagramUtils: DiagramUtils,
    private elementRef: ElementRef,
    private translateService: TranslateService
  ) {
    this.presetValues = this.getColorValues();
    this.colorGroupsConfig = this.getColorGroups();
  }
  ngOnInit(): void {
    this.inputSubject
      .pipe(debounceTime(this.debounceTimeMs), takeUntil(this.destroy$))
      .subscribe((inputValue) => {
        if (inputValue.name.trim() != '') {
          this.nodeProperty = { ...this.nodeProperty, ...inputValue } as
            | GojsDiagramClassNode
            | GojsDiagramEnumerationNode
            | GojsDiagramAttributeNode
            | GojsDiagramLiteralNode
            | GojsLinkNode
            | GojsFolderNode;
          if (
            inputValue.category == GojsNodeCategory.Class ||
            inputValue.category == GojsNodeCategory.AssociativeClass
          ) {
            if (
              this.commonGojsService.isGojsDiagramClassNode(this.nodeProperty)
            ) {
              this.updateTemplateClass(this.nodeProperty);
            }
          } else if (inputValue.category == GojsNodeCategory.Enumeration) {
            if (
              this.commonGojsService.isGojsDiagramEnumerationNode(
                this.nodeProperty
              )
            ) {
              this.updateTemplateEnumeration(this.nodeProperty);
            }
          } else if (
            inputValue.category == GojsNodeCategory.Attribute ||
            inputValue.category == GojsNodeCategory.Operation
          ) {
            if (
              this.commonGojsService.isGojsDiagramAttributeNode(
                this.nodeProperty
              )
            ) {
              this.updateAttribute(this.nodeProperty);
            }
          } else if (
            inputValue.category == GojsNodeCategory.EnumerationLiteral
          ) {
            if (
              this.commonGojsService.isGojsDiagramLiteralNode(this.nodeProperty)
            ) {
              this.updateLiteral(this.nodeProperty);
            }
          } else if (this.nodeProperty.category == GojsNodeCategory.Folder) {
            if (
              this.commonGojsService.isGojsPaletteFolderNode(this.nodeProperty)
            )
              this.updateFolder({
                id: this.nodeProperty.idFolder,
                name: inputValue.name,
                icon: this.nodeProperty.icon,
              } as Folder);
          } else if (
            this.nodeProperty.category == GojsNodeCategory.Association
          ) {
            this.updateLinkNode({
              ...this.nodeProperty,
              name: inputValue.name,
              color: (this.nodeProperty as GojsLinkNode).color,
              cardinalityFrom: (this.nodeProperty as GojsLinkNode)
                .cardinalityFrom,
              cardinalityTo: (this.nodeProperty as GojsLinkNode).cardinalityTo,
              segmentOffset: (this.nodeProperty as GojsLinkNode).segmentOffset,
            } as GojsLinkNode);
          }
        }
      });
    this.getPropertyData();
  }
  updateLiteral(nodeProperty: GojsDiagramLiteralNode) {
    this.literalService
      .updateEnumerationLiteral({
        id: nodeProperty.id,
        name: nodeProperty.name,
        key: nodeProperty.key,
      } as Literal)
      .subscribe((res) => {
        this.updateNodePropertyEmitter.emit({
          ...nodeProperty,
          ...res,
        });
      });
  }
  updateTemplateEnumeration(inputValue: GojsDiagramEnumerationNode) {
    this.enumerationService
      .updateTempEnumeration(
        {
          id: inputValue.idTemplateEnumeration,
          name: inputValue.name,
          key: this.nodeProperty.key,
          icon: inputValue.icon,
          color: inputValue.color,
          description: inputValue.description,
          volumetry: inputValue.volumetry,
          tag: inputValue.tag,
        },
        this.currentDiagramId
      )
      .subscribe((res) => {
        this.updateNodePropertyEmitter.emit({
          ...this.nodeProperty,
          ...res,
        });
        this.diagramUtils.updateAttributeType(res.id?.toString()!, res.name);
      });
  }
  updateTemplateClass(templateClass: GojsDiagramClassNode) {
    this.classService
      .updateTemplateClass(
        {
          id: templateClass.idTemplateClass,
          name: templateClass.name,
          key: templateClass?.key?.toString().split('_')[0],
          icon: templateClass.icon,
          color: templateClass.color,
          tag: templateClass?.tag,
          volumetry: templateClass?.volumetry,
          description: templateClass?.description,
          isAssociative:
            templateClass.category == GojsNodeCategory.AssociativeClass
              ? true
              : false,
        },
        this.currentDiagramId
      )
      .subscribe((_) => {
        this.updateNodePropertyEmitter.emit({
          ...templateClass,
          id: templateClass.idTemplateClass,
        });
      });
  }
  updateAttribute(attribute: GojsDiagramAttributeNode) {
    const attributeOption = this.attributeTypes.find(
      (o) => o.id === attribute.dataType
    );
    this.attributeService
      .updateAttribute({
        id: attribute.id,
        name: attribute.name,
        description: attribute?.description,
        category: attribute.memberType,
        attributeType: !attributeOption?.isEnumeration
          ? this.attributeService.getDefaultAttributeId(
              attribute.dataType.toString()
            )
          : AttributeType.Undefined,
        idTemplateEnumeration: attributeOption?.isEnumeration
          ? attributeOption?.id
          : 0,
        key: attribute.key,
      } as Attribute)
      .subscribe((attr) => {
        this.updateNodePropertyEmitter.emit({
          ...attribute,
          dataType:
            attr.idTemplateEnumeration == 0
              ? `${attr.attributeType}_${AttributeType[attr.attributeType]}`
              : attr.idTemplateEnumeration?.toString(),
        });
      });
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  private updateFolder(folderObject: Folder) {
    this.folderService.updateFolder(folderObject).subscribe((updatedFolder) => {
      this.updateNodePropertyEmitter.emit({
        ...this.nodeProperty,
        ...updatedFolder,
      });
    });
  }
  updateLinkNode(linkNode: GojsLinkNode): void {
    this.linkService
      .updateLink({
        idLinkType: linkNode.idLinkType,
        color: linkNode.color,
        id: +linkNode?.key!,
        name: linkNode.name,
        fromComment: linkNode.fromComment,
        toComment: linkNode.toComment,
        idSourceTempClass: linkNode.idSourceTempClass,
        idDestinationTempClass: linkNode.idDestinationTempClass,
        segmentOffset: linkNode.segmentOffset,
        linkPort: {
          idDiagram: this.currentDiagramId,
          destinationPort: linkNode.toPort!,
          sourcePort: linkNode.fromPort!,
          idLink: linkNode.id!,
          segmentOffset: linkNode.segmentOffset,
        },
      })
      .subscribe((updatedLink) => {
        this.updateNodePropertyEmitter.emit({
          ...this.nodeProperty,
          ...updatedLink,
        });
      });
  }
  getPropertyData(): void {
    this.propertyService
      .propertyDataChanges()
      .pipe(takeUntil(this.destroy$))
      .subscribe((data) => {
        if (data) {
          if (Object.keys(data).length > 0) {
            this.showPropertyForm = true;
            this.nodeProperty = data;
            this.isLinkNode = Boolean(
              this.nodeProperty.category == GojsNodeCategory.Association
            );
            this.isAttributeNode = Boolean(
              this.nodeProperty.category === GojsNodeCategory.Attribute ||
                this.nodeProperty.category === GojsNodeCategory.Operation
            );
            if (
              this.nodeProperty.category === GojsNodeCategory.Enumeration ||
              this.nodeProperty.category === GojsNodeCategory.Class ||
              this.nodeProperty.category === GojsNodeCategory.AssociativeClass
            )
              this.isGroupNode = true;
            else this.isGroupNode = false;
            this.initForm(data);
          } else this.showPropertyForm = false;
        } else this.showPropertyForm = false;
      });
  }
  initForm(data: any): void {
    const {
      category,
      name,
      description,
      volumetry,
      tag,
      color,
      dataType,
      fromComment,
      toComment,
    } = data;
    if (
      category === GojsNodeCategory.Operation ||
      category === GojsNodeCategory.Attribute
    ) {
      const attributeOption = this.attributeTypes.find(
        (option) => option.id === dataType.toString()
      );
      data.dataType = attributeOption
        ? attributeOption.id
        : `0_${AttributeType[AttributeType.Undefined]}`;
    }

    const createControl = (value: string) =>
      new FormControl({ value, disabled: !this.haveEditAccess });

    this.form = this.fb.group<PropertyForm>({
      name: createControl(name),
      description: createControl(description),
      volumetry: createControl(volumetry),
      tag: createControl(tag),
      color: createControl(color),
      dataType: createControl(dataType?.toString()),
      category: createControl(category),
      fromComment: createControl(fromComment),
      toComment: createControl(toComment),
    });

    this.color = color;
    this.form.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((res: PropertyFormData) => {
        this.inputSubject.next(res);
      });
  }

  changeColor(color: string) {
    this.color = color;
    this.form.controls['color'].setValue(color, { emitEvent: true });
  }
  onSelectionChange(event: any) {
    this.form.controls['dataType'].setValue(event.value);
  }

  toggleColorPicker() {
    this.showColorPicker = !this.showColorPicker;
    this.colorPickerSelectEmitter.emit(this.showColorPicker);

    // Ensure the color is properly initialized
    if (this.showColorPicker && !this.color) {
      // Set a default color if none is selected
      this.color = 'rgba(229, 57, 53, 0.8)';
      this.form.controls['color'].setValue(this.color, { emitEvent: false });
    }
  }

  onColorSelected(color: string) {
    this.color = color;
    this.changeColor(color);
    this.showColorPicker = false;

    // Update the form control value without displaying it in the input
    this.form.controls['color'].setValue(color, { emitEvent: false });
  }

  onCustomColorSelected(color: string) {
    this.color = color;
    this.changeColor(color);

    // Update the form control value without displaying it in the input
    this.form.controls['color'].setValue(color, { emitEvent: false });

    // Note: We don't close the color picker here
  }

  @HostListener('document:click', ['$event'])
  handleDocumentClick(event: MouseEvent) {
    // Close color picker when clicking outside the component
    if (this.showColorPicker) {
      const clickedInside = this.elementRef.nativeElement.contains(
        event.target
      );
      if (!clickedInside) {
        this.showColorPicker = false;
      }
    }
  }

  getColorValues() {
    return colorList.map((c) => c.value);
  }

  getColorGroups() {
    const groups: { [key: string]: string[] } = {};
    colorGroups.forEach((group) => {
      groups[group.name] = group.colors.map((c) => c.value);
    });
    return groups;
  }
}

export interface PropertyForm {
  name: FormControl<string | null>;
  description: FormControl<string | null>;
  volumetry: FormControl<string | null>;
  tag: FormControl<string | null>;
  color: FormControl<string | null>;
  dataType: FormControl<string | null>;
  category: FormControl<string | null>;
  fromComment: FormControl<string | null>;
  toComment: FormControl<string | null>;
}
