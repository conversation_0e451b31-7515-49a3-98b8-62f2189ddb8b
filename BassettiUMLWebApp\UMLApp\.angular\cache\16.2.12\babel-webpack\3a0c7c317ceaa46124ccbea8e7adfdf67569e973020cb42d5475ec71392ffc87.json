{"ast": null, "code": "import { GoJsNodeIcon } from 'src/app/shared/model/common';\nimport { GojsNodeCategory } from 'src/app/shared/model/gojs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { ContextMenuAction, TreeNodeTag } from 'src/app/shared/model/treeNode';\nimport { DiagramDialogComponent } from '../../components/diagram-dialog/diagram-dialog.component';\nimport { DialogConfirmationComponent } from '../../components/dialog-confirmation/dialog-confirmation.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../snackbar/snack-bar.service\";\nimport * as i3 from \"../project/project.service\";\nimport * as i4 from \"../diagram/diagram.service\";\nimport * as i5 from \"src/app/shared/utils/diagram-utils\";\nimport * as i6 from \"../treeNode/tree-node.service\";\nimport * as i7 from \"../gojs/gojsClass/gojs-class.service\";\nimport * as i8 from \"../gojs/gojsEnumeration/gojs-enumeration.service\";\nimport * as i9 from \"../gojs/gojsLiteral/gojs-literal.service\";\nimport * as i10 from \"../gojs/gojsAttribute/gojs-attribute.service\";\nimport * as i11 from \"../gojs/gojsFolder/gojs-folder.service\";\nimport * as i12 from \"../property/property.service\";\nimport * as i13 from \"../access/access.service\";\nimport * as i14 from \"@angular/router\";\nexport class ContextMenuActionService {\n  constructor(dialog, snackBarService, projectService, diagramService, diagramUtils, treeNodeService, goJsClassService, gojsEnumerationService, gojsLiteralService, gojsAttributeService, gojsFolderService, propertyService, _accessService, router) {\n    this.dialog = dialog;\n    this.snackBarService = snackBarService;\n    this.projectService = projectService;\n    this.diagramService = diagramService;\n    this.diagramUtils = diagramUtils;\n    this.treeNodeService = treeNodeService;\n    this.goJsClassService = goJsClassService;\n    this.gojsEnumerationService = gojsEnumerationService;\n    this.gojsLiteralService = gojsLiteralService;\n    this.gojsAttributeService = gojsAttributeService;\n    this.gojsFolderService = gojsFolderService;\n    this.propertyService = propertyService;\n    this._accessService = _accessService;\n    this.router = router;\n    this.diagrams = [];\n    this.projectId = -1;\n    this.baseFileName = 'class-diagram';\n    this._hasEditAccessOnly = false;\n    this.projectService.currentProjectChanges().subscribe(project => {\n      if (project && project.id) {\n        this.projectId = project.id;\n      }\n    });\n    this.diagramUtils.activeDiagramChanges().subscribe(diagram => {\n      if (diagram) this.currentDiagram = diagram;\n    });\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe(diagrams => {\n      this.diagrams = diagrams;\n    });\n    this._accessService.accessTypeChanges().subscribe(access => {\n      this._hasEditAccessOnly = access != AccessType.Viewer;\n    });\n  }\n  executeAction(actionName, node, selection) {\n    switch (actionName) {\n      case ContextMenuAction.AddDiagram:\n        this.openDiagramDialog({\n          name: node.data?.name,\n          targetedNode: node\n        }, true);\n        break;\n      case ContextMenuAction.EditDiagram:\n        this.openDiagramDialog({\n          name: node.data?.name,\n          id: node.data?.id\n        }, false);\n        break;\n      case ContextMenuAction.DeleteDiagram:\n        this.onDeleteDiagram(node);\n        break;\n      case ContextMenuAction.AddLiteral:\n        if (node.data && 'idTemplateEnumeration' in node.data) {\n          this.openNodeCreationDialog({\n            headerTitle: 'literal.header',\n            placeholder: 'literal.placeholder',\n            text: 'Literal',\n            btnText: '',\n            isCreation: true\n          }, node, actionName);\n        }\n        break;\n      case ContextMenuAction.DeleteFolder:\n      case ContextMenuAction.DeleteClass:\n      case ContextMenuAction.DeleteEnumeration:\n      case ContextMenuAction.DeleteAttribute:\n      case ContextMenuAction.DeleteMethod:\n      case ContextMenuAction.DeleteLiteral:\n        // Check if the node is already in the selection to avoid duplication\n        const nodesToDelete = selection.isSelected(node) ? [...selection.selected] : [...selection.selected, node];\n        this.deleteSelectedNodes(nodesToDelete);\n        break;\n      case ContextMenuAction.AddAttribute:\n        if (node.data && 'idTemplateClass' in node.data) {\n          this.openNodeCreationDialog({\n            headerTitle: 'attribute.header',\n            placeholder: 'attribute.placeholder',\n            text: 'Attribute',\n            btnText: '',\n            isCreation: true,\n            isAttribute: true\n          }, node, actionName);\n        }\n        break;\n      case ContextMenuAction.AddOperation:\n        if (node.data && 'idTemplateClass' in node.data) {\n          this.openNodeCreationDialog({\n            headerTitle: 'method.header',\n            placeholder: 'method.placeholder',\n            text: 'Method',\n            btnText: '',\n            isCreation: true,\n            isAttribute: true\n          }, node, actionName);\n        }\n        break;\n      case ContextMenuAction.AddFolder:\n        this.openNodeCreationDialog({\n          headerTitle: 'folder.header',\n          placeholder: 'folder.placeholder',\n          text: 'New Folder',\n          btnText: '',\n          isCreation: true\n        }, node, actionName);\n        break;\n      case ContextMenuAction.AddClass:\n      case ContextMenuAction.AddAssociative:\n        this.openNodeCreationDialog({\n          headerTitle: 'class.header',\n          placeholder: 'class.placeholder',\n          text: 'Class',\n          btnText: '',\n          isCreation: true\n        }, node, actionName);\n        break;\n      case ContextMenuAction.AddEnumeration:\n        this.openNodeCreationDialog({\n          headerTitle: 'enumeration.header',\n          placeholder: 'enumeration.placeholder',\n          text: 'Enumeration',\n          btnText: '',\n          isCreation: true\n        }, node, actionName);\n        break;\n      default:\n        break;\n    }\n  }\n  openNodeCreationDialog(data, node, actionName) {\n    const dialogRef = this.dialog.open(DiagramDialogComponent, {\n      width: '300px',\n      data: {\n        text: data.text || '',\n        headerTitle: data.headerTitle,\n        placeholder: data.placeholder,\n        btnText: 'diagram.create',\n        isCreation: true,\n        isAttribute: data.isAttribute\n      }\n    });\n    dialogRef.afterClosed().subscribe(createdData => {\n      if (createdData) {\n        switch (actionName) {\n          case ContextMenuAction.AddFolder:\n            this.gojsFolderService.onCreateNewFolder(createdData.name, this.projectId, this._hasEditAccessOnly, node.tag);\n            break;\n          case ContextMenuAction.AddClass:\n          case ContextMenuAction.AddAssociative:\n            this.goJsClassService.handleClassCreationFromLibrary(createdData.name, node, actionName === ContextMenuAction.AddAssociative);\n            break;\n          case ContextMenuAction.AddEnumeration:\n            this.gojsEnumerationService.handleEnumerationCreationFromLibrary(createdData.name, node);\n            break;\n          case ContextMenuAction.AddAttribute:\n            if (node.data && 'idTemplateClass' in node.data) {\n              this.gojsAttributeService.addAttributeOrMethodFromLibrary(createdData.name, GojsNodeCategory.Attribute, node.data.idTemplateClass, createdData.dataType);\n            }\n            break;\n          case ContextMenuAction.AddOperation:\n            if (node.data && 'idTemplateClass' in node.data) {\n              this.gojsAttributeService.addAttributeOrMethodFromLibrary(createdData.name, GojsNodeCategory.Operation, node.data.idTemplateClass, createdData.dataType);\n            }\n            break;\n          case ContextMenuAction.AddLiteral:\n            this.gojsLiteralService.addLiteralInLibrary(createdData.name, node.data);\n            break;\n        }\n      }\n    });\n  }\n  openDiagramDialog(data, isCreation) {\n    const dialogRef = this.dialog.open(DiagramDialogComponent, {\n      width: '300px',\n      data: {\n        text: isCreation ? this.generateFileName() : data.name || '',\n        headerTitle: isCreation ? 'diagram.createDiagram' : 'diagram.editDiagram',\n        placeholder: 'diagram.diagramPlaceholderText',\n        btnText: isCreation ? 'diagram.create' : 'diagram.save',\n        isCreation: isCreation\n      }\n    });\n    dialogRef.afterClosed().subscribe(diagramResult => {\n      if (!diagramResult) return;\n      if (diagramResult.isCreation) {\n        // const parentNode = this.treeNodeService.findCurrentDiagramParentNode(\n        //   `atTag${GojsNodeCategory.Diagram}_${this.currentDiagram.id}`\n        // );\n        this.diagramService.createDiagram({\n          idProject: this.projectId,\n          name: diagramResult.name,\n          idFolder: data.targetedNode && data.targetedNode.category == GojsNodeCategory.Folder ? data.targetedNode.data?.idFolder : 0\n        }).subscribe(createdDiagram => {\n          this.diagrams.push(createdDiagram);\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n          this.diagramUtils.setActiveDiagram(createdDiagram);\n          this.treeNodeService.addGroupNodeInTree({\n            name: createdDiagram.name,\n            children: [],\n            category: GojsNodeCategory.Diagram,\n            icon: GoJsNodeIcon.Diagram,\n            tag: `atTag${GojsNodeCategory.Diagram}_${createdDiagram.id}`,\n            parentTag: data.targetedNode && data.targetedNode.category == GojsNodeCategory.Project ? `${TreeNodeTag.DiagramWrapper}_${TreeNodeTag.Project}` : `${data.targetedNode?.tag}`,\n            data: createdDiagram,\n            isDraggable: true,\n            supportingNodes: [GojsNodeCategory.Diagram]\n          });\n          this.propertyService.setPropertyData(null);\n          if (createdDiagram.id) {\n            this.currentDiagram = createdDiagram;\n            // Update the URL to reflect the newly created diagram\n            // This is especially important when this is the first diagram in a project\n            // and the URL currently has the placeholder diagram ID (0)\n            this.router.navigate([`/editor/${this.projectId}/diagram/${createdDiagram.id}`], {\n              replaceUrl: true // Replace the current URL instead of adding a new history entry\n            });\n          }\n        });\n      } else {\n        this.updateDiagram(data.id, diagramResult.name);\n      }\n    });\n  }\n  updateDiagram(diagramId, diagramName) {\n    if (this._hasEditAccessOnly) {\n      this.diagramService.updateDiagram({\n        id: diagramId,\n        name: diagramName\n      }).subscribe(updatedDiagram => {\n        const index = this.diagrams.findIndex(value => value.id == this.currentDiagram.id);\n        if (index !== -1) {\n          this.treeNodeService.editGroupTreeNode({\n            name: updatedDiagram.name,\n            children: [],\n            category: GojsNodeCategory.Diagram,\n            icon: GoJsNodeIcon.Diagram,\n            tag: `atTag${GojsNodeCategory.Diagram}_${updatedDiagram.id}`,\n            data: {\n              ...this.diagrams[index],\n              name: updatedDiagram.name\n            },\n            isDraggable: true,\n            supportingNodes: [GojsNodeCategory.Diagram]\n          });\n          this.diagrams[index].name = updatedDiagram.name;\n        }\n        this.diagramUtils.setActiveDiagram(this.diagrams[index]);\n        this.snackBarService.openSnackbar('snackBar.diagramUpdateMsg');\n      });\n    }\n  }\n  generateFileName() {\n    const existingNames = new Set(this.diagrams.map(diagram => diagram.name));\n    let newFileName = this.baseFileName;\n    let count = 1;\n    while (existingNames.has(newFileName)) {\n      newFileName = `${this.baseFileName}${count}`;\n      count++;\n    }\n    return newFileName;\n  }\n  onDeleteDiagram(node) {\n    const dialogRef = this.dialog.open(DialogConfirmationComponent, {\n      width: '320px',\n      data: {\n        title: 'dialog.title',\n        reject: 'dialog.no',\n        confirm: 'dialog.yes'\n      }\n    });\n    dialogRef.afterClosed().subscribe(isConfirm => {\n      if (isConfirm) {\n        if (node && node.data?.id) {\n          this.diagramService.deleteDiagram(node.data?.id);\n          this.treeNodeService.deleteGroupTreeNode(node);\n          this.diagrams = this.diagrams.filter(diagram => diagram.id !== node.data?.id);\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\n          if (this.diagrams.length > 0) {\n            this.currentDiagram = this.diagrams[0];\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\n            // Update the URL to reflect the new active diagram\n            this.router.navigate([`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`], {\n              replaceUrl: true\n            });\n          } else {\n            this.diagramUtils.setActiveDiagram(null);\n            // If there are no diagrams left, navigate to a placeholder URL\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\n              replaceUrl: true\n            });\n          }\n        }\n      }\n    });\n  }\n  renameNode(node) {\n    if (node.name.trim().length === 0) return;\n    switch (node.category) {\n      case GojsNodeCategory.Diagram:\n        this.updateDiagram(node.data?.id, node.name);\n        break;\n      case GojsNodeCategory.Class:\n      case GojsNodeCategory.AssociativeClass:\n        this.goJsClassService.handleEditClassNameInLibrary(node.data, node);\n        break;\n      case GojsNodeCategory.Enumeration:\n        this.gojsEnumerationService.handleEditEnumNameInLibrary(node.data, node);\n        break;\n      case GojsNodeCategory.Folder:\n        this.gojsFolderService.handleFolderEdit(node.data, node);\n        break;\n      case GojsNodeCategory.Attribute:\n      case GojsNodeCategory.Operation:\n        this.gojsAttributeService.handleEditAttributeNameInLibrary(node.data, node);\n        break;\n      case GojsNodeCategory.EnumerationLiteral:\n        this.gojsLiteralService.handleEditAttributeNameInLibrary(node.data, node);\n        break;\n    }\n  }\n  deleteSelectedNodes(selectedNodes) {\n    this.doDeleteSelectedNodes(selectedNodes);\n    selectedNodes.forEach(node => {\n      this.treeNodeService.deleteGroupTreeNode(node);\n    });\n    this.propertyService.setPropertyData(null);\n  }\n  doDeleteSelectedNodes(selectedNodes) {\n    const classNodes = [],\n      enumNodes = [],\n      attributeNodes = [],\n      literalNodes = [],\n      folderNodes = [];\n    selectedNodes.forEach(node => {\n      switch (node.category) {\n        case GojsNodeCategory.Class:\n        case GojsNodeCategory.AssociativeClass:\n          if (!this.treeNodeService.nodeExistOrNot(node.parentTag, selectedNodes)) classNodes.push(node.data);\n          break;\n        case GojsNodeCategory.Enumeration:\n          if (!this.treeNodeService.nodeExistOrNot(node.parentTag, selectedNodes)) enumNodes.push(node.data);\n          break;\n        case GojsNodeCategory.Attribute:\n        case GojsNodeCategory.Operation:\n          if (!this.checkNodeExists(node.parentTag, selectedNodes)) {\n            attributeNodes.push(node.data);\n          }\n          break;\n        case GojsNodeCategory.EnumerationLiteral:\n          if (!this.checkNodeExists(node.parentTag, selectedNodes)) literalNodes.push(node.data);\n          break;\n        case GojsNodeCategory.Folder:\n          if (!this.treeNodeService.nodeExistOrNot(node.tag, selectedNodes)) folderNodes.push(node);\n          break;\n        default:\n          break;\n      }\n    });\n    if (classNodes.length > 0) {\n      this.goJsClassService.deleteTempClass(classNodes);\n    }\n    if (enumNodes.length > 0) {\n      this.gojsEnumerationService.deleteTempEnumeration(enumNodes);\n    }\n    if (attributeNodes.length > 0) {\n      this.gojsAttributeService.deleteMultipleAttrInPalette(attributeNodes);\n    }\n    if (literalNodes.length > 0) {\n      this.gojsLiteralService.deletePaletteLiteral(literalNodes);\n    }\n    if (folderNodes.length > 0) {\n      this.gojsFolderService.deleteSelectedFolder(folderNodes);\n    }\n  }\n  checkNodeExists(tag, nodes) {\n    return nodes.some(node => node.tag === tag);\n  }\n  static #_ = this.ɵfac = function ContextMenuActionService_Factory(t) {\n    return new (t || ContextMenuActionService)(i0.ɵɵinject(i1.MatDialog), i0.ɵɵinject(i2.SnackBarService), i0.ɵɵinject(i3.ProjectService), i0.ɵɵinject(i4.DiagramService), i0.ɵɵinject(i5.DiagramUtils), i0.ɵɵinject(i6.TreeNodeService), i0.ɵɵinject(i7.GojsClassService), i0.ɵɵinject(i8.GojsEnumerationService), i0.ɵɵinject(i9.GojsLiteralService), i0.ɵɵinject(i10.GojsAttributeService), i0.ɵɵinject(i11.GojsFolderService), i0.ɵɵinject(i12.PropertyService), i0.ɵɵinject(i13.AccessService), i0.ɵɵinject(i14.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ContextMenuActionService,\n    factory: ContextMenuActionService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["GoJsNodeIcon", "GojsNodeCategory", "AccessType", "ContextMenuAction", "TreeNodeTag", "DiagramDialogComponent", "DialogConfirmationComponent", "ContextMenuActionService", "constructor", "dialog", "snackBarService", "projectService", "diagramService", "diagramUtils", "treeNodeService", "goJsClassService", "gojsEnumerationService", "gojsLiteralService", "gojsAttributeService", "gojsFolderService", "propertyService", "_accessService", "router", "diagrams", "projectId", "baseFileName", "_hasEditAccessOnly", "currentProjectChanges", "subscribe", "project", "id", "activeDiagramChanges", "diagram", "currentDiagram", "currentProjectDiagramsChanges", "accessTypeChanges", "access", "Viewer", "executeAction", "actionName", "node", "selection", "AddDiagram", "openDiagramDialog", "name", "data", "targetedNode", "EditDiagram", "DeleteDiagram", "onDeleteDiagram", "AddLiteral", "openNodeCreationDialog", "headerTitle", "placeholder", "text", "btnText", "isCreation", "DeleteFolder", "DeleteClass", "DeleteEnumeration", "DeleteAttribute", "DeleteMethod", "DeleteLiteral", "nodesToDelete", "isSelected", "selected", "deleteSelectedNodes", "AddAttribute", "isAttribute", "AddOperation", "AddFolder", "AddClass", "AddAssociative", "AddEnumeration", "dialogRef", "open", "width", "afterClosed", "createdData", "onCreateNewFolder", "tag", "handleClassCreationFromLibrary", "handleEnumerationCreationFromLibrary", "addAttributeOrMethodFromLibrary", "Attribute", "idTemplateClass", "dataType", "Operation", "addLiteralInLibrary", "generateFileName", "diagramResult", "createDiagram", "idProject", "idFolder", "category", "Folder", "createdDiagram", "push", "setCurrentProjectDiagrams", "setActiveDiagram", "addGroupNodeInTree", "children", "Diagram", "icon", "parentTag", "Project", "DiagramWrapper", "isDraggable", "supportingNodes", "setPropertyData", "navigate", "replaceUrl", "updateDiagram", "diagramId", "diagramName", "updatedDiagram", "index", "findIndex", "value", "editGroupTreeNode", "openSnackbar", "existingNames", "Set", "map", "newFileName", "count", "has", "title", "reject", "confirm", "isConfirm", "deleteDiagram", "deleteGroupTreeNode", "filter", "length", "renameNode", "trim", "Class", "AssociativeClass", "handleEditClassNameInLibrary", "Enumeration", "handleEditEnumNameInLibrary", "handleFolderEdit", "handleEditAttributeNameInLibrary", "EnumerationLiteral", "selectedNodes", "doDeleteSelectedNodes", "for<PERSON>ach", "classNodes", "enumNodes", "attributeNodes", "literalNodes", "folderNodes", "nodeExistOrNot", "checkNodeExists", "deleteTempClass", "deleteTempEnumeration", "deleteMultipleAttrInPalette", "deletePaletteLiteral", "deleteSelectedFolder", "nodes", "some", "_", "i0", "ɵɵinject", "i1", "MatDialog", "i2", "SnackBarService", "i3", "ProjectService", "i4", "DiagramService", "i5", "DiagramUtils", "i6", "TreeNodeService", "i7", "GojsClassService", "i8", "GojsEnumerationService", "i9", "GojsLiteralService", "i10", "GojsAttributeService", "i11", "GojsFolderService", "i12", "PropertyService", "i13", "AccessService", "i14", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\contextMenuAction\\context-menu-action.service.ts"], "sourcesContent": ["import { SelectionModel } from '@angular/cdk/collections';\r\nimport { Injectable } from '@angular/core';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { Router } from '@angular/router';\r\nimport { GoJsNodeIcon } from 'src/app/shared/model/common';\r\nimport { Diagram } from 'src/app/shared/model/diagram';\r\nimport {\r\n  ConfirmDialogData,\r\n  DiagramDialogData,\r\n  DiagramDialogResult,\r\n} from 'src/app/shared/model/dialog';\r\nimport {\r\n  GojsDiagramAttributeNode,\r\n  GojsDiagramClassNode,\r\n  GojsDiagramEnumerationNode,\r\n  GojsDiagramLiteralNode,\r\n  GojsFolderNode,\r\n  GojsNodeCategory,\r\n} from 'src/app/shared/model/gojs';\r\nimport { AccessType } from 'src/app/shared/model/project';\r\nimport {\r\n  ContextMenuAction,\r\n  TreeNode,\r\n  TreeNodeTag,\r\n} from 'src/app/shared/model/treeNode';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { DiagramDialogComponent } from '../../components/diagram-dialog/diagram-dialog.component';\r\nimport { DialogConfirmationComponent } from '../../components/dialog-confirmation/dialog-confirmation.component';\r\nimport { AccessService } from '../access/access.service';\r\nimport { DiagramService } from '../diagram/diagram.service';\r\nimport { GojsAttributeService } from '../gojs/gojsAttribute/gojs-attribute.service';\r\nimport { GojsClassService } from '../gojs/gojsClass/gojs-class.service';\r\nimport { GojsEnumerationService } from '../gojs/gojsEnumeration/gojs-enumeration.service';\r\nimport { GojsFolderService } from '../gojs/gojsFolder/gojs-folder.service';\r\nimport { GojsLiteralService } from '../gojs/gojsLiteral/gojs-literal.service';\r\nimport { ProjectService } from '../project/project.service';\r\nimport { PropertyService } from '../property/property.service';\r\nimport { SnackBarService } from '../snackbar/snack-bar.service';\r\nimport { TreeNodeService } from '../treeNode/tree-node.service';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ContextMenuActionService {\r\n  currentDiagram!: Diagram;\r\n  diagrams: Diagram[] = [];\r\n  private projectId: number = -1;\r\n  private baseFileName = 'class-diagram';\r\n  private _hasEditAccessOnly: boolean = false;\r\n  constructor(\r\n    private dialog: MatDialog,\r\n    private snackBarService: SnackBarService,\r\n    private projectService: ProjectService,\r\n    private diagramService: DiagramService,\r\n    private diagramUtils: DiagramUtils,\r\n    private treeNodeService: TreeNodeService,\r\n    private goJsClassService: GojsClassService,\r\n    private gojsEnumerationService: GojsEnumerationService,\r\n    private gojsLiteralService: GojsLiteralService,\r\n    private gojsAttributeService: GojsAttributeService,\r\n    private gojsFolderService: GojsFolderService,\r\n    private propertyService: PropertyService,\r\n    private _accessService: AccessService,\r\n    private router: Router\r\n  ) {\r\n    this.projectService.currentProjectChanges().subscribe((project) => {\r\n      if (project && project.id) {\r\n        this.projectId = project.id;\r\n      }\r\n    });\r\n    this.diagramUtils.activeDiagramChanges().subscribe((diagram) => {\r\n      if (diagram) this.currentDiagram = diagram;\r\n    });\r\n    this.diagramUtils.currentProjectDiagramsChanges().subscribe((diagrams) => {\r\n      this.diagrams = diagrams;\r\n    });\r\n    this._accessService.accessTypeChanges().subscribe((access) => {\r\n      this._hasEditAccessOnly = access != AccessType.Viewer;\r\n    });\r\n  }\r\n\r\n  executeAction(\r\n    actionName: ContextMenuAction,\r\n    node: TreeNode,\r\n    selection: SelectionModel<TreeNode>\r\n  ): void {\r\n    switch (actionName) {\r\n      case ContextMenuAction.AddDiagram:\r\n        this.openDiagramDialog(\r\n          {\r\n            name: node.data?.name!,\r\n            targetedNode: node,\r\n          },\r\n          true\r\n        );\r\n        break;\r\n      case ContextMenuAction.EditDiagram:\r\n        this.openDiagramDialog(\r\n          { name: node.data?.name!, id: node.data?.id! },\r\n          false\r\n        );\r\n        break;\r\n      case ContextMenuAction.DeleteDiagram:\r\n        this.onDeleteDiagram(node);\r\n        break;\r\n      case ContextMenuAction.AddLiteral:\r\n        if (node.data && 'idTemplateEnumeration' in node.data) {\r\n          this.openNodeCreationDialog(\r\n            {\r\n              headerTitle: 'literal.header',\r\n              placeholder: 'literal.placeholder',\r\n              text: 'Literal',\r\n              btnText: '',\r\n              isCreation: true,\r\n            },\r\n            node,\r\n            actionName\r\n          );\r\n        }\r\n        break;\r\n      case ContextMenuAction.DeleteFolder:\r\n      case ContextMenuAction.DeleteClass:\r\n      case ContextMenuAction.DeleteEnumeration:\r\n      case ContextMenuAction.DeleteAttribute:\r\n      case ContextMenuAction.DeleteMethod:\r\n      case ContextMenuAction.DeleteLiteral:\r\n        // Check if the node is already in the selection to avoid duplication\r\n        const nodesToDelete = selection.isSelected(node)\r\n          ? [...selection.selected]\r\n          : [...selection.selected, node];\r\n        this.deleteSelectedNodes(nodesToDelete);\r\n        break;\r\n      case ContextMenuAction.AddAttribute:\r\n        if (node.data && 'idTemplateClass' in node.data) {\r\n          this.openNodeCreationDialog(\r\n            {\r\n              headerTitle: 'attribute.header',\r\n              placeholder: 'attribute.placeholder',\r\n              text: 'Attribute',\r\n              btnText: '',\r\n              isCreation: true,\r\n              isAttribute: true,\r\n            },\r\n            node,\r\n            actionName\r\n          );\r\n        }\r\n        break;\r\n      case ContextMenuAction.AddOperation:\r\n        if (node.data && 'idTemplateClass' in node.data) {\r\n          this.openNodeCreationDialog(\r\n            {\r\n              headerTitle: 'method.header',\r\n              placeholder: 'method.placeholder',\r\n              text: 'Method',\r\n              btnText: '',\r\n              isCreation: true,\r\n              isAttribute: true,\r\n            },\r\n            node,\r\n            actionName\r\n          );\r\n        }\r\n        break;\r\n      case ContextMenuAction.AddFolder:\r\n        this.openNodeCreationDialog(\r\n          {\r\n            headerTitle: 'folder.header',\r\n            placeholder: 'folder.placeholder',\r\n            text: 'New Folder',\r\n            btnText: '',\r\n            isCreation: true,\r\n          },\r\n          node,\r\n          actionName\r\n        );\r\n        break;\r\n      case ContextMenuAction.AddClass:\r\n      case ContextMenuAction.AddAssociative:\r\n        this.openNodeCreationDialog(\r\n          {\r\n            headerTitle: 'class.header',\r\n            placeholder: 'class.placeholder',\r\n            text: 'Class',\r\n            btnText: '',\r\n            isCreation: true,\r\n          },\r\n          node,\r\n          actionName\r\n        );\r\n        break;\r\n      case ContextMenuAction.AddEnumeration:\r\n        this.openNodeCreationDialog(\r\n          {\r\n            headerTitle: 'enumeration.header',\r\n            placeholder: 'enumeration.placeholder',\r\n            text: 'Enumeration',\r\n            btnText: '',\r\n            isCreation: true,\r\n          },\r\n          node,\r\n          actionName\r\n        );\r\n\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  }\r\n\r\n  private openNodeCreationDialog(\r\n    data: DiagramDialogData,\r\n    node: TreeNode,\r\n    actionName: ContextMenuAction\r\n  ): void {\r\n    const dialogRef = this.dialog.open<\r\n      DiagramDialogComponent,\r\n      DiagramDialogData,\r\n      DiagramDialogResult\r\n    >(DiagramDialogComponent, {\r\n      width: '300px',\r\n      data: {\r\n        text: data.text || '',\r\n        headerTitle: data.headerTitle,\r\n        placeholder: data.placeholder,\r\n        btnText: 'diagram.create',\r\n        isCreation: true,\r\n        isAttribute: data.isAttribute,\r\n      },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((createdData) => {\r\n      if (createdData) {\r\n        switch (actionName) {\r\n          case ContextMenuAction.AddFolder:\r\n            this.gojsFolderService.onCreateNewFolder(\r\n              createdData.name,\r\n              this.projectId,\r\n              this._hasEditAccessOnly,\r\n              node.tag\r\n            );\r\n            break;\r\n          case ContextMenuAction.AddClass:\r\n          case ContextMenuAction.AddAssociative:\r\n            this.goJsClassService.handleClassCreationFromLibrary(\r\n              createdData.name,\r\n              node,\r\n              actionName === ContextMenuAction.AddAssociative\r\n            );\r\n            break;\r\n          case ContextMenuAction.AddEnumeration:\r\n            this.gojsEnumerationService.handleEnumerationCreationFromLibrary(\r\n              createdData.name,\r\n              node\r\n            );\r\n            break;\r\n          case ContextMenuAction.AddAttribute:\r\n            if (node.data && 'idTemplateClass' in node.data) {\r\n              this.gojsAttributeService.addAttributeOrMethodFromLibrary(\r\n                createdData.name,\r\n                GojsNodeCategory.Attribute,\r\n                node.data.idTemplateClass,\r\n                createdData.dataType!\r\n              );\r\n            }\r\n            break;\r\n          case ContextMenuAction.AddOperation:\r\n            if (node.data && 'idTemplateClass' in node.data) {\r\n              this.gojsAttributeService.addAttributeOrMethodFromLibrary(\r\n                createdData.name,\r\n                GojsNodeCategory.Operation,\r\n                node.data.idTemplateClass,\r\n                createdData.dataType!\r\n              );\r\n            }\r\n            break;\r\n          case ContextMenuAction.AddLiteral:\r\n            this.gojsLiteralService.addLiteralInLibrary(\r\n              createdData.name,\r\n              node.data as GojsDiagramEnumerationNode\r\n            );\r\n            break;\r\n        }\r\n      }\r\n    });\r\n  }\r\n  private openDiagramDialog(\r\n    data: { name: string; id?: number; targetedNode?: TreeNode },\r\n    isCreation: boolean\r\n  ): void {\r\n    const dialogRef = this.dialog.open<\r\n      DiagramDialogComponent,\r\n      DiagramDialogData,\r\n      DiagramDialogResult\r\n    >(DiagramDialogComponent, {\r\n      width: '300px',\r\n      data: {\r\n        text: isCreation ? this.generateFileName() : data.name || '',\r\n        headerTitle: isCreation\r\n          ? 'diagram.createDiagram'\r\n          : 'diagram.editDiagram',\r\n        placeholder: 'diagram.diagramPlaceholderText',\r\n        btnText: isCreation ? 'diagram.create' : 'diagram.save',\r\n        isCreation: isCreation,\r\n      },\r\n    });\r\n\r\n    dialogRef.afterClosed().subscribe((diagramResult) => {\r\n      if (!diagramResult) return;\r\n      if (diagramResult.isCreation) {\r\n        // const parentNode = this.treeNodeService.findCurrentDiagramParentNode(\r\n        //   `atTag${GojsNodeCategory.Diagram}_${this.currentDiagram.id}`\r\n        // );\r\n        this.diagramService\r\n          .createDiagram({\r\n            idProject: this.projectId,\r\n            name: diagramResult.name,\r\n            idFolder:\r\n              data.targetedNode &&\r\n              data.targetedNode.category == GojsNodeCategory.Folder\r\n                ? (data.targetedNode.data as GojsFolderNode)?.idFolder\r\n                : 0,\r\n          })\r\n          .subscribe((createdDiagram: Diagram) => {\r\n            this.diagrams.push(createdDiagram);\r\n            this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n            this.diagramUtils.setActiveDiagram(createdDiagram);\r\n            this.treeNodeService.addGroupNodeInTree({\r\n              name: createdDiagram.name,\r\n              children: [],\r\n              category: GojsNodeCategory.Diagram,\r\n              icon: GoJsNodeIcon.Diagram,\r\n              tag: `atTag${GojsNodeCategory.Diagram}_${createdDiagram.id}`,\r\n              parentTag:\r\n                data.targetedNode &&\r\n                data.targetedNode.category == GojsNodeCategory.Project\r\n                  ? `${TreeNodeTag.DiagramWrapper}_${TreeNodeTag.Project}`\r\n                  : `${data.targetedNode?.tag}`,\r\n              data: createdDiagram,\r\n              isDraggable: true,\r\n              supportingNodes: [GojsNodeCategory.Diagram],\r\n            });\r\n            this.propertyService.setPropertyData(null);\r\n            if (createdDiagram.id) {\r\n              this.currentDiagram = createdDiagram;\r\n\r\n              // Update the URL to reflect the newly created diagram\r\n              // This is especially important when this is the first diagram in a project\r\n              // and the URL currently has the placeholder diagram ID (0)\r\n              this.router.navigate(\r\n                [`/editor/${this.projectId}/diagram/${createdDiagram.id}`],\r\n                {\r\n                  replaceUrl: true, // Replace the current URL instead of adding a new history entry\r\n                }\r\n              );\r\n            }\r\n          });\r\n      } else {\r\n        this.updateDiagram(data.id!, diagramResult.name);\r\n      }\r\n    });\r\n  }\r\n  private updateDiagram(diagramId: number, diagramName: string) {\r\n    if (this._hasEditAccessOnly) {\r\n      this.diagramService\r\n        .updateDiagram({\r\n          id: diagramId,\r\n          name: diagramName,\r\n        })\r\n        .subscribe((updatedDiagram) => {\r\n          const index = this.diagrams.findIndex(\r\n            (value) => value.id == this.currentDiagram!.id!\r\n          );\r\n          if (index !== -1) {\r\n            this.treeNodeService.editGroupTreeNode({\r\n              name: updatedDiagram.name,\r\n              children: [],\r\n              category: GojsNodeCategory.Diagram,\r\n              icon: GoJsNodeIcon.Diagram,\r\n              tag: `atTag${GojsNodeCategory.Diagram}_${updatedDiagram.id}`,\r\n              data: { ...this.diagrams[index], name: updatedDiagram.name },\r\n              isDraggable: true,\r\n              supportingNodes: [GojsNodeCategory.Diagram],\r\n            });\r\n            this.diagrams[index].name = updatedDiagram.name;\r\n          }\r\n\r\n          this.diagramUtils.setActiveDiagram(this.diagrams[index]);\r\n          this.snackBarService.openSnackbar('snackBar.diagramUpdateMsg');\r\n        });\r\n    }\r\n  }\r\n\r\n  private generateFileName(): string {\r\n    const existingNames = new Set(this.diagrams.map((diagram) => diagram.name));\r\n    let newFileName = this.baseFileName;\r\n    let count = 1;\r\n    while (existingNames.has(newFileName)) {\r\n      newFileName = `${this.baseFileName}${count}`;\r\n      count++;\r\n    }\r\n    return newFileName;\r\n  }\r\n\r\n  onDeleteDiagram(node: TreeNode) {\r\n    const dialogRef = this.dialog.open<\r\n      DialogConfirmationComponent,\r\n      ConfirmDialogData,\r\n      boolean\r\n    >(DialogConfirmationComponent, {\r\n      width: '320px',\r\n      data: {\r\n        title: 'dialog.title',\r\n        reject: 'dialog.no',\r\n        confirm: 'dialog.yes',\r\n      },\r\n    });\r\n    dialogRef.afterClosed().subscribe((isConfirm) => {\r\n      if (isConfirm) {\r\n        if (node && node.data?.id) {\r\n          this.diagramService.deleteDiagram(node.data?.id);\r\n          this.treeNodeService.deleteGroupTreeNode(node);\r\n          this.diagrams = this.diagrams.filter(\r\n            (diagram: Diagram) => diagram.id !== node.data?.id\r\n          );\r\n          this.diagramUtils.setCurrentProjectDiagrams(this.diagrams);\r\n          if (this.diagrams.length > 0) {\r\n            this.currentDiagram = this.diagrams[0];\r\n            this.diagramUtils.setActiveDiagram(this.currentDiagram);\r\n\r\n            // Update the URL to reflect the new active diagram\r\n            this.router.navigate(\r\n              [`/editor/${this.projectId}/diagram/${this.currentDiagram.id}`],\r\n              {\r\n                replaceUrl: true,\r\n              }\r\n            );\r\n          } else {\r\n            this.diagramUtils.setActiveDiagram(null);\r\n\r\n            // If there are no diagrams left, navigate to a placeholder URL\r\n            this.router.navigate([`/editor/${this.projectId}/diagram/0`], {\r\n              replaceUrl: true,\r\n            });\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  renameNode(node: TreeNode): void {\r\n    if (node.name.trim().length === 0) return;\r\n    switch (node.category) {\r\n      case GojsNodeCategory.Diagram:\r\n        this.updateDiagram(node.data?.id!, node.name);\r\n        break;\r\n      case GojsNodeCategory.Class:\r\n      case GojsNodeCategory.AssociativeClass:\r\n        this.goJsClassService.handleEditClassNameInLibrary(\r\n          node.data as GojsDiagramClassNode,\r\n          node\r\n        );\r\n        break;\r\n      case GojsNodeCategory.Enumeration:\r\n        this.gojsEnumerationService.handleEditEnumNameInLibrary(\r\n          node.data as GojsDiagramEnumerationNode,\r\n          node\r\n        );\r\n        break;\r\n      case GojsNodeCategory.Folder:\r\n        this.gojsFolderService.handleFolderEdit(\r\n          node.data as GojsFolderNode,\r\n          node\r\n        );\r\n        break;\r\n      case GojsNodeCategory.Attribute:\r\n      case GojsNodeCategory.Operation:\r\n        this.gojsAttributeService.handleEditAttributeNameInLibrary(\r\n          node.data as GojsDiagramAttributeNode,\r\n          node\r\n        );\r\n        break;\r\n      case GojsNodeCategory.EnumerationLiteral:\r\n        this.gojsLiteralService.handleEditAttributeNameInLibrary(\r\n          node.data as GojsDiagramLiteralNode,\r\n          node\r\n        );\r\n        break;\r\n    }\r\n  }\r\n\r\n  deleteSelectedNodes(selectedNodes: TreeNode[]) {\r\n    this.doDeleteSelectedNodes(selectedNodes);\r\n    selectedNodes.forEach((node) => {\r\n      this.treeNodeService.deleteGroupTreeNode(node);\r\n    });\r\n    this.propertyService.setPropertyData(null);\r\n  }\r\n\r\n  private doDeleteSelectedNodes(selectedNodes: TreeNode[]) {\r\n    const classNodes: GojsDiagramClassNode[] = [],\r\n      enumNodes: GojsDiagramEnumerationNode[] = [],\r\n      attributeNodes: go.ObjectData[] = [],\r\n      literalNodes: GojsDiagramLiteralNode[] = [],\r\n      folderNodes: TreeNode[] = [];\r\n    selectedNodes.forEach((node) => {\r\n      switch (node.category) {\r\n        case GojsNodeCategory.Class:\r\n        case GojsNodeCategory.AssociativeClass:\r\n          if (\r\n            !this.treeNodeService.nodeExistOrNot(node.parentTag!, selectedNodes)\r\n          )\r\n            classNodes.push(node.data! as GojsDiagramClassNode);\r\n          break;\r\n        case GojsNodeCategory.Enumeration:\r\n          if (\r\n            !this.treeNodeService.nodeExistOrNot(node.parentTag!, selectedNodes)\r\n          )\r\n            enumNodes.push(node.data! as GojsDiagramEnumerationNode);\r\n          break;\r\n        case GojsNodeCategory.Attribute:\r\n        case GojsNodeCategory.Operation:\r\n          if (!this.checkNodeExists(node.parentTag!, selectedNodes)) {\r\n            attributeNodes.push(node.data!);\r\n          }\r\n          break;\r\n        case GojsNodeCategory.EnumerationLiteral:\r\n          if (!this.checkNodeExists(node.parentTag!, selectedNodes))\r\n            literalNodes.push(node.data! as GojsDiagramLiteralNode);\r\n          break;\r\n        case GojsNodeCategory.Folder:\r\n          if (!this.treeNodeService.nodeExistOrNot(node.tag!, selectedNodes))\r\n            folderNodes.push(node);\r\n          break;\r\n\r\n        default:\r\n          break;\r\n      }\r\n    });\r\n    if (classNodes.length > 0) {\r\n      this.goJsClassService.deleteTempClass(classNodes);\r\n    }\r\n    if (enumNodes.length > 0) {\r\n      this.gojsEnumerationService.deleteTempEnumeration(enumNodes);\r\n    }\r\n    if (attributeNodes.length > 0) {\r\n      this.gojsAttributeService.deleteMultipleAttrInPalette(attributeNodes);\r\n    }\r\n    if (literalNodes.length > 0) {\r\n      this.gojsLiteralService.deletePaletteLiteral(literalNodes);\r\n    }\r\n    if (folderNodes.length > 0) {\r\n      this.gojsFolderService.deleteSelectedFolder(folderNodes);\r\n    }\r\n  }\r\n\r\n  private checkNodeExists(tag: string, nodes: TreeNode[]): boolean {\r\n    return nodes.some((node) => node.tag === tag);\r\n  }\r\n}\r\n"], "mappings": "AAIA,SAASA,YAAY,QAAQ,6BAA6B;AAO1D,SAMEC,gBAAgB,QACX,2BAA2B;AAClC,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SACEC,iBAAiB,EAEjBC,WAAW,QACN,+BAA+B;AAEtC,SAASC,sBAAsB,QAAQ,0DAA0D;AACjG,SAASC,2BAA2B,QAAQ,oEAAoE;;;;;;;;;;;;;;;;AAgBhH,OAAM,MAAOC,wBAAwB;EAMnCC,YACUC,MAAiB,EACjBC,eAAgC,EAChCC,cAA8B,EAC9BC,cAA8B,EAC9BC,YAA0B,EAC1BC,eAAgC,EAChCC,gBAAkC,EAClCC,sBAA8C,EAC9CC,kBAAsC,EACtCC,oBAA0C,EAC1CC,iBAAoC,EACpCC,eAAgC,EAChCC,cAA6B,EAC7BC,MAAc;IAbd,KAAAb,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,MAAM,GAANA,MAAM;IAlBhB,KAAAC,QAAQ,GAAc,EAAE;IAChB,KAAAC,SAAS,GAAW,CAAC,CAAC;IACtB,KAAAC,YAAY,GAAG,eAAe;IAC9B,KAAAC,kBAAkB,GAAY,KAAK;IAiBzC,IAAI,CAACf,cAAc,CAACgB,qBAAqB,EAAE,CAACC,SAAS,CAAEC,OAAO,IAAI;MAChE,IAAIA,OAAO,IAAIA,OAAO,CAACC,EAAE,EAAE;QACzB,IAAI,CAACN,SAAS,GAAGK,OAAO,CAACC,EAAE;;IAE/B,CAAC,CAAC;IACF,IAAI,CAACjB,YAAY,CAACkB,oBAAoB,EAAE,CAACH,SAAS,CAAEI,OAAO,IAAI;MAC7D,IAAIA,OAAO,EAAE,IAAI,CAACC,cAAc,GAAGD,OAAO;IAC5C,CAAC,CAAC;IACF,IAAI,CAACnB,YAAY,CAACqB,6BAA6B,EAAE,CAACN,SAAS,CAAEL,QAAQ,IAAI;MACvE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,CAAC;IACF,IAAI,CAACF,cAAc,CAACc,iBAAiB,EAAE,CAACP,SAAS,CAAEQ,MAAM,IAAI;MAC3D,IAAI,CAACV,kBAAkB,GAAGU,MAAM,IAAIlC,UAAU,CAACmC,MAAM;IACvD,CAAC,CAAC;EACJ;EAEAC,aAAaA,CACXC,UAA6B,EAC7BC,IAAc,EACdC,SAAmC;IAEnC,QAAQF,UAAU;MAChB,KAAKpC,iBAAiB,CAACuC,UAAU;QAC/B,IAAI,CAACC,iBAAiB,CACpB;UACEC,IAAI,EAAEJ,IAAI,CAACK,IAAI,EAAED,IAAK;UACtBE,YAAY,EAAEN;SACf,EACD,IAAI,CACL;QACD;MACF,KAAKrC,iBAAiB,CAAC4C,WAAW;QAChC,IAAI,CAACJ,iBAAiB,CACpB;UAAEC,IAAI,EAAEJ,IAAI,CAACK,IAAI,EAAED,IAAK;UAAEd,EAAE,EAAEU,IAAI,CAACK,IAAI,EAAEf;QAAG,CAAE,EAC9C,KAAK,CACN;QACD;MACF,KAAK3B,iBAAiB,CAAC6C,aAAa;QAClC,IAAI,CAACC,eAAe,CAACT,IAAI,CAAC;QAC1B;MACF,KAAKrC,iBAAiB,CAAC+C,UAAU;QAC/B,IAAIV,IAAI,CAACK,IAAI,IAAI,uBAAuB,IAAIL,IAAI,CAACK,IAAI,EAAE;UACrD,IAAI,CAACM,sBAAsB,CACzB;YACEC,WAAW,EAAE,gBAAgB;YAC7BC,WAAW,EAAE,qBAAqB;YAClCC,IAAI,EAAE,SAAS;YACfC,OAAO,EAAE,EAAE;YACXC,UAAU,EAAE;WACb,EACDhB,IAAI,EACJD,UAAU,CACX;;QAEH;MACF,KAAKpC,iBAAiB,CAACsD,YAAY;MACnC,KAAKtD,iBAAiB,CAACuD,WAAW;MAClC,KAAKvD,iBAAiB,CAACwD,iBAAiB;MACxC,KAAKxD,iBAAiB,CAACyD,eAAe;MACtC,KAAKzD,iBAAiB,CAAC0D,YAAY;MACnC,KAAK1D,iBAAiB,CAAC2D,aAAa;QAClC;QACA,MAAMC,aAAa,GAAGtB,SAAS,CAACuB,UAAU,CAACxB,IAAI,CAAC,GAC5C,CAAC,GAAGC,SAAS,CAACwB,QAAQ,CAAC,GACvB,CAAC,GAAGxB,SAAS,CAACwB,QAAQ,EAAEzB,IAAI,CAAC;QACjC,IAAI,CAAC0B,mBAAmB,CAACH,aAAa,CAAC;QACvC;MACF,KAAK5D,iBAAiB,CAACgE,YAAY;QACjC,IAAI3B,IAAI,CAACK,IAAI,IAAI,iBAAiB,IAAIL,IAAI,CAACK,IAAI,EAAE;UAC/C,IAAI,CAACM,sBAAsB,CACzB;YACEC,WAAW,EAAE,kBAAkB;YAC/BC,WAAW,EAAE,uBAAuB;YACpCC,IAAI,EAAE,WAAW;YACjBC,OAAO,EAAE,EAAE;YACXC,UAAU,EAAE,IAAI;YAChBY,WAAW,EAAE;WACd,EACD5B,IAAI,EACJD,UAAU,CACX;;QAEH;MACF,KAAKpC,iBAAiB,CAACkE,YAAY;QACjC,IAAI7B,IAAI,CAACK,IAAI,IAAI,iBAAiB,IAAIL,IAAI,CAACK,IAAI,EAAE;UAC/C,IAAI,CAACM,sBAAsB,CACzB;YACEC,WAAW,EAAE,eAAe;YAC5BC,WAAW,EAAE,oBAAoB;YACjCC,IAAI,EAAE,QAAQ;YACdC,OAAO,EAAE,EAAE;YACXC,UAAU,EAAE,IAAI;YAChBY,WAAW,EAAE;WACd,EACD5B,IAAI,EACJD,UAAU,CACX;;QAEH;MACF,KAAKpC,iBAAiB,CAACmE,SAAS;QAC9B,IAAI,CAACnB,sBAAsB,CACzB;UACEC,WAAW,EAAE,eAAe;UAC5BC,WAAW,EAAE,oBAAoB;UACjCC,IAAI,EAAE,YAAY;UAClBC,OAAO,EAAE,EAAE;UACXC,UAAU,EAAE;SACb,EACDhB,IAAI,EACJD,UAAU,CACX;QACD;MACF,KAAKpC,iBAAiB,CAACoE,QAAQ;MAC/B,KAAKpE,iBAAiB,CAACqE,cAAc;QACnC,IAAI,CAACrB,sBAAsB,CACzB;UACEC,WAAW,EAAE,cAAc;UAC3BC,WAAW,EAAE,mBAAmB;UAChCC,IAAI,EAAE,OAAO;UACbC,OAAO,EAAE,EAAE;UACXC,UAAU,EAAE;SACb,EACDhB,IAAI,EACJD,UAAU,CACX;QACD;MACF,KAAKpC,iBAAiB,CAACsE,cAAc;QACnC,IAAI,CAACtB,sBAAsB,CACzB;UACEC,WAAW,EAAE,oBAAoB;UACjCC,WAAW,EAAE,yBAAyB;UACtCC,IAAI,EAAE,aAAa;UACnBC,OAAO,EAAE,EAAE;UACXC,UAAU,EAAE;SACb,EACDhB,IAAI,EACJD,UAAU,CACX;QAED;MACF;QACE;;EAEN;EAEQY,sBAAsBA,CAC5BN,IAAuB,EACvBL,IAAc,EACdD,UAA6B;IAE7B,MAAMmC,SAAS,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAIhCtE,sBAAsB,EAAE;MACxBuE,KAAK,EAAE,OAAO;MACd/B,IAAI,EAAE;QACJS,IAAI,EAAET,IAAI,CAACS,IAAI,IAAI,EAAE;QACrBF,WAAW,EAAEP,IAAI,CAACO,WAAW;QAC7BC,WAAW,EAAER,IAAI,CAACQ,WAAW;QAC7BE,OAAO,EAAE,gBAAgB;QACzBC,UAAU,EAAE,IAAI;QAChBY,WAAW,EAAEvB,IAAI,CAACuB;;KAErB,CAAC;IAEFM,SAAS,CAACG,WAAW,EAAE,CAACjD,SAAS,CAAEkD,WAAW,IAAI;MAChD,IAAIA,WAAW,EAAE;QACf,QAAQvC,UAAU;UAChB,KAAKpC,iBAAiB,CAACmE,SAAS;YAC9B,IAAI,CAACnD,iBAAiB,CAAC4D,iBAAiB,CACtCD,WAAW,CAAClC,IAAI,EAChB,IAAI,CAACpB,SAAS,EACd,IAAI,CAACE,kBAAkB,EACvBc,IAAI,CAACwC,GAAG,CACT;YACD;UACF,KAAK7E,iBAAiB,CAACoE,QAAQ;UAC/B,KAAKpE,iBAAiB,CAACqE,cAAc;YACnC,IAAI,CAACzD,gBAAgB,CAACkE,8BAA8B,CAClDH,WAAW,CAAClC,IAAI,EAChBJ,IAAI,EACJD,UAAU,KAAKpC,iBAAiB,CAACqE,cAAc,CAChD;YACD;UACF,KAAKrE,iBAAiB,CAACsE,cAAc;YACnC,IAAI,CAACzD,sBAAsB,CAACkE,oCAAoC,CAC9DJ,WAAW,CAAClC,IAAI,EAChBJ,IAAI,CACL;YACD;UACF,KAAKrC,iBAAiB,CAACgE,YAAY;YACjC,IAAI3B,IAAI,CAACK,IAAI,IAAI,iBAAiB,IAAIL,IAAI,CAACK,IAAI,EAAE;cAC/C,IAAI,CAAC3B,oBAAoB,CAACiE,+BAA+B,CACvDL,WAAW,CAAClC,IAAI,EAChB3C,gBAAgB,CAACmF,SAAS,EAC1B5C,IAAI,CAACK,IAAI,CAACwC,eAAe,EACzBP,WAAW,CAACQ,QAAS,CACtB;;YAEH;UACF,KAAKnF,iBAAiB,CAACkE,YAAY;YACjC,IAAI7B,IAAI,CAACK,IAAI,IAAI,iBAAiB,IAAIL,IAAI,CAACK,IAAI,EAAE;cAC/C,IAAI,CAAC3B,oBAAoB,CAACiE,+BAA+B,CACvDL,WAAW,CAAClC,IAAI,EAChB3C,gBAAgB,CAACsF,SAAS,EAC1B/C,IAAI,CAACK,IAAI,CAACwC,eAAe,EACzBP,WAAW,CAACQ,QAAS,CACtB;;YAEH;UACF,KAAKnF,iBAAiB,CAAC+C,UAAU;YAC/B,IAAI,CAACjC,kBAAkB,CAACuE,mBAAmB,CACzCV,WAAW,CAAClC,IAAI,EAChBJ,IAAI,CAACK,IAAkC,CACxC;YACD;;;IAGR,CAAC,CAAC;EACJ;EACQF,iBAAiBA,CACvBE,IAA4D,EAC5DW,UAAmB;IAEnB,MAAMkB,SAAS,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAIhCtE,sBAAsB,EAAE;MACxBuE,KAAK,EAAE,OAAO;MACd/B,IAAI,EAAE;QACJS,IAAI,EAAEE,UAAU,GAAG,IAAI,CAACiC,gBAAgB,EAAE,GAAG5C,IAAI,CAACD,IAAI,IAAI,EAAE;QAC5DQ,WAAW,EAAEI,UAAU,GACnB,uBAAuB,GACvB,qBAAqB;QACzBH,WAAW,EAAE,gCAAgC;QAC7CE,OAAO,EAAEC,UAAU,GAAG,gBAAgB,GAAG,cAAc;QACvDA,UAAU,EAAEA;;KAEf,CAAC;IAEFkB,SAAS,CAACG,WAAW,EAAE,CAACjD,SAAS,CAAE8D,aAAa,IAAI;MAClD,IAAI,CAACA,aAAa,EAAE;MACpB,IAAIA,aAAa,CAAClC,UAAU,EAAE;QAC5B;QACA;QACA;QACA,IAAI,CAAC5C,cAAc,CAChB+E,aAAa,CAAC;UACbC,SAAS,EAAE,IAAI,CAACpE,SAAS;UACzBoB,IAAI,EAAE8C,aAAa,CAAC9C,IAAI;UACxBiD,QAAQ,EACNhD,IAAI,CAACC,YAAY,IACjBD,IAAI,CAACC,YAAY,CAACgD,QAAQ,IAAI7F,gBAAgB,CAAC8F,MAAM,GAChDlD,IAAI,CAACC,YAAY,CAACD,IAAuB,EAAEgD,QAAQ,GACpD;SACP,CAAC,CACDjE,SAAS,CAAEoE,cAAuB,IAAI;UACrC,IAAI,CAACzE,QAAQ,CAAC0E,IAAI,CAACD,cAAc,CAAC;UAClC,IAAI,CAACnF,YAAY,CAACqF,yBAAyB,CAAC,IAAI,CAAC3E,QAAQ,CAAC;UAC1D,IAAI,CAACV,YAAY,CAACsF,gBAAgB,CAACH,cAAc,CAAC;UAClD,IAAI,CAAClF,eAAe,CAACsF,kBAAkB,CAAC;YACtCxD,IAAI,EAAEoD,cAAc,CAACpD,IAAI;YACzByD,QAAQ,EAAE,EAAE;YACZP,QAAQ,EAAE7F,gBAAgB,CAACqG,OAAO;YAClCC,IAAI,EAAEvG,YAAY,CAACsG,OAAO;YAC1BtB,GAAG,EAAE,QAAQ/E,gBAAgB,CAACqG,OAAO,IAAIN,cAAc,CAAClE,EAAE,EAAE;YAC5D0E,SAAS,EACP3D,IAAI,CAACC,YAAY,IACjBD,IAAI,CAACC,YAAY,CAACgD,QAAQ,IAAI7F,gBAAgB,CAACwG,OAAO,GAClD,GAAGrG,WAAW,CAACsG,cAAc,IAAItG,WAAW,CAACqG,OAAO,EAAE,GACtD,GAAG5D,IAAI,CAACC,YAAY,EAAEkC,GAAG,EAAE;YACjCnC,IAAI,EAAEmD,cAAc;YACpBW,WAAW,EAAE,IAAI;YACjBC,eAAe,EAAE,CAAC3G,gBAAgB,CAACqG,OAAO;WAC3C,CAAC;UACF,IAAI,CAAClF,eAAe,CAACyF,eAAe,CAAC,IAAI,CAAC;UAC1C,IAAIb,cAAc,CAAClE,EAAE,EAAE;YACrB,IAAI,CAACG,cAAc,GAAG+D,cAAc;YAEpC;YACA;YACA;YACA,IAAI,CAAC1E,MAAM,CAACwF,QAAQ,CAClB,CAAC,WAAW,IAAI,CAACtF,SAAS,YAAYwE,cAAc,CAAClE,EAAE,EAAE,CAAC,EAC1D;cACEiF,UAAU,EAAE,IAAI,CAAE;aACnB,CACF;;QAEL,CAAC,CAAC;OACL,MAAM;QACL,IAAI,CAACC,aAAa,CAACnE,IAAI,CAACf,EAAG,EAAE4D,aAAa,CAAC9C,IAAI,CAAC;;IAEpD,CAAC,CAAC;EACJ;EACQoE,aAAaA,CAACC,SAAiB,EAAEC,WAAmB;IAC1D,IAAI,IAAI,CAACxF,kBAAkB,EAAE;MAC3B,IAAI,CAACd,cAAc,CAChBoG,aAAa,CAAC;QACblF,EAAE,EAAEmF,SAAS;QACbrE,IAAI,EAAEsE;OACP,CAAC,CACDtF,SAAS,CAAEuF,cAAc,IAAI;QAC5B,MAAMC,KAAK,GAAG,IAAI,CAAC7F,QAAQ,CAAC8F,SAAS,CAClCC,KAAK,IAAKA,KAAK,CAACxF,EAAE,IAAI,IAAI,CAACG,cAAe,CAACH,EAAG,CAChD;QACD,IAAIsF,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACtG,eAAe,CAACyG,iBAAiB,CAAC;YACrC3E,IAAI,EAAEuE,cAAc,CAACvE,IAAI;YACzByD,QAAQ,EAAE,EAAE;YACZP,QAAQ,EAAE7F,gBAAgB,CAACqG,OAAO;YAClCC,IAAI,EAAEvG,YAAY,CAACsG,OAAO;YAC1BtB,GAAG,EAAE,QAAQ/E,gBAAgB,CAACqG,OAAO,IAAIa,cAAc,CAACrF,EAAE,EAAE;YAC5De,IAAI,EAAE;cAAE,GAAG,IAAI,CAACtB,QAAQ,CAAC6F,KAAK,CAAC;cAAExE,IAAI,EAAEuE,cAAc,CAACvE;YAAI,CAAE;YAC5D+D,WAAW,EAAE,IAAI;YACjBC,eAAe,EAAE,CAAC3G,gBAAgB,CAACqG,OAAO;WAC3C,CAAC;UACF,IAAI,CAAC/E,QAAQ,CAAC6F,KAAK,CAAC,CAACxE,IAAI,GAAGuE,cAAc,CAACvE,IAAI;;QAGjD,IAAI,CAAC/B,YAAY,CAACsF,gBAAgB,CAAC,IAAI,CAAC5E,QAAQ,CAAC6F,KAAK,CAAC,CAAC;QACxD,IAAI,CAAC1G,eAAe,CAAC8G,YAAY,CAAC,2BAA2B,CAAC;MAChE,CAAC,CAAC;;EAER;EAEQ/B,gBAAgBA,CAAA;IACtB,MAAMgC,aAAa,GAAG,IAAIC,GAAG,CAAC,IAAI,CAACnG,QAAQ,CAACoG,GAAG,CAAE3F,OAAO,IAAKA,OAAO,CAACY,IAAI,CAAC,CAAC;IAC3E,IAAIgF,WAAW,GAAG,IAAI,CAACnG,YAAY;IACnC,IAAIoG,KAAK,GAAG,CAAC;IACb,OAAOJ,aAAa,CAACK,GAAG,CAACF,WAAW,CAAC,EAAE;MACrCA,WAAW,GAAG,GAAG,IAAI,CAACnG,YAAY,GAAGoG,KAAK,EAAE;MAC5CA,KAAK,EAAE;;IAET,OAAOD,WAAW;EACpB;EAEA3E,eAAeA,CAACT,IAAc;IAC5B,MAAMkC,SAAS,GAAG,IAAI,CAACjE,MAAM,CAACkE,IAAI,CAIhCrE,2BAA2B,EAAE;MAC7BsE,KAAK,EAAE,OAAO;MACd/B,IAAI,EAAE;QACJkF,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,WAAW;QACnBC,OAAO,EAAE;;KAEZ,CAAC;IACFvD,SAAS,CAACG,WAAW,EAAE,CAACjD,SAAS,CAAEsG,SAAS,IAAI;MAC9C,IAAIA,SAAS,EAAE;QACb,IAAI1F,IAAI,IAAIA,IAAI,CAACK,IAAI,EAAEf,EAAE,EAAE;UACzB,IAAI,CAAClB,cAAc,CAACuH,aAAa,CAAC3F,IAAI,CAACK,IAAI,EAAEf,EAAE,CAAC;UAChD,IAAI,CAAChB,eAAe,CAACsH,mBAAmB,CAAC5F,IAAI,CAAC;UAC9C,IAAI,CAACjB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC8G,MAAM,CACjCrG,OAAgB,IAAKA,OAAO,CAACF,EAAE,KAAKU,IAAI,CAACK,IAAI,EAAEf,EAAE,CACnD;UACD,IAAI,CAACjB,YAAY,CAACqF,yBAAyB,CAAC,IAAI,CAAC3E,QAAQ,CAAC;UAC1D,IAAI,IAAI,CAACA,QAAQ,CAAC+G,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAI,CAACrG,cAAc,GAAG,IAAI,CAACV,QAAQ,CAAC,CAAC,CAAC;YACtC,IAAI,CAACV,YAAY,CAACsF,gBAAgB,CAAC,IAAI,CAAClE,cAAc,CAAC;YAEvD;YACA,IAAI,CAACX,MAAM,CAACwF,QAAQ,CAClB,CAAC,WAAW,IAAI,CAACtF,SAAS,YAAY,IAAI,CAACS,cAAc,CAACH,EAAE,EAAE,CAAC,EAC/D;cACEiF,UAAU,EAAE;aACb,CACF;WACF,MAAM;YACL,IAAI,CAAClG,YAAY,CAACsF,gBAAgB,CAAC,IAAI,CAAC;YAExC;YACA,IAAI,CAAC7E,MAAM,CAACwF,QAAQ,CAAC,CAAC,WAAW,IAAI,CAACtF,SAAS,YAAY,CAAC,EAAE;cAC5DuF,UAAU,EAAE;aACb,CAAC;;;;IAIV,CAAC,CAAC;EACJ;EAEAwB,UAAUA,CAAC/F,IAAc;IACvB,IAAIA,IAAI,CAACI,IAAI,CAAC4F,IAAI,EAAE,CAACF,MAAM,KAAK,CAAC,EAAE;IACnC,QAAQ9F,IAAI,CAACsD,QAAQ;MACnB,KAAK7F,gBAAgB,CAACqG,OAAO;QAC3B,IAAI,CAACU,aAAa,CAACxE,IAAI,CAACK,IAAI,EAAEf,EAAG,EAAEU,IAAI,CAACI,IAAI,CAAC;QAC7C;MACF,KAAK3C,gBAAgB,CAACwI,KAAK;MAC3B,KAAKxI,gBAAgB,CAACyI,gBAAgB;QACpC,IAAI,CAAC3H,gBAAgB,CAAC4H,4BAA4B,CAChDnG,IAAI,CAACK,IAA4B,EACjCL,IAAI,CACL;QACD;MACF,KAAKvC,gBAAgB,CAAC2I,WAAW;QAC/B,IAAI,CAAC5H,sBAAsB,CAAC6H,2BAA2B,CACrDrG,IAAI,CAACK,IAAkC,EACvCL,IAAI,CACL;QACD;MACF,KAAKvC,gBAAgB,CAAC8F,MAAM;QAC1B,IAAI,CAAC5E,iBAAiB,CAAC2H,gBAAgB,CACrCtG,IAAI,CAACK,IAAsB,EAC3BL,IAAI,CACL;QACD;MACF,KAAKvC,gBAAgB,CAACmF,SAAS;MAC/B,KAAKnF,gBAAgB,CAACsF,SAAS;QAC7B,IAAI,CAACrE,oBAAoB,CAAC6H,gCAAgC,CACxDvG,IAAI,CAACK,IAAgC,EACrCL,IAAI,CACL;QACD;MACF,KAAKvC,gBAAgB,CAAC+I,kBAAkB;QACtC,IAAI,CAAC/H,kBAAkB,CAAC8H,gCAAgC,CACtDvG,IAAI,CAACK,IAA8B,EACnCL,IAAI,CACL;QACD;;EAEN;EAEA0B,mBAAmBA,CAAC+E,aAAyB;IAC3C,IAAI,CAACC,qBAAqB,CAACD,aAAa,CAAC;IACzCA,aAAa,CAACE,OAAO,CAAE3G,IAAI,IAAI;MAC7B,IAAI,CAAC1B,eAAe,CAACsH,mBAAmB,CAAC5F,IAAI,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAACpB,eAAe,CAACyF,eAAe,CAAC,IAAI,CAAC;EAC5C;EAEQqC,qBAAqBA,CAACD,aAAyB;IACrD,MAAMG,UAAU,GAA2B,EAAE;MAC3CC,SAAS,GAAiC,EAAE;MAC5CC,cAAc,GAAoB,EAAE;MACpCC,YAAY,GAA6B,EAAE;MAC3CC,WAAW,GAAe,EAAE;IAC9BP,aAAa,CAACE,OAAO,CAAE3G,IAAI,IAAI;MAC7B,QAAQA,IAAI,CAACsD,QAAQ;QACnB,KAAK7F,gBAAgB,CAACwI,KAAK;QAC3B,KAAKxI,gBAAgB,CAACyI,gBAAgB;UACpC,IACE,CAAC,IAAI,CAAC5H,eAAe,CAAC2I,cAAc,CAACjH,IAAI,CAACgE,SAAU,EAAEyC,aAAa,CAAC,EAEpEG,UAAU,CAACnD,IAAI,CAACzD,IAAI,CAACK,IAA6B,CAAC;UACrD;QACF,KAAK5C,gBAAgB,CAAC2I,WAAW;UAC/B,IACE,CAAC,IAAI,CAAC9H,eAAe,CAAC2I,cAAc,CAACjH,IAAI,CAACgE,SAAU,EAAEyC,aAAa,CAAC,EAEpEI,SAAS,CAACpD,IAAI,CAACzD,IAAI,CAACK,IAAmC,CAAC;UAC1D;QACF,KAAK5C,gBAAgB,CAACmF,SAAS;QAC/B,KAAKnF,gBAAgB,CAACsF,SAAS;UAC7B,IAAI,CAAC,IAAI,CAACmE,eAAe,CAAClH,IAAI,CAACgE,SAAU,EAAEyC,aAAa,CAAC,EAAE;YACzDK,cAAc,CAACrD,IAAI,CAACzD,IAAI,CAACK,IAAK,CAAC;;UAEjC;QACF,KAAK5C,gBAAgB,CAAC+I,kBAAkB;UACtC,IAAI,CAAC,IAAI,CAACU,eAAe,CAAClH,IAAI,CAACgE,SAAU,EAAEyC,aAAa,CAAC,EACvDM,YAAY,CAACtD,IAAI,CAACzD,IAAI,CAACK,IAA+B,CAAC;UACzD;QACF,KAAK5C,gBAAgB,CAAC8F,MAAM;UAC1B,IAAI,CAAC,IAAI,CAACjF,eAAe,CAAC2I,cAAc,CAACjH,IAAI,CAACwC,GAAI,EAAEiE,aAAa,CAAC,EAChEO,WAAW,CAACvD,IAAI,CAACzD,IAAI,CAAC;UACxB;QAEF;UACE;;IAEN,CAAC,CAAC;IACF,IAAI4G,UAAU,CAACd,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACvH,gBAAgB,CAAC4I,eAAe,CAACP,UAAU,CAAC;;IAEnD,IAAIC,SAAS,CAACf,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACtH,sBAAsB,CAAC4I,qBAAqB,CAACP,SAAS,CAAC;;IAE9D,IAAIC,cAAc,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACpH,oBAAoB,CAAC2I,2BAA2B,CAACP,cAAc,CAAC;;IAEvE,IAAIC,YAAY,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACrH,kBAAkB,CAAC6I,oBAAoB,CAACP,YAAY,CAAC;;IAE5D,IAAIC,WAAW,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACnH,iBAAiB,CAAC4I,oBAAoB,CAACP,WAAW,CAAC;;EAE5D;EAEQE,eAAeA,CAAC1E,GAAW,EAAEgF,KAAiB;IACpD,OAAOA,KAAK,CAACC,IAAI,CAAEzH,IAAI,IAAKA,IAAI,CAACwC,GAAG,KAAKA,GAAG,CAAC;EAC/C;EAAC,QAAAkF,CAAA,G;qBAngBU3J,wBAAwB,EAAA4J,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,gBAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,sBAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,kBAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,oBAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,iBAAA,GAAAvB,EAAA,CAAAC,QAAA,CAAAuB,GAAA,CAAAC,eAAA,GAAAzB,EAAA,CAAAC,QAAA,CAAAyB,GAAA,CAAAC,aAAA,GAAA3B,EAAA,CAAAC,QAAA,CAAA2B,GAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAxB1L,wBAAwB;IAAA2L,OAAA,EAAxB3L,wBAAwB,CAAA4L,IAAA;IAAAC,UAAA,EAFvB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}