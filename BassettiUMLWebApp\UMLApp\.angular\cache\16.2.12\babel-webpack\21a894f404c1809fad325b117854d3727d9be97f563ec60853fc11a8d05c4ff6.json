{"ast": null, "code": "import { EventEmitter, effect } from '@angular/core';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { ProjectFilterComponent } from '../project-filter/project-filter.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/search-bar/search-bar.service\";\nimport * as i2 from \"../../services/user/user.service\";\nimport * as i3 from \"@angular/cdk/a11y\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/tooltip\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/table\";\nimport * as i10 from \"@angular/material/sort\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"@angular/material/badge\";\nimport * as i13 from \"@fortawesome/angular-fontawesome\";\nimport * as i14 from \"../search-bar/search-bar.component\";\nimport * as i15 from \"../project-filter/project-filter.component\";\nimport * as i16 from \"@ngx-translate/core\";\nimport * as i17 from \"../../../shared/pipes/truncate.pipe\";\nimport * as i18 from \"../../../shared/pipes/time-ago.pipe\";\nconst _c0 = [\"actionMenuTrigger\"];\nfunction ProjectTableComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"span\", 24);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 1, \"dashboard.loading\"));\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_1_th_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", column_r9.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, column_r9.display), \" \");\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_1_th_1_Template, 3, 4, \"th\", 28);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_template_2_th_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"translate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const column_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", column_r9.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 2, column_r9.display), \" \");\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectTableComponent_ng_container_16_ng_template_2_th_0_Template, 3, 4, \"th\", 30);\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r25 = ctx.$implicit;\n    const column_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, element_r25[column_r9.key]), \" \");\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_td_1_Template, 3, 3, \"td\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_td_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"truncate\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r28 = ctx.$implicit;\n    const column_r9 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, element_r28[column_r9.key], 120), \" \");\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_td_0_Template, 3, 4, \"td\", 33);\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵtemplate(2, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_Template, 1, 0, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r22 = i0.ɵɵreference(3);\n    const column_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", column_r9.isDate)(\"ngIfElse\", _r22);\n  }\n}\nconst _c1 = function () {\n  return [\"fas\", \"lock\"];\n};\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_fa_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fa-icon\", 36);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(1, _c1));\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 34);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_fa_icon_1_Template, 1, 2, \"fa-icon\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const element_r32 = ctx.$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r31.checkProjectLock(element_r32));\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_Template, 2, 1, \"td\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_Template, 4, 2, \"ng-container\", 22);\n    i0.ɵɵtemplate(2, ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_Template, 2, 0, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !column_r9.isLock);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", column_r9.isLock);\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 34)(1, \"div\", 37)(2, \"button\", 38)(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_5_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const element_r36 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.openEditor(element_r36));\n    });\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"launch\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-menu\", null, 40)(11, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const element_r36 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.onEditProject(element_r36));\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const element_r36 = restoredCtx.$implicit;\n      const ctx_r41 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r41.onShareProject(element_r36));\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"share\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const element_r36 = restoredCtx.$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r42.onDeleteProject(element_r36));\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\", 42);\n    i0.ɵɵtext(25, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"translate\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const element_r36 = ctx.$implicit;\n    const _r37 = i0.ɵɵreference(10);\n    const ctx_r35 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r37)(\"disabled\", !(element_r36.accessType == 0 && !ctx_r35.checkProjectLock(element_r36)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matTooltip\", i0.ɵɵpipeBind1(6, 6, \"dashboard.table.open\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(16, 8, \"dashboard.table.edit\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 10, \"dashboard.table.share\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(28, 12, \"dashboard.table.delete\"));\n  }\n}\nfunction ProjectTableComponent_ng_container_16_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template, 29, 14, \"td\", 33);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ProjectTableComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 25);\n    i0.ɵɵtemplate(1, ProjectTableComponent_ng_container_16_ng_container_1_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵtemplate(2, ProjectTableComponent_ng_container_16_ng_template_2_Template, 1, 0, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(4, ProjectTableComponent_ng_container_16_ng_container_4_Template, 3, 2, \"ng-container\", 22);\n    i0.ɵɵtemplate(5, ProjectTableComponent_ng_container_16_ng_container_5_Template, 2, 0, \"ng-container\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r9 = ctx.$implicit;\n    const _r11 = i0.ɵɵreference(3);\n    i0.ɵɵproperty(\"matColumnDef\", column_r9.key);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !column_r9.isLock && !column_r9.isAction)(\"ngIfElse\", _r11);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !column_r9.isAction);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", column_r9.isAction);\n  }\n}\nfunction ProjectTableComponent_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 43);\n  }\n}\nconst _c2 = function (a0) {\n  return {\n    gray: a0\n  };\n};\nfunction ProjectTableComponent_tr_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 44);\n    i0.ɵɵlistener(\"dblclick\", function ProjectTableComponent_tr_18_Template_tr_dblclick_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const row_r43 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.openEditor(row_r43));\n    })(\"contextmenu\", function ProjectTableComponent_tr_18_Template_tr_contextmenu_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const row_r43 = restoredCtx.$implicit;\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onRightClick($event, row_r43));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const even_r44 = ctx.even;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c2, even_r44));\n  }\n}\nfunction ProjectTableComponent_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"translate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.isTableLoading ? \"\" : i0.ɵɵpipeBind1(3, 1, \"dashboard.noProject\"), \" \");\n  }\n}\nfunction ProjectTableComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_33_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onEditProject(ctx_r48.contextMenuRow));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_33_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.onShareProject(ctx_r50.contextMenuRow));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"share\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function ProjectTableComponent_ng_container_33_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onDeleteProject(ctx_r51.contextMenuRow));\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\", 42);\n    i0.ɵɵtext(15, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"translate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(6, 3, \"dashboard.table.edit\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 5, \"dashboard.table.share\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(18, 7, \"dashboard.table.delete\"));\n  }\n}\nconst _c3 = function () {\n  return [\"fal\", \"filter\"];\n};\nconst _c4 = function () {\n  return [10, 15, 20];\n};\nexport class ProjectTableComponent {\n  set loading(value) {\n    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError\n    if (this.isTableLoading !== value) {\n      setTimeout(() => {\n        this.isTableLoading = value;\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  constructor(searchBarService, userService, liveAnnouncer, cdr) {\n    this.searchBarService = searchBarService;\n    this.userService = userService;\n    this.liveAnnouncer = liveAnnouncer;\n    this.cdr = cdr;\n    this.noOfFilter = 0;\n    this.currentPage = 0;\n    // Track loading state specifically for the table\n    this.isTableLoading = false;\n    // Strongly typed column configuration\n    this.columnConfig = [{\n      key: 'isLock',\n      display: '',\n      isAction: false,\n      isLock: true\n    }, {\n      key: 'name',\n      display: 'dashboard.table.name',\n      isAction: false\n    }, {\n      key: 'description',\n      display: 'dashboard.table.description',\n      isAction: false\n    }, {\n      key: 'type',\n      display: 'dashboard.table.type',\n      isAction: false\n    }, {\n      key: 'productLine',\n      display: 'dashboard.table.productLine',\n      isAction: false\n    }, {\n      key: 'admin',\n      display: 'dashboard.table.owner',\n      isAction: false\n    }, {\n      key: 'lastModifiedDate',\n      display: 'dashboard.table.lastModified',\n      isAction: false,\n      isDate: true\n    }, {\n      key: 'action',\n      display: 'dashboard.table.action',\n      isAction: true\n    }];\n    // Computed display columns\n    this.displayedColumns = this.columnConfig.map(col => col.key);\n    this.menuPosition = {\n      x: '0px',\n      y: '0px'\n    };\n    // Output event emitters\n    this.openProject = new EventEmitter();\n    this.editProject = new EventEmitter();\n    this.deleteProject = new EventEmitter();\n    this.shareProject = new EventEmitter();\n    this.paginationChanged = new EventEmitter();\n    this.sortChange = new EventEmitter();\n    this.sortConfig = {\n      active: 'lastModifiedDate',\n      direction: 'desc'\n    };\n    this.dataSource = new MatTableDataSource();\n    this.totalCount = 0;\n    this.pageSize = 10;\n    effect(() => {\n      this.noOfFilter = this.searchBarService.filterCount();\n    });\n  }\n  ngAfterViewInit() {\n    // Apply initial sort configuration\n    if (this.sort) {\n      this.sort.active = this.sortConfig.active;\n      this.sort.direction = this.sortConfig.direction;\n    }\n  }\n  onRightClick(event, row) {\n    event.preventDefault();\n    // Store the clicked row\n    this.contextMenuRow = row;\n    // Update menu position\n    this.menuPosition = {\n      x: `${event.clientX}px`,\n      y: `${event.clientY - 40}px`\n    };\n    // Open the action menu programmatically\n    setTimeout(() => {\n      this.actionMenuTrigger.openMenu();\n    });\n  }\n  /**\n   * Handles sort change events from the table.\n   * Announces the sort change for accessibility and emits the sort event to the parent component.\n   * @param sortState Sort event containing active column and direction\n   */\n  announceSortChange(sortState) {\n    // Announce sort change for accessibility\n    if (sortState.direction) {\n      this.liveAnnouncer.announce(`Sorted ${sortState.direction}ending`);\n    } else {\n      this.liveAnnouncer.announce('Sorting cleared');\n    }\n    // Emit sort change event to parent component\n    this.sortChange.emit(sortState);\n  }\n  // Methods for table actions\n  openEditor(project) {\n    if (this.projectFilterComponent) this.projectFilterComponent.resetFilters();\n    debugger;\n    this.openProject.emit(project);\n  }\n  onEditProject(project) {\n    this.editProject.emit(project);\n  }\n  onDeleteProject(project) {\n    this.deleteProject.emit(project);\n  }\n  onShareProject(project) {\n    this.shareProject.emit(project);\n  }\n  // Improved project lock checking with null safety\n  checkProjectLock(project) {\n    // If the user is a Viewer, we don't show the project as locked\n    // if (project.accessType === AccessType.Viewer) {\n    //   return false;\n    // }\n    const loggedInUser = this.userService.getUser();\n    return !!(loggedInUser && project.isProjectLocked && project.lockingIdContact !== 0 && project.lockingIdContact !== loggedInUser.id);\n  }\n  /**\n   * Applies a search filter by setting the search term in the search bar service.\n   * This will trigger server-side filtering through the dashboard component.\n   * @param searchTerm The search term to filter by\n   */\n  applyFilter(searchTerm) {\n    if (searchTerm === null) return;\n    // Set the search term in the service to trigger server-side filtering\n    this.searchBarService.search(searchTerm.trim());\n  }\n  /**\n   * Clears the current filter by setting an empty search term.\n   */\n  clearFilter() {\n    this.searchBarService.search('');\n  }\n  onPageChange(event) {\n    this.currentPage = event.pageIndex + 1;\n    this.pageSize = event.pageSize;\n    this.paginationChanged.emit({\n      pageSize: this.pageSize,\n      currentPage: this.currentPage\n    });\n  }\n  static #_ = this.ɵfac = function ProjectTableComponent_Factory(t) {\n    return new (t || ProjectTableComponent)(i0.ɵɵdirectiveInject(i1.SearchBarService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.LiveAnnouncer), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectTableComponent,\n    selectors: [[\"app-project-table\"]],\n    viewQuery: function ProjectTableComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatPaginator, 5);\n        i0.ɵɵviewQuery(MatSort, 5);\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(ProjectFilterComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.actionMenuTrigger = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.projectFilterComponent = _t.first);\n      }\n    },\n    inputs: {\n      sortConfig: \"sortConfig\",\n      dataSource: \"dataSource\",\n      totalCount: \"totalCount\",\n      pageSize: \"pageSize\",\n      loading: [\"isLoading\", \"loading\"]\n    },\n    outputs: {\n      openProject: \"open\",\n      editProject: \"edit\",\n      deleteProject: \"delete\",\n      shareProject: \"share\",\n      paginationChanged: \"paginationChanged\",\n      sortChange: \"sortChange\"\n    },\n    decls: 34,\n    vars: 39,\n    consts: [[1, \"table-container\"], [1, \"table-header\"], [1, \"header-left\", 3, \"matMenuTriggerFor\"], [\"size\", \"xl\", \"matTooltipClass\", \"tooltip-custom\", 1, \"header-icon\", 3, \"icon\", \"matTooltip\"], [\"matBadgeOverlap\", \"false\", \"matBadgeSize\", \"small\", 1, \"header-paragraph\", 3, \"matBadge\", \"matBadgeHidden\"], [3, \"overlapTrigger\", \"click\"], [\"filterMenu\", \"matMenu\"], [1, \"search-container\", 3, \"placeholder\", \"searchChanged\"], [1, \"mat-elevation\", \"table\"], [\"class\", \"table-loading-shade\", 4, \"ngIf\"], [\"mat-table\", \"\", \"matSort\", \"\", 3, \"dataSource\", \"matSortActive\", \"matSortDirection\", \"matSortChange\"], [3, \"matColumnDef\", 4, \"ngFor\", \"ngForOf\"], [\"mat-header-row\", \"\", \"class\", \"table-header-row\", 4, \"matHeaderRowDef\", \"matHeaderRowDefSticky\"], [\"mat-row\", \"\", \"class\", \"example-element-row\", 3, \"ngClass\", \"dblclick\", \"contextmenu\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"mat-row\", 4, \"matNoDataRow\"], [1, \"table-bottom\"], [\"showFirstLastButtons\", \"\", \"aria-label\", \"Select page of periodic elements\", 3, \"pageSize\", \"length\", \"pageSizeOptions\", \"disabled\", \"page\"], [1, \"hidden-menu-trigger\"], [\"mat-icon-button\", \"\", 2, \"visibility\", \"hidden\", 3, \"matMenuTriggerFor\"], [\"actionMenuTrigger\", \"matMenuTrigger\"], [\"actionMenu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"], [4, \"ngIf\"], [1, \"table-loading-shade\"], [1, \"loading-text\"], [3, \"matColumnDef\"], [4, \"ngIf\", \"ngIfElse\"], [\"unsortableHeader\", \"\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 3, \"ngClass\", 4, \"matHeaderCellDef\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 3, \"ngClass\"], [\"mat-header-cell\", \"\", 3, \"ngClass\", 4, \"matHeaderCellDef\"], [\"mat-header-cell\", \"\", 3, \"ngClass\"], [\"regularCell\", \"\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"mat-cell\", \"\"], [\"size\", \"lg\", \"class\", \"lock-icon\", 3, \"icon\", 4, \"ngIf\"], [\"size\", \"lg\", 1, \"lock-icon\", 3, \"icon\"], [1, \"action-column\"], [\"mat-icon-button\", \"\", \"aria-label\", \"More actions\", 1, \"action-btn\", 3, \"matMenuTriggerFor\", \"disabled\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Open Project\", 1, \"action-btn\", 3, \"matTooltip\", \"click\"], [\"menu\", \"matMenu\"], [\"mat-menu-item\", \"\", 1, \"delete-menu\", 3, \"click\"], [1, \"menu-icon\"], [\"mat-header-row\", \"\", 1, \"table-header-row\"], [\"mat-row\", \"\", 1, \"example-element-row\", 3, \"ngClass\", \"dblclick\", \"contextmenu\"], [1, \"mat-row\"], [\"colspan\", \"8\", 1, \"mat-cell\", \"no-project-text\"]],\n    template: function ProjectTableComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"fa-icon\", 3);\n        i0.ɵɵpipe(4, \"translate\");\n        i0.ɵɵelementStart(5, \"p\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵpipe(7, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"mat-menu\", 5, 6);\n        i0.ɵɵlistener(\"click\", function ProjectTableComponent_Template_mat_menu_click_8_listener($event) {\n          return $event.stopPropagation();\n        });\n        i0.ɵɵelement(10, \"app-project-filter\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"app-search-bar\", 7);\n        i0.ɵɵlistener(\"searchChanged\", function ProjectTableComponent_Template_app_search_bar_searchChanged_11_listener($event) {\n          return ctx.applyFilter($event);\n        });\n        i0.ɵɵpipe(12, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 8);\n        i0.ɵɵtemplate(14, ProjectTableComponent_div_14_Template, 4, 3, \"div\", 9);\n        i0.ɵɵelementStart(15, \"table\", 10);\n        i0.ɵɵlistener(\"matSortChange\", function ProjectTableComponent_Template_table_matSortChange_15_listener($event) {\n          return ctx.announceSortChange($event);\n        });\n        i0.ɵɵtemplate(16, ProjectTableComponent_ng_container_16_Template, 6, 5, \"ng-container\", 11);\n        i0.ɵɵtemplate(17, ProjectTableComponent_tr_17_Template, 1, 0, \"tr\", 12);\n        i0.ɵɵtemplate(18, ProjectTableComponent_tr_18_Template, 1, 3, \"tr\", 13);\n        i0.ɵɵtemplate(19, ProjectTableComponent_tr_19_Template, 4, 3, \"tr\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 15)(21, \"mat-paginator\", 16);\n        i0.ɵɵlistener(\"page\", function ProjectTableComponent_Template_mat_paginator_page_21_listener($event) {\n          return ctx.onPageChange($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 17);\n        i0.ɵɵelement(23, \"button\", 18, 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"mat-menu\", null, 20)(27, \"button\", 21);\n        i0.ɵɵlistener(\"click\", function ProjectTableComponent_Template_button_click_27_listener() {\n          return ctx.openEditor(ctx.contextMenuRow);\n        });\n        i0.ɵɵelementStart(28, \"mat-icon\");\n        i0.ɵɵtext(29, \"launch\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"span\");\n        i0.ɵɵtext(31);\n        i0.ɵɵpipe(32, \"translate\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(33, ProjectTableComponent_ng_container_33_Template, 19, 9, \"ng-container\", 22);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(9);\n        const _r7 = i0.ɵɵreference(26);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"icon\", i0.ɵɵpureFunction0(37, _c3))(\"matTooltip\", i0.ɵɵpipeBind1(4, 29, \"dashboard.table.filter\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"matBadge\", ctx.noOfFilter)(\"matBadgeHidden\", ctx.noOfFilter == 0);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(7, 31, \"dashboard.table.filter\"), \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"overlapTrigger\", false);\n        i0.ɵɵadvance(3);\n        i0.ɵɵpropertyInterpolate(\"placeholder\", i0.ɵɵpipeBind1(12, 33, \"dashboard.search.placeholder\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isTableLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassProp(\"loading-results\", ctx.isTableLoading);\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource)(\"matSortActive\", ctx.sortConfig.active)(\"matSortDirection\", ctx.sortConfig.direction);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngForOf\", ctx.columnConfig);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns)(\"matHeaderRowDefSticky\", true);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"pageSize\", ctx.pageSize)(\"length\", ctx.totalCount)(\"pageSizeOptions\", i0.ɵɵpureFunction0(38, _c4))(\"disabled\", ctx.totalCount <= 10);\n        i0.ɵɵadvance(1);\n        i0.ɵɵstyleProp(\"left\", ctx.menuPosition.x)(\"top\", ctx.menuPosition.y);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", _r7);\n        i0.ɵɵadvance(8);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(32, 35, \"dashboard.table.open\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.contextMenuRow && ctx.contextMenuRow.accessType == 0 && !ctx.checkProjectLock(ctx.contextMenuRow));\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.MatIconButton, i6.MatTooltip, i7.MatIcon, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatTable, i9.MatHeaderCellDef, i9.MatHeaderRowDef, i9.MatColumnDef, i9.MatCellDef, i9.MatRowDef, i9.MatHeaderCell, i9.MatCell, i9.MatHeaderRow, i9.MatRow, i9.MatNoDataRow, i10.MatSort, i10.MatSortHeader, i11.MatPaginator, i12.MatBadge, i13.FaIconComponent, i14.SearchBarComponent, i15.ProjectFilterComponent, i16.TranslatePipe, i17.TruncatePipe, i18.TimeAgoPipe],\n    styles: [\".table-container[_ngcontent-%COMP%] {\\n  height: calc(100vh - 3.875rem);\\n  border: 1px solid rgb(209, 199, 199);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.table-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 2.55rem;\\n  justify-content: space-between;\\n  padding-inline: 0.5rem;\\n  padding-top: 0.2rem;\\n  border-bottom: 1px solid rgb(209, 199, 199);\\n}\\n.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 15px;\\n}\\n.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  font-size: small;\\n  margin-right: 0.5rem;\\n}\\n.table-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%]   .header-paragraph[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.table-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  min-width: 250px;\\n}\\n\\n.hidden-menu-trigger[_ngcontent-%COMP%] {\\n  position: fixed;\\n  z-index: 1000;\\n  \\n\\n}\\n\\n.action-column[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-right: 0.2rem;\\n}\\n\\n.mat-mdc-table[_ngcontent-%COMP%]   .mat-mdc-header-cell[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: 500;\\n}\\n\\n.mat-elevation.table[_ngcontent-%COMP%] {\\n  flex-grow: 1;\\n  overflow: auto;\\n  position: relative;\\n}\\n.mat-elevation.table[_ngcontent-%COMP%]   .table-loading-shade[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  z-index: 10;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.mat-elevation.table[_ngcontent-%COMP%]   .table-loading-shade[_ngcontent-%COMP%]   .loading-text[_ngcontent-%COMP%] {\\n  margin-top: 3rem;\\n  font-size: 1rem;\\n  color: rgba(0, 0, 0, 0.7);\\n}\\n.mat-elevation.table[_ngcontent-%COMP%]   table.loading-results[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n}\\n\\n.table-bottom[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n\\n.mat-mdc-row[_ngcontent-%COMP%]   .mat-mdc-cell[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  padding-block: 0.25rem;\\n}\\n\\n.mat-mdc-row[_ngcontent-%COMP%]:hover {\\n  background: whitesmoke;\\n}\\n\\n.no-project-text[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n\\n.lock-icon[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n\\n.isLock[_ngcontent-%COMP%] {\\n  width: 1%;\\n}\\n\\n.name[_ngcontent-%COMP%] {\\n  width: 18%;\\n}\\n\\n.description[_ngcontent-%COMP%] {\\n  width: 29%;\\n}\\n\\n.type[_ngcontent-%COMP%], .productLine[_ngcontent-%COMP%] {\\n  width: 12%;\\n}\\n\\n.owner[_ngcontent-%COMP%] {\\n  width: 16%;\\n}\\n\\n.delete-menu[_ngcontent-%COMP%] {\\n  color: red;\\n}\\n.delete-menu[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%] {\\n  color: inherit;\\n}\\n\\n.gray[_ngcontent-%COMP%] {\\n  background: #fbfbfb;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "effect", "MatPaginator", "MatSort", "MatTableDataSource", "ProjectFilterComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵproperty", "column_r9", "key", "ɵɵtextInterpolate1", "display", "ɵɵelementContainerStart", "ɵɵtemplate", "ProjectTableComponent_ng_container_16_ng_container_1_th_1_Template", "ɵɵelementContainerEnd", "ProjectTableComponent_ng_container_16_ng_template_2_th_0_Template", "element_r25", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_td_1_Template", "ɵɵpipeBind2", "element_r28", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_td_0_Template", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_container_1_Template", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_ng_template_2_Template", "ɵɵtemplateRefExtractor", "isDate", "_r22", "ɵɵelement", "ɵɵpureFunction0", "_c1", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_fa_icon_1_Template", "ctx_r31", "checkProjectLock", "element_r32", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_td_1_Template", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_1_Template", "ProjectTableComponent_ng_container_16_ng_container_4_ng_container_2_Template", "isLock", "ɵɵlistener", "ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_5_listener", "restoredCtx", "ɵɵrestoreView", "_r39", "element_r36", "$implicit", "ctx_r38", "ɵɵnextContext", "ɵɵresetView", "openEditor", "ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_11_listener", "ctx_r40", "onEditProject", "ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_17_listener", "ctx_r41", "onShareProject", "ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template_button_click_23_listener", "ctx_r42", "onDeleteProject", "_r37", "accessType", "ctx_r35", "ProjectTableComponent_ng_container_16_ng_container_5_td_1_Template", "ProjectTableComponent_ng_container_16_ng_container_1_Template", "ProjectTableComponent_ng_container_16_ng_template_2_Template", "ProjectTableComponent_ng_container_16_ng_container_4_Template", "ProjectTableComponent_ng_container_16_ng_container_5_Template", "isAction", "_r11", "ProjectTableComponent_tr_18_Template_tr_dblclick_0_listener", "_r46", "row_r43", "ctx_r45", "ProjectTableComponent_tr_18_Template_tr_contextmenu_0_listener", "$event", "ctx_r47", "onRightClick", "ɵɵpureFunction1", "_c2", "even_r44", "ctx_r5", "isTableLoading", "ProjectTableComponent_ng_container_33_Template_button_click_1_listener", "_r49", "ctx_r48", "contextMenuRow", "ProjectTableComponent_ng_container_33_Template_button_click_7_listener", "ctx_r50", "ProjectTableComponent_ng_container_33_Template_button_click_13_listener", "ctx_r51", "ProjectTableComponent", "loading", "value", "setTimeout", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "searchBarService", "userService", "liveAnnouncer", "noOf<PERSON><PERSON>er", "currentPage", "columnConfig", "displayedColumns", "map", "col", "menuPosition", "x", "y", "openProject", "editProject", "deleteProject", "shareProject", "paginationChanged", "sortChange", "sortConfig", "active", "direction", "dataSource", "totalCount", "pageSize", "filterCount", "ngAfterViewInit", "sort", "event", "row", "preventDefault", "clientX", "clientY", "actionMenuTrigger", "openMenu", "announceSortChange", "sortState", "announce", "emit", "project", "projectFilterComponent", "resetFilters", "loggedInUser", "getUser", "isProjectLocked", "lockingIdContact", "id", "applyFilter", "searchTerm", "search", "trim", "clearFilter", "onPageChange", "pageIndex", "_", "ɵɵdirectiveInject", "i1", "SearchBarService", "i2", "UserService", "i3", "LiveAnnouncer", "ChangeDetectorRef", "_2", "selectors", "viewQuery", "ProjectTableComponent_Query", "rf", "ctx", "ProjectTableComponent_Template_mat_menu_click_8_listener", "stopPropagation", "ProjectTableComponent_Template_app_search_bar_searchChanged_11_listener", "ProjectTableComponent_div_14_Template", "ProjectTableComponent_Template_table_matSortChange_15_listener", "ProjectTableComponent_ng_container_16_Template", "ProjectTableComponent_tr_17_Template", "ProjectTableComponent_tr_18_Template", "ProjectTableComponent_tr_19_Template", "ProjectTableComponent_Template_mat_paginator_page_21_listener", "ProjectTableComponent_Template_button_click_27_listener", "ProjectTableComponent_ng_container_33_Template", "_r0", "_c3", "ɵɵpropertyInterpolate", "ɵɵclassProp", "_c4", "ɵɵstyleProp", "_r7"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\project-table\\project-table.component.ts", "D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\components\\project-table\\project-table.component.html"], "sourcesContent": ["import { LiveAnnouncer } from '@angular/cdk/a11y';\r\nimport {\r\n  AfterViewInit,\r\n  ChangeDetectionStrategy,\r\n  ChangeDetectorRef,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  Output,\r\n  ViewChild,\r\n  effect,\r\n} from '@angular/core';\r\nimport { MatMenuTrigger } from '@angular/material/menu';\r\nimport { MatPaginator, PageEvent } from '@angular/material/paginator';\r\nimport { MatSort, Sort } from '@angular/material/sort';\r\nimport { MatTableDataSource } from '@angular/material/table';\r\nimport { ProjectWithPermission } from 'src/app/shared/model/project';\r\nimport { SearchBarService } from '../../services/search-bar/search-bar.service';\r\nimport { UserService } from '../../services/user/user.service';\r\nimport { ProjectFilterComponent } from '../project-filter/project-filter.component';\r\n\r\ninterface ColumnConfig {\r\n  key: string;\r\n  display: string;\r\n  isAction: boolean;\r\n  isLock?: boolean;\r\n  isDate?: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-project-table',\r\n  templateUrl: './project-table.component.html',\r\n  styleUrls: ['./project-table.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class ProjectTableComponent implements AfterViewInit {\r\n  noOfFilter: number = 0;\r\n  currentPage: number = 0;\r\n  // Track loading state specifically for the table\r\n  isTableLoading: boolean = false;\r\n  // Strongly typed column configuration\r\n  readonly columnConfig: ColumnConfig[] = [\r\n    { key: 'isLock', display: '', isAction: false, isLock: true },\r\n    { key: 'name', display: 'dashboard.table.name', isAction: false },\r\n    {\r\n      key: 'description',\r\n      display: 'dashboard.table.description',\r\n      isAction: false,\r\n    },\r\n    { key: 'type', display: 'dashboard.table.type', isAction: false },\r\n    {\r\n      key: 'productLine',\r\n      display: 'dashboard.table.productLine',\r\n      isAction: false,\r\n    },\r\n    { key: 'admin', display: 'dashboard.table.owner', isAction: false },\r\n    {\r\n      key: 'lastModifiedDate',\r\n      display: 'dashboard.table.lastModified',\r\n      isAction: false,\r\n      isDate: true,\r\n    },\r\n    { key: 'action', display: 'dashboard.table.action', isAction: true },\r\n  ];\r\n\r\n  // Computed display columns\r\n  readonly displayedColumns = this.columnConfig.map((col) => col.key);\r\n  // We're now using the input property for loading state instead of the global loader\r\n\r\n  // View child references\r\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\r\n  @ViewChild(MatSort) sort!: MatSort;\r\n  @ViewChild('actionMenuTrigger', { static: true })\r\n  actionMenuTrigger!: MatMenuTrigger;\r\n  @ViewChild(ProjectFilterComponent)\r\n  projectFilterComponent!: ProjectFilterComponent;\r\n  contextMenuRow!: ProjectWithPermission;\r\n  menuPosition = { x: '0px', y: '0px' };\r\n\r\n  // Output event emitters\r\n  @Output('open') openProject = new EventEmitter<ProjectWithPermission>();\r\n  @Output('edit') editProject = new EventEmitter<ProjectWithPermission>();\r\n  @Output('delete') deleteProject = new EventEmitter<ProjectWithPermission>();\r\n  @Output('share') shareProject = new EventEmitter<ProjectWithPermission>();\r\n  @Output('paginationChanged') paginationChanged = new EventEmitter<{\r\n    pageSize: number;\r\n    currentPage: number;\r\n  }>();\r\n  @Output() sortChange = new EventEmitter<Sort>();\r\n  @Input() sortConfig: Sort = {\r\n    active: 'lastModifiedDate',\r\n    direction: 'desc',\r\n  };\r\n  @Input() dataSource: MatTableDataSource<ProjectWithPermission> =\r\n    new MatTableDataSource<ProjectWithPermission>();\r\n  @Input('totalCount') totalCount: number = 0;\r\n  @Input('pageSize') pageSize = 10;\r\n  @Input('isLoading') set loading(value: boolean) {\r\n    // Use setTimeout to avoid ExpressionChangedAfterItHasBeenCheckedError\r\n    if (this.isTableLoading !== value) {\r\n      setTimeout(() => {\r\n        this.isTableLoading = value;\r\n        this.cdr.markForCheck();\r\n      });\r\n    }\r\n  }\r\n\r\n  constructor(\r\n    private searchBarService: SearchBarService,\r\n    private userService: UserService,\r\n    private liveAnnouncer: LiveAnnouncer,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    effect(() => {\r\n      this.noOfFilter = this.searchBarService.filterCount();\r\n    });\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    // Apply initial sort configuration\r\n    if (this.sort) {\r\n      this.sort.active = this.sortConfig.active;\r\n      this.sort.direction = this.sortConfig.direction;\r\n    }\r\n  }\r\n  onRightClick(event: MouseEvent, row: any): void {\r\n    event.preventDefault();\r\n\r\n    // Store the clicked row\r\n    this.contextMenuRow = row;\r\n\r\n    // Update menu position\r\n    this.menuPosition = {\r\n      x: `${event.clientX}px`,\r\n      y: `${event.clientY - 40}px`,\r\n    };\r\n\r\n    // Open the action menu programmatically\r\n    setTimeout(() => {\r\n      this.actionMenuTrigger.openMenu();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Handles sort change events from the table.\r\n   * Announces the sort change for accessibility and emits the sort event to the parent component.\r\n   * @param sortState Sort event containing active column and direction\r\n   */\r\n  announceSortChange(sortState: Sort): void {\r\n    // Announce sort change for accessibility\r\n    if (sortState.direction) {\r\n      this.liveAnnouncer.announce(`Sorted ${sortState.direction}ending`);\r\n    } else {\r\n      this.liveAnnouncer.announce('Sorting cleared');\r\n    }\r\n\r\n    // Emit sort change event to parent component\r\n    this.sortChange.emit(sortState);\r\n  }\r\n\r\n  // Methods for table actions\r\n  openEditor(project: ProjectWithPermission): void {\r\n    if (this.projectFilterComponent) this.projectFilterComponent.resetFilters();\r\n    debugger;\r\n    this.openProject.emit(project);\r\n  }\r\n\r\n  onEditProject(project: ProjectWithPermission): void {\r\n    this.editProject.emit(project);\r\n  }\r\n\r\n  onDeleteProject(project: ProjectWithPermission): void {\r\n    this.deleteProject.emit(project);\r\n  }\r\n\r\n  onShareProject(project: ProjectWithPermission): void {\r\n    this.shareProject.emit(project);\r\n  }\r\n\r\n  // Improved project lock checking with null safety\r\n  checkProjectLock(project: ProjectWithPermission): boolean {\r\n    // If the user is a Viewer, we don't show the project as locked\r\n    // if (project.accessType === AccessType.Viewer) {\r\n    //   return false;\r\n    // }\r\n\r\n    const loggedInUser = this.userService.getUser();\r\n    return !!(\r\n      loggedInUser &&\r\n      project.isProjectLocked &&\r\n      project.lockingIdContact !== 0 &&\r\n      project.lockingIdContact !== loggedInUser.id\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Applies a search filter by setting the search term in the search bar service.\r\n   * This will trigger server-side filtering through the dashboard component.\r\n   * @param searchTerm The search term to filter by\r\n   */\r\n  applyFilter(searchTerm: string | null) {\r\n    if (searchTerm === null) return;\r\n    // Set the search term in the service to trigger server-side filtering\r\n    this.searchBarService.search(searchTerm.trim());\r\n  }\r\n\r\n  /**\r\n   * Clears the current filter by setting an empty search term.\r\n   */\r\n  clearFilter() {\r\n    this.searchBarService.search('');\r\n  }\r\n  onPageChange(event: PageEvent): void {\r\n    this.currentPage = event.pageIndex + 1;\r\n    this.pageSize = event.pageSize;\r\n    this.paginationChanged.emit({\r\n      pageSize: this.pageSize,\r\n      currentPage: this.currentPage,\r\n    });\r\n  }\r\n}\r\n", "<div class=\"table-container\">\r\n  <div class=\"table-header\">\r\n    <div class=\"header-left\" [matMenuTriggerFor]=\"filterMenu\">\r\n      <fa-icon\r\n        [icon]=\"['fal', 'filter']\"\r\n        size=\"xl\"\r\n        class=\"header-icon\"\r\n        matTooltipClass=\"tooltip-custom\"\r\n        [matTooltip]=\"'dashboard.table.filter' | translate\"\r\n      ></fa-icon>\r\n      <p\r\n        class=\"header-paragraph\"\r\n        [matBadge]=\"noOfFilter\"\r\n        matBadgeOverlap=\"false\"\r\n        [matBadgeHidden]=\"noOfFilter == 0\"\r\n        matBadgeSize=\"small\"\r\n      >\r\n        {{ \"dashboard.table.filter\" | translate }}\r\n      </p>\r\n    </div>\r\n    <mat-menu\r\n      #filterMenu=\"matMenu\"\r\n      [overlapTrigger]=\"false\"\r\n      (click)=\"$event.stopPropagation()\"\r\n    >\r\n      <app-project-filter></app-project-filter>\r\n    </mat-menu>\r\n    <app-search-bar\r\n      class=\"search-container\"\r\n      (searchChanged)=\"applyFilter($event)\"\r\n      placeholder=\"{{ 'dashboard.search.placeholder' | translate }}\"\r\n    ></app-search-bar>\r\n  </div>\r\n  <div class=\"mat-elevation table\">\r\n    <!-- Loading overlay -->\r\n    <div class=\"table-loading-shade\" *ngIf=\"isTableLoading\">\r\n      <span class=\"loading-text\">{{ \"dashboard.loading\" | translate }}</span>\r\n    </div>\r\n\r\n    <table\r\n      mat-table\r\n      [dataSource]=\"dataSource\"\r\n      matSort\r\n      [matSortActive]=\"sortConfig.active\"\r\n      [matSortDirection]=\"sortConfig.direction\"\r\n      (matSortChange)=\"announceSortChange($event)\"\r\n      [class.loading-results]=\"isTableLoading\"\r\n    >\r\n      <ng-container\r\n        *ngFor=\"let column of columnConfig\"\r\n        [matColumnDef]=\"column.key\"\r\n      >\r\n        <ng-container\r\n          *ngIf=\"!column.isLock && !column.isAction; else unsortableHeader\"\r\n        >\r\n          <th\r\n            mat-header-cell\r\n            *matHeaderCellDef\r\n            [ngClass]=\"column.key\"\r\n            mat-sort-header\r\n          >\r\n            {{ column.display | translate }}\r\n          </th>\r\n        </ng-container>\r\n        <ng-template #unsortableHeader>\r\n          <th mat-header-cell *matHeaderCellDef [ngClass]=\"column.key\">\r\n            {{ column.display | translate }}\r\n          </th>\r\n        </ng-template>\r\n\r\n        <ng-container *ngIf=\"!column.isAction\">\r\n          <ng-container *ngIf=\"!column.isLock\">\r\n            <ng-container *ngIf=\"column.isDate; else regularCell\">\r\n              <td mat-cell *matCellDef=\"let element\">\r\n                {{ element[column.key] | timeAgo }}\r\n              </td>\r\n            </ng-container>\r\n            <ng-template #regularCell>\r\n              <td mat-cell *matCellDef=\"let element\">\r\n                {{ element[column.key] | truncate : 120 }}\r\n              </td>\r\n            </ng-template>\r\n          </ng-container>\r\n          <ng-container *ngIf=\"column.isLock\">\r\n            <td mat-cell *matCellDef=\"let element\">\r\n              <fa-icon\r\n                [icon]=\"['fas', 'lock']\"\r\n                size=\"lg\"\r\n                class=\"lock-icon\"\r\n                *ngIf=\"checkProjectLock(element)\"\r\n              ></fa-icon>\r\n            </td>\r\n          </ng-container>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"column.isAction\">\r\n          <td mat-cell *matCellDef=\"let element\">\r\n            <div class=\"action-column\">\r\n              <button\r\n                mat-icon-button\r\n                [matMenuTriggerFor]=\"menu\"\r\n                aria-label=\"More actions\"\r\n                [disabled]=\"\r\n                  !(element.accessType == 0 && !checkProjectLock(element))\r\n                \"\r\n                class=\"action-btn\"\r\n              >\r\n                <mat-icon>more_vert</mat-icon>\r\n              </button>\r\n              <button\r\n                mat-icon-button\r\n                aria-label=\"Open Project\"\r\n                (click)=\"openEditor(element)\"\r\n                class=\"action-btn\"\r\n                [matTooltip]=\"'dashboard.table.open' | translate\"\r\n              >\r\n                <mat-icon>launch</mat-icon>\r\n              </button>\r\n              <mat-menu #menu=\"matMenu\">\r\n                <button mat-menu-item (click)=\"onEditProject(element)\">\r\n                  <mat-icon>edit</mat-icon>\r\n                  <span>{{ \"dashboard.table.edit\" | translate }}</span>\r\n                </button>\r\n                <button mat-menu-item (click)=\"onShareProject(element)\">\r\n                  <mat-icon>share</mat-icon>\r\n                  <span>{{ \"dashboard.table.share\" | translate }}</span>\r\n                </button>\r\n                <button\r\n                  mat-menu-item\r\n                  (click)=\"onDeleteProject(element)\"\r\n                  class=\"delete-menu\"\r\n                >\r\n                  <mat-icon class=\"menu-icon\">delete</mat-icon>\r\n                  <span>{{ \"dashboard.table.delete\" | translate }}</span>\r\n                </button>\r\n              </mat-menu>\r\n            </div>\r\n          </td>\r\n        </ng-container>\r\n      </ng-container>\r\n      <tr\r\n        mat-header-row\r\n        *matHeaderRowDef=\"displayedColumns; sticky: true\"\r\n        class=\"table-header-row\"\r\n      ></tr>\r\n      <tr\r\n        mat-row\r\n        *matRowDef=\"let row; let even = even; columns: displayedColumns\"\r\n        (dblclick)=\"openEditor(row)\"\r\n        (contextmenu)=\"onRightClick($event, row)\"\r\n        class=\"example-element-row\"\r\n        [ngClass]=\"{ gray: even }\"\r\n      ></tr>\r\n      <tr class=\"mat-row\" *matNoDataRow>\r\n        <td class=\"mat-cell no-project-text\" colspan=\"8\">\r\n          {{ isTableLoading ? \"\" : (\"dashboard.noProject\" | translate) }}\r\n        </td>\r\n      </tr>\r\n    </table>\r\n  </div>\r\n  <div class=\"table-bottom\">\r\n    <mat-paginator\r\n      showFirstLastButtons\r\n      [pageSize]=\"pageSize\"\r\n      [length]=\"totalCount\"\r\n      [pageSizeOptions]=\"[10, 15, 20]\"\r\n      [disabled]=\"totalCount! <= 10\"\r\n      aria-label=\"Select page of periodic elements\"\r\n      (page)=\"onPageChange($event)\"\r\n    >\r\n    </mat-paginator>\r\n  </div>\r\n</div>\r\n\r\n<!-- Hidden trigger for the action menu -->\r\n<div\r\n  [style.left]=\"menuPosition.x\"\r\n  [style.top]=\"menuPosition.y\"\r\n  class=\"hidden-menu-trigger\"\r\n>\r\n  <button\r\n    mat-icon-button\r\n    [matMenuTriggerFor]=\"actionMenu\"\r\n    #actionMenuTrigger=\"matMenuTrigger\"\r\n    style=\"visibility: hidden\"\r\n  ></button>\r\n</div>\r\n\r\n<!-- Action Menu -->\r\n<mat-menu #actionMenu=\"matMenu\">\r\n  <button mat-menu-item (click)=\"openEditor(contextMenuRow)\">\r\n    <mat-icon>launch</mat-icon>\r\n    <span>{{ \"dashboard.table.open\" | translate }}</span>\r\n  </button>\r\n  <ng-container\r\n    *ngIf=\"\r\n      contextMenuRow &&\r\n      contextMenuRow.accessType == 0 &&\r\n      !checkProjectLock(contextMenuRow)\r\n    \"\r\n  >\r\n    <button mat-menu-item (click)=\"onEditProject(contextMenuRow)\">\r\n      <mat-icon>edit</mat-icon>\r\n      <span>{{ \"dashboard.table.edit\" | translate }}</span>\r\n    </button>\r\n    <button mat-menu-item (click)=\"onShareProject(contextMenuRow)\">\r\n      <mat-icon>share</mat-icon>\r\n      <span>{{ \"dashboard.table.share\" | translate }}</span>\r\n    </button>\r\n    <button\r\n      mat-menu-item\r\n      (click)=\"onDeleteProject(contextMenuRow)\"\r\n      class=\"delete-menu\"\r\n    >\r\n      <mat-icon class=\"menu-icon\">delete</mat-icon>\r\n      <span>{{ \"dashboard.table.delete\" | translate }}</span>\r\n    </button>\r\n  </ng-container>\r\n</mat-menu>\r\n"], "mappings": "AACA,SAKEA,YAAY,EAIZC,MAAM,QACD,eAAe;AAEtB,SAASC,YAAY,QAAmB,6BAA6B;AACrE,SAASC,OAAO,QAAc,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,yBAAyB;AAI5D,SAASC,sBAAsB,QAAQ,4CAA4C;;;;;;;;;;;;;;;;;;;;;;;ICgB/EC,EAAA,CAAAC,cAAA,cAAwD;IAC3BD,EAAA,CAAAE,MAAA,GAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;IAA5CH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,4BAAqC;;;;;IAmB5DN,EAAA,CAAAC,cAAA,aAKC;IACCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAJHH,EAAA,CAAAO,UAAA,YAAAC,SAAA,CAAAC,GAAA,CAAsB;IAGtBT,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAM,WAAA,OAAAE,SAAA,CAAAG,OAAA,OACF;;;;;IAVFX,EAAA,CAAAY,uBAAA,GAEC;IACCZ,EAAA,CAAAa,UAAA,IAAAC,kEAAA,iBAOK;IACPd,EAAA,CAAAe,qBAAA,EAAe;;;;;IAEbf,EAAA,CAAAC,cAAA,aAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAFiCH,EAAA,CAAAO,UAAA,YAAAC,SAAA,CAAAC,GAAA,CAAsB;IAC1DT,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAM,WAAA,OAAAE,SAAA,CAAAG,OAAA,OACF;;;;;IAFAX,EAAA,CAAAa,UAAA,IAAAG,iEAAA,iBAEK;;;;;IAMDhB,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAM,WAAA,OAAAW,WAAA,CAAAT,SAAA,CAAAC,GAAA,QACF;;;;;IAHFT,EAAA,CAAAY,uBAAA,GAAsD;IACpDZ,EAAA,CAAAa,UAAA,IAAAK,gGAAA,iBAEK;IACPlB,EAAA,CAAAe,qBAAA,EAAe;;;;;IAEbf,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAmB,WAAA,OAAAC,WAAA,CAAAZ,SAAA,CAAAC,GAAA,aACF;;;;;IAFAT,EAAA,CAAAa,UAAA,IAAAQ,+FAAA,iBAEK;;;;;IATTrB,EAAA,CAAAY,uBAAA,GAAqC;IACnCZ,EAAA,CAAAa,UAAA,IAAAS,2FAAA,2BAIe;IACftB,EAAA,CAAAa,UAAA,IAAAU,0FAAA,iCAAAvB,EAAA,CAAAwB,sBAAA,CAIc;IAChBxB,EAAA,CAAAe,qBAAA,EAAe;;;;;IAVEf,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAO,UAAA,SAAAC,SAAA,CAAAiB,MAAA,CAAqB,aAAAC,IAAA;;;;;;;;IAalC1B,EAAA,CAAA2B,SAAA,kBAKW;;;IAJT3B,EAAA,CAAAO,UAAA,SAAAP,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAwB;;;;;IAF5B7B,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAa,UAAA,IAAAiB,2FAAA,sBAKW;IACb9B,EAAA,CAAAG,YAAA,EAAK;;;;;IAFAH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAO,UAAA,SAAAwB,OAAA,CAAAC,gBAAA,CAAAC,WAAA,EAA+B;;;;;IANtCjC,EAAA,CAAAY,uBAAA,GAAoC;IAClCZ,EAAA,CAAAa,UAAA,IAAAqB,iFAAA,iBAOK;IACPlC,EAAA,CAAAe,qBAAA,EAAe;;;;;IAtBjBf,EAAA,CAAAY,uBAAA,GAAuC;IACrCZ,EAAA,CAAAa,UAAA,IAAAsB,4EAAA,2BAWe;IACfnC,EAAA,CAAAa,UAAA,IAAAuB,4EAAA,2BASe;IACjBpC,EAAA,CAAAe,qBAAA,EAAe;;;;IAtBEf,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAO,UAAA,UAAAC,SAAA,CAAA6B,MAAA,CAAoB;IAYpBrC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAO,UAAA,SAAAC,SAAA,CAAA6B,MAAA,CAAmB;;;;;;IAYlCrC,EAAA,CAAAC,cAAA,aAAuC;IAWvBD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EAAA,CAAAC,cAAA,iBAMC;IAHCD,EAAA,CAAAsC,UAAA,mBAAAC,2FAAA;MAAA,MAAAC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA7C,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAL,WAAA,CAAmB;IAAA,EAAC;;IAI7B3C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EAAA,CAAAC,cAAA,yBAA0B;IACFD,EAAA,CAAAsC,UAAA,mBAAAW,4FAAA;MAAA,MAAAT,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAAlD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAR,WAAA,CAAsB;IAAA,EAAC;IACpD3C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,kBAAwD;IAAlCD,EAAA,CAAAsC,UAAA,mBAAAc,4FAAA;MAAA,MAAAZ,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAS,OAAA,GAAArD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAM,OAAA,CAAAC,cAAA,CAAAX,WAAA,CAAuB;IAAA,EAAC;IACrD3C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExDH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAsC,UAAA,mBAAAiB,4FAAA;MAAA,MAAAf,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAY,OAAA,GAAAxD,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAS,OAAA,CAAAC,eAAA,CAAAd,WAAA,CAAwB;IAAA,EAAC;IAGlC3C,EAAA,CAAAC,cAAA,oBAA4B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAjCzDH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAO,UAAA,sBAAAmD,IAAA,CAA0B,eAAAf,WAAA,CAAAgB,UAAA,UAAAC,OAAA,CAAA5B,gBAAA,CAAAW,WAAA;IAc1B3C,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAO,UAAA,eAAAP,EAAA,CAAAM,WAAA,+BAAiD;IAOzCN,EAAA,CAAAI,SAAA,IAAwC;IAAxCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,gCAAwC;IAIxCN,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,kCAAyC;IAQzCN,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,mCAA0C;;;;;IAtC1DN,EAAA,CAAAY,uBAAA,GAAsC;IACpCZ,EAAA,CAAAa,UAAA,IAAAgD,kEAAA,mBAyCK;IACP7D,EAAA,CAAAe,qBAAA,EAAe;;;;;IAzFjBf,EAAA,CAAAY,uBAAA,OAGC;IACCZ,EAAA,CAAAa,UAAA,IAAAiD,6DAAA,2BAWe;IACf9D,EAAA,CAAAa,UAAA,IAAAkD,4DAAA,iCAAA/D,EAAA,CAAAwB,sBAAA,CAIc;IAEdxB,EAAA,CAAAa,UAAA,IAAAmD,6DAAA,2BAuBe;IACfhE,EAAA,CAAAa,UAAA,IAAAoD,6DAAA,2BA2Ce;IACjBjE,EAAA,CAAAe,qBAAA,EAAe;;;;;IAxFbf,EAAA,CAAAO,UAAA,iBAAAC,SAAA,CAAAC,GAAA,CAA2B;IAGxBT,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAO,UAAA,UAAAC,SAAA,CAAA6B,MAAA,KAAA7B,SAAA,CAAA0D,QAAA,CAA0C,aAAAC,IAAA;IAiB9BnE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAO,UAAA,UAAAC,SAAA,CAAA0D,QAAA,CAAsB;IAwBtBlE,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAO,UAAA,SAAAC,SAAA,CAAA0D,QAAA,CAAqB;;;;;IA6CtClE,EAAA,CAAA2B,SAAA,aAIM;;;;;;;;;;;IACN3B,EAAA,CAAAC,cAAA,aAOC;IAJCD,EAAA,CAAAsC,UAAA,sBAAA8B,4DAAA;MAAA,MAAA5B,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAA9B,WAAA,CAAAI,SAAA;MAAA,MAAA2B,OAAA,GAAAvE,EAAA,CAAA8C,aAAA;MAAA,OAAY9C,EAAA,CAAA+C,WAAA,CAAAwB,OAAA,CAAAvB,UAAA,CAAAsB,OAAA,CAAe;IAAA,EAAC,yBAAAE,+DAAAC,MAAA;MAAA,MAAAjC,WAAA,GAAAxC,EAAA,CAAAyC,aAAA,CAAA4B,IAAA;MAAA,MAAAC,OAAA,GAAA9B,WAAA,CAAAI,SAAA;MAAA,MAAA8B,OAAA,GAAA1E,EAAA,CAAA8C,aAAA;MAAA,OACb9C,EAAA,CAAA+C,WAAA,CAAA2B,OAAA,CAAAC,YAAA,CAAAF,MAAA,EAAAH,OAAA,CAAyB;IAAA,EADZ;IAI7BtE,EAAA,CAAAG,YAAA,EAAK;;;;IADJH,EAAA,CAAAO,UAAA,YAAAP,EAAA,CAAA4E,eAAA,IAAAC,GAAA,EAAAC,QAAA,EAA0B;;;;;IAE5B9E,EAAA,CAAAC,cAAA,aAAkC;IAE9BD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IADHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAqE,MAAA,CAAAC,cAAA,QAAAhF,EAAA,CAAAM,WAAA,mCACF;;;;;;IAsCNN,EAAA,CAAAY,uBAAA,GAMC;IACCZ,EAAA,CAAAC,cAAA,iBAA8D;IAAxCD,EAAA,CAAAsC,UAAA,mBAAA2C,uEAAA;MAAAjF,EAAA,CAAAyC,aAAA,CAAAyC,IAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAoC,OAAA,CAAAhC,aAAA,CAAAgC,OAAA,CAAAC,cAAA,CAA6B;IAAA,EAAC;IAC3DpF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvDH,EAAA,CAAAC,cAAA,iBAA+D;IAAzCD,EAAA,CAAAsC,UAAA,mBAAA+C,uEAAA;MAAArF,EAAA,CAAAyC,aAAA,CAAAyC,IAAA;MAAA,MAAAI,OAAA,GAAAtF,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAuC,OAAA,CAAAhC,cAAA,CAAAgC,OAAA,CAAAF,cAAA,CAA8B;IAAA,EAAC;IAC5DpF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExDH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAsC,UAAA,mBAAAiD,wEAAA;MAAAvF,EAAA,CAAAyC,aAAA,CAAAyC,IAAA;MAAA,MAAAM,OAAA,GAAAxF,EAAA,CAAA8C,aAAA;MAAA,OAAS9C,EAAA,CAAA+C,WAAA,CAAAyC,OAAA,CAAA/B,eAAA,CAAA+B,OAAA,CAAAJ,cAAA,CAA+B;IAAA,EAAC;IAGzCpF,EAAA,CAAAC,cAAA,oBAA4B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7CH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAe,qBAAA,EAAe;;;IAdLf,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,+BAAwC;IAIxCN,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,iCAAyC;IAQzCN,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,kCAA0C;;;;;;;;;ADnLtD,OAAM,MAAOmF,qBAAqB;EA8DhC,IAAwBC,OAAOA,CAACC,KAAc;IAC5C;IACA,IAAI,IAAI,CAACX,cAAc,KAAKW,KAAK,EAAE;MACjCC,UAAU,CAAC,MAAK;QACd,IAAI,CAACZ,cAAc,GAAGW,KAAK;QAC3B,IAAI,CAACE,GAAG,CAACC,YAAY,EAAE;MACzB,CAAC,CAAC;;EAEN;EAEAC,YACUC,gBAAkC,EAClCC,WAAwB,EACxBC,aAA4B,EAC5BL,GAAsB;IAHtB,KAAAG,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAL,GAAG,GAAHA,GAAG;IA3Eb,KAAAM,UAAU,GAAW,CAAC;IACtB,KAAAC,WAAW,GAAW,CAAC;IACvB;IACA,KAAApB,cAAc,GAAY,KAAK;IAC/B;IACS,KAAAqB,YAAY,GAAmB,CACtC;MAAE5F,GAAG,EAAE,QAAQ;MAAEE,OAAO,EAAE,EAAE;MAAEuD,QAAQ,EAAE,KAAK;MAAE7B,MAAM,EAAE;IAAI,CAAE,EAC7D;MAAE5B,GAAG,EAAE,MAAM;MAAEE,OAAO,EAAE,sBAAsB;MAAEuD,QAAQ,EAAE;IAAK,CAAE,EACjE;MACEzD,GAAG,EAAE,aAAa;MAClBE,OAAO,EAAE,6BAA6B;MACtCuD,QAAQ,EAAE;KACX,EACD;MAAEzD,GAAG,EAAE,MAAM;MAAEE,OAAO,EAAE,sBAAsB;MAAEuD,QAAQ,EAAE;IAAK,CAAE,EACjE;MACEzD,GAAG,EAAE,aAAa;MAClBE,OAAO,EAAE,6BAA6B;MACtCuD,QAAQ,EAAE;KACX,EACD;MAAEzD,GAAG,EAAE,OAAO;MAAEE,OAAO,EAAE,uBAAuB;MAAEuD,QAAQ,EAAE;IAAK,CAAE,EACnE;MACEzD,GAAG,EAAE,kBAAkB;MACvBE,OAAO,EAAE,8BAA8B;MACvCuD,QAAQ,EAAE,KAAK;MACfzC,MAAM,EAAE;KACT,EACD;MAAEhB,GAAG,EAAE,QAAQ;MAAEE,OAAO,EAAE,wBAAwB;MAAEuD,QAAQ,EAAE;IAAI,CAAE,CACrE;IAED;IACS,KAAAoC,gBAAgB,GAAG,IAAI,CAACD,YAAY,CAACE,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAC/F,GAAG,CAAC;IAWnE,KAAAgG,YAAY,GAAG;MAAEC,CAAC,EAAE,KAAK;MAAEC,CAAC,EAAE;IAAK,CAAE;IAErC;IACgB,KAAAC,WAAW,GAAG,IAAIlH,YAAY,EAAyB;IACvD,KAAAmH,WAAW,GAAG,IAAInH,YAAY,EAAyB;IACrD,KAAAoH,aAAa,GAAG,IAAIpH,YAAY,EAAyB;IAC1D,KAAAqH,YAAY,GAAG,IAAIrH,YAAY,EAAyB;IAC5C,KAAAsH,iBAAiB,GAAG,IAAItH,YAAY,EAG7D;IACM,KAAAuH,UAAU,GAAG,IAAIvH,YAAY,EAAQ;IACtC,KAAAwH,UAAU,GAAS;MAC1BC,MAAM,EAAE,kBAAkB;MAC1BC,SAAS,EAAE;KACZ;IACQ,KAAAC,UAAU,GACjB,IAAIvH,kBAAkB,EAAyB;IAC5B,KAAAwH,UAAU,GAAW,CAAC;IACxB,KAAAC,QAAQ,GAAG,EAAE;IAiB9B5H,MAAM,CAAC,MAAK;MACV,IAAI,CAACwG,UAAU,GAAG,IAAI,CAACH,gBAAgB,CAACwB,WAAW,EAAE;IACvD,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACC,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACP,MAAM,GAAG,IAAI,CAACD,UAAU,CAACC,MAAM;MACzC,IAAI,CAACO,IAAI,CAACN,SAAS,GAAG,IAAI,CAACF,UAAU,CAACE,SAAS;;EAEnD;EACAzC,YAAYA,CAACgD,KAAiB,EAAEC,GAAQ;IACtCD,KAAK,CAACE,cAAc,EAAE;IAEtB;IACA,IAAI,CAACzC,cAAc,GAAGwC,GAAG;IAEzB;IACA,IAAI,CAACnB,YAAY,GAAG;MAClBC,CAAC,EAAE,GAAGiB,KAAK,CAACG,OAAO,IAAI;MACvBnB,CAAC,EAAE,GAAGgB,KAAK,CAACI,OAAO,GAAG,EAAE;KACzB;IAED;IACAnC,UAAU,CAAC,MAAK;MACd,IAAI,CAACoC,iBAAiB,CAACC,QAAQ,EAAE;IACnC,CAAC,CAAC;EACJ;EAEA;;;;;EAKAC,kBAAkBA,CAACC,SAAe;IAChC;IACA,IAAIA,SAAS,CAACf,SAAS,EAAE;MACvB,IAAI,CAAClB,aAAa,CAACkC,QAAQ,CAAC,UAAUD,SAAS,CAACf,SAAS,QAAQ,CAAC;KACnE,MAAM;MACL,IAAI,CAAClB,aAAa,CAACkC,QAAQ,CAAC,iBAAiB,CAAC;;IAGhD;IACA,IAAI,CAACnB,UAAU,CAACoB,IAAI,CAACF,SAAS,CAAC;EACjC;EAEA;EACAnF,UAAUA,CAACsF,OAA8B;IACvC,IAAI,IAAI,CAACC,sBAAsB,EAAE,IAAI,CAACA,sBAAsB,CAACC,YAAY,EAAE;IAC3E;IACA,IAAI,CAAC5B,WAAW,CAACyB,IAAI,CAACC,OAAO,CAAC;EAChC;EAEAnF,aAAaA,CAACmF,OAA8B;IAC1C,IAAI,CAACzB,WAAW,CAACwB,IAAI,CAACC,OAAO,CAAC;EAChC;EAEA7E,eAAeA,CAAC6E,OAA8B;IAC5C,IAAI,CAACxB,aAAa,CAACuB,IAAI,CAACC,OAAO,CAAC;EAClC;EAEAhF,cAAcA,CAACgF,OAA8B;IAC3C,IAAI,CAACvB,YAAY,CAACsB,IAAI,CAACC,OAAO,CAAC;EACjC;EAEA;EACAtG,gBAAgBA,CAACsG,OAA8B;IAC7C;IACA;IACA;IACA;IAEA,MAAMG,YAAY,GAAG,IAAI,CAACxC,WAAW,CAACyC,OAAO,EAAE;IAC/C,OAAO,CAAC,EACND,YAAY,IACZH,OAAO,CAACK,eAAe,IACvBL,OAAO,CAACM,gBAAgB,KAAK,CAAC,IAC9BN,OAAO,CAACM,gBAAgB,KAAKH,YAAY,CAACI,EAAE,CAC7C;EACH;EAEA;;;;;EAKAC,WAAWA,CAACC,UAAyB;IACnC,IAAIA,UAAU,KAAK,IAAI,EAAE;IACzB;IACA,IAAI,CAAC/C,gBAAgB,CAACgD,MAAM,CAACD,UAAU,CAACE,IAAI,EAAE,CAAC;EACjD;EAEA;;;EAGAC,WAAWA,CAAA;IACT,IAAI,CAAClD,gBAAgB,CAACgD,MAAM,CAAC,EAAE,CAAC;EAClC;EACAG,YAAYA,CAACxB,KAAgB;IAC3B,IAAI,CAACvB,WAAW,GAAGuB,KAAK,CAACyB,SAAS,GAAG,CAAC;IACtC,IAAI,CAAC7B,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;IAC9B,IAAI,CAACP,iBAAiB,CAACqB,IAAI,CAAC;MAC1Bd,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBnB,WAAW,EAAE,IAAI,CAACA;KACnB,CAAC;EACJ;EAAC,QAAAiD,CAAA,G;qBAxLU5D,qBAAqB,EAAAzF,EAAA,CAAAsJ,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAxJ,EAAA,CAAAsJ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1J,EAAA,CAAAsJ,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA5J,EAAA,CAAAsJ,iBAAA,CAAAtJ,EAAA,CAAA6J,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBrE,qBAAqB;IAAAsE,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAmCrBtK,YAAY;uBACZC,OAAO;;uBAGPE,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QC1EnCC,EAAA,CAAAC,cAAA,aAA6B;QAGvBD,EAAA,CAAA2B,SAAA,iBAMW;;QACX3B,EAAA,CAAAC,cAAA,WAMC;QACCD,EAAA,CAAAE,MAAA,GACF;;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAENH,EAAA,CAAAC,cAAA,qBAIC;QADCD,EAAA,CAAAsC,UAAA,mBAAA8H,yDAAA3F,MAAA;UAAA,OAASA,MAAA,CAAA4F,eAAA,EAAwB;QAAA,EAAC;QAElCrK,EAAA,CAAA2B,SAAA,0BAAyC;QAC3C3B,EAAA,CAAAG,YAAA,EAAW;QACXH,EAAA,CAAAC,cAAA,yBAIC;QAFCD,EAAA,CAAAsC,UAAA,2BAAAgI,wEAAA7F,MAAA;UAAA,OAAiB0F,GAAA,CAAArB,WAAA,CAAArE,MAAA,CAAmB;QAAA,EAAC;;QAEtCzE,EAAA,CAAAG,YAAA,EAAiB;QAEpBH,EAAA,CAAAC,cAAA,cAAiC;QAE/BD,EAAA,CAAAa,UAAA,KAAA0J,qCAAA,iBAEM;QAENvK,EAAA,CAAAC,cAAA,iBAQC;QAFCD,EAAA,CAAAsC,UAAA,2BAAAkI,+DAAA/F,MAAA;UAAA,OAAiB0F,GAAA,CAAAjC,kBAAA,CAAAzD,MAAA,CAA0B;QAAA,EAAC;QAG5CzE,EAAA,CAAAa,UAAA,KAAA4J,8CAAA,2BA0Fe;QACfzK,EAAA,CAAAa,UAAA,KAAA6J,oCAAA,iBAIM;QACN1K,EAAA,CAAAa,UAAA,KAAA8J,oCAAA,iBAOM;QACN3K,EAAA,CAAAa,UAAA,KAAA+J,oCAAA,iBAIK;QACP5K,EAAA,CAAAG,YAAA,EAAQ;QAEVH,EAAA,CAAAC,cAAA,eAA0B;QAQtBD,EAAA,CAAAsC,UAAA,kBAAAuI,8DAAApG,MAAA;UAAA,OAAQ0F,GAAA,CAAAhB,YAAA,CAAA1E,MAAA,CAAoB;QAAA,EAAC;QAE/BzE,EAAA,CAAAG,YAAA,EAAgB;QAKpBH,EAAA,CAAAC,cAAA,eAIC;QACCD,EAAA,CAAA2B,SAAA,sBAKU;QACZ3B,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAC,cAAA,0BAAgC;QACRD,EAAA,CAAAsC,UAAA,mBAAAwI,wDAAA;UAAA,OAASX,GAAA,CAAAnH,UAAA,CAAAmH,GAAA,CAAA/E,cAAA,CAA0B;QAAA,EAAC;QACxDpF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAC,cAAA,YAAM;QAAAD,EAAA,CAAAE,MAAA,IAAwC;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAEvDH,EAAA,CAAAa,UAAA,KAAAkK,8CAAA,4BAuBe;QACjB/K,EAAA,CAAAG,YAAA,EAAW;;;;;QAvNkBH,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAO,UAAA,sBAAAyK,GAAA,CAAgC;QAErDhL,EAAA,CAAAI,SAAA,GAA0B;QAA1BJ,EAAA,CAAAO,UAAA,SAAAP,EAAA,CAAA4B,eAAA,KAAAqJ,GAAA,EAA0B,eAAAjL,EAAA,CAAAM,WAAA;QAQ1BN,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAO,UAAA,aAAA4J,GAAA,CAAAhE,UAAA,CAAuB,mBAAAgE,GAAA,CAAAhE,UAAA;QAKvBnG,EAAA,CAAAI,SAAA,GACF;QADEJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAM,WAAA,uCACF;QAIAN,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAO,UAAA,yBAAwB;QAQxBP,EAAA,CAAAI,SAAA,GAA8D;QAA9DJ,EAAA,CAAAkL,qBAAA,gBAAAlL,EAAA,CAAAM,WAAA,yCAA8D;QAK9BN,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAO,UAAA,SAAA4J,GAAA,CAAAnF,cAAA,CAAoB;QAWpDhF,EAAA,CAAAI,SAAA,GAAwC;QAAxCJ,EAAA,CAAAmL,WAAA,oBAAAhB,GAAA,CAAAnF,cAAA,CAAwC;QALxChF,EAAA,CAAAO,UAAA,eAAA4J,GAAA,CAAA9C,UAAA,CAAyB,kBAAA8C,GAAA,CAAAjD,UAAA,CAAAC,MAAA,sBAAAgD,GAAA,CAAAjD,UAAA,CAAAE,SAAA;QAQJpH,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAO,UAAA,YAAA4J,GAAA,CAAA9D,YAAA,CAAe;QA4FjCrG,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAO,UAAA,oBAAA4J,GAAA,CAAA7D,gBAAA,CAAmC;QAKEtG,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAO,UAAA,qBAAA4J,GAAA,CAAA7D,gBAAA,CAAyB;QAgBjEtG,EAAA,CAAAI,SAAA,GAAqB;QAArBJ,EAAA,CAAAO,UAAA,aAAA4J,GAAA,CAAA5C,QAAA,CAAqB,WAAA4C,GAAA,CAAA7C,UAAA,qBAAAtH,EAAA,CAAA4B,eAAA,KAAAwJ,GAAA,eAAAjB,GAAA,CAAA7C,UAAA;QAazBtH,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAAqL,WAAA,SAAAlB,GAAA,CAAA1D,YAAA,CAAAC,CAAA,CAA6B,QAAAyD,GAAA,CAAA1D,YAAA,CAAAE,CAAA;QAM3B3G,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAO,UAAA,sBAAA+K,GAAA,CAAgC;QAU1BtL,EAAA,CAAAI,SAAA,GAAwC;QAAxCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,iCAAwC;QAG7CN,EAAA,CAAAI,SAAA,GAIF;QAJEJ,EAAA,CAAAO,UAAA,SAAA4J,GAAA,CAAA/E,cAAA,IAAA+E,GAAA,CAAA/E,cAAA,CAAAzB,UAAA,UAAAwG,GAAA,CAAAnI,gBAAA,CAAAmI,GAAA,CAAA/E,cAAA,EAIF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}