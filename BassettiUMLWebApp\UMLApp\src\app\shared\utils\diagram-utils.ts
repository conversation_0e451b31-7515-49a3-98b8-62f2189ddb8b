import { Injectable } from '@angular/core';
import go from 'gojs';
import { BehaviorSubject, Observable } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';
import { AttributeOption, AttributeType } from '../model/attribute';
import { DeletedLink } from '../model/cardinality';
import { Diagram } from '../model/diagram';
import { GojsDiagramAttributeNode, GojsNodeCategory } from '../model/gojs';
import { SharedService } from '../services/shared.service';

/**
 * Service for managing and manipulating diagrams, links, and attribute types in a GoJS-based diagram editor.
 * It provides utility methods for managing diagram-related data, such as nodes, links, and attribute types,
 * as well as handling active diagrams and their associated details.
 */
@Injectable({
  providedIn: 'root', // This makes the service available globally in the Angular app
})
export class DiagramUtils {
  // Private variables for tracking diagram data and attributes
  private _deletedLinks: DeletedLink[] = [];
  private _currentProjectDiagramsSubject = new BehaviorSubject<Diagram[]>([]);
  private _activeDiagramSubject = new BehaviorSubject<Diagram | null>(null);
  private _attributeTypes = new BehaviorSubject<AttributeOption[]>([]);
  private _activeDiagramDetailsSubject = new BehaviorSubject<any | null>(null);
  constructor(private shared: SharedService) {
    this._attributeTypes.next(
      Object.keys(AttributeType)
        .filter(
          (key) =>
            isNaN(Number(key)) &&
            AttributeType[key as keyof typeof AttributeType] !==
              AttributeType.Enumeration
        )
        .map((key) => {
          return {
            id: `${AttributeType[key as keyof typeof AttributeType]}_${key}`,
            name: key,
            isEnumeration: false,
          } as AttributeOption;
        })
    );
  }

  /**
   * Adds a deleted link to the internal collection.
   *
   * This method stores a deleted link by appending it to the `_deletedLinks` array.
   *
   * @param link - The `DeletedLink` object representing the deleted link.
   */
  addDeletedLink(link: DeletedLink): void {
    this._deletedLinks.push(link);
  }

  /**
   * Removes a deleted link from the internal collection by its ID.
   *
   * This method filters out the deleted link matching the specified `idDeletedLink`
   * from the `_deletedLinks` array.
   *
   * @param idDeletedLink - The unique identifier of the deleted link to be removed.
   */
  removeDeletedLink(idDeletedLink: number): void {
    this._deletedLinks = this._deletedLinks.filter(
      (link) => link.id != idDeletedLink
    );
  }

  /**
   * Retrieves the list of deleted links.
   *
   * This method returns all the deleted links stored in the `_deletedLinks` array.
   *
   * @returns An array of `DeletedLink` objects representing the deleted links.
   */
  getDeletedLinks(): DeletedLink[] {
    return this._deletedLinks;
  }

  /**
   * Sets the list of diagrams associated with the current project.
   *
   * This method emits the provided `diagrams` to the `_currentProjectDiagramsSubject`.
   *
   * @param diagrams - An array of `Diagram` objects representing the current project diagrams.
   */
  setCurrentProjectDiagrams(diagrams: Diagram[]): void {
    this._currentProjectDiagramsSubject.next(diagrams);
  }

  /**
   * Sets the active diagram for the current project.
   *
   * This method emits the specified `diagram` to the `_activeDiagramSubject`.
   *
   * @param diagram - The `Diagram` object to be set as the active diagram.
   */
  setActiveDiagram(diagram: Diagram | null): void {
    this._activeDiagramSubject.next(diagram);
  }

  /**
   * Subscribes to changes in the current project diagrams.
   *
   * This method provides an observable stream of `Diagram[]` values, allowing consumers
   * to react to updates in the current project diagrams.
   *
   * @returns An `Observable` emitting the latest list of `Diagram` objects.
   */
  currentProjectDiagramsChanges(): Observable<Diagram[]> {
    return this._currentProjectDiagramsSubject.asObservable();
  }

  /**
   * Subscribes to changes in the active diagram.
   *
   * This method provides an observable stream of `Diagram` values, allowing consumers
   * to react to updates in the active diagram.
   *
   * @returns An `Observable` emitting the current active `Diagram` or `null` if none is set.
   */
  activeDiagramChanges(): Observable<Diagram | null> {
    return this._activeDiagramSubject.asObservable().pipe(
      distinctUntilChanged((prev: Diagram | null, curr: Diagram | null) => {
        // Compare diagrams by ID to prevent duplicate emissions
        return prev?.id === curr?.id;
      })
    );
  }

  /**
   * Sets the details for the active diagram.
   *
   * This method emits the specified `diagram` details to the `_activeDiagramDetailsSubject`.
   *
   * @param diagram - An object containing details for the active diagram.
   */
  setActiveDiagramDetails(diagram: any): void {
    this._activeDiagramDetailsSubject.next(diagram);
  }

  /**
   * Subscribes to changes in the active diagram details.
   *
   * This method provides an observable stream of values, allowing consumers to
   * react to updates in the active diagram details.
   *
   * @returns An `Observable` emitting the current active diagram details or `null` if none are set.
   */
  activeDiagramDetailsChanges(): Observable<any | null> {
    return this._activeDiagramDetailsSubject.asObservable();
  }

  /**
   * Retrieves the node details from a diagram or palette by its key.
   * @param diagram The active diagram or palette.
   * @param key The key used to find the node in the model.
   * @returns The node data or null if not found.
   */
  getObjectDataByKey(
    diagram: go.Diagram | go.Palette,
    key: any
  ): go.ObjectData | null {
    return diagram.model.findNodeDataForKey(key);
  }

  /**
   * Finds a node by its template class ID.
   * @param idTemplateClass The ID of the template class to search for.
   * @returns The found node data, or undefined if not found.
   */
  findNodeByIdTemplateClass(
    diagram: go.Diagram,
    idTemplateClass: number
  ): go.ObjectData | undefined {
    return diagram.model.nodeDataArray.find(
      (nodeData) => nodeData['idTemplateClass'] === idTemplateClass
    );
  }

  // Methods for managing attribute types
  getAttributeTypes(): Observable<AttributeOption[]> {
    return this._attributeTypes.asObservable();
  }

  getAttributeTypeById(idAttributeType: string) {
    const attributeTypes = this._attributeTypes.getValue();
    return attributeTypes.find((attr) => attr.id === idAttributeType);
  }

  updateAttributeType(idAttributeType: string, updatedName: string): void {
    const attributeTypes = this._attributeTypes.getValue();
    const attributeType = attributeTypes.find(
      (attr) => attr.id === idAttributeType
    );
    if (attributeType) {
      attributeType.name = updatedName;
      this._attributeTypes.next(attributeTypes);
    }
  }

  /**
   * Adds new options to the attribute types list.
   * @param option The attribute option or an array of options to add.
   */
  addAttributeTypes(option: AttributeOption | AttributeOption[]): void {
    const attributeTypes = this._attributeTypes.getValue();
    if (Array.isArray(option)) {
      this._attributeTypes.next([...attributeTypes, ...option]);
    } else {
      this._attributeTypes.next([...attributeTypes, option]);
    }
  }

  /**
   * Removes an attribute option by its ID.
   * @param optionId The ID of the option to remove.
   */
  removeAttributeType(optionId: string): void {
    const attributeTypes = this._attributeTypes
      .getValue()
      .filter((option) => option.id !== optionId);
    this._attributeTypes.next(attributeTypes);
  }

  /**
   * Clears all enumeration type options.
   */
  clearEnumTypeOptions(): void {
    const attributeTypes = this._attributeTypes
      .getValue()
      .filter((o) => !o.isEnumeration);
    this._attributeTypes.next(attributeTypes);
  }

  /**
   * Updates an enumeration type by modifying its name and updating relevant nodes in the diagram.
   * @param idTempEnumeration The ID of the template enumeration.
   * @param modifiedName The new name for the enumeration.
   * @param goJsDiagram The active GoJS diagram to update.
   */
  updateEnumerationType(
    idTempEnumeration: string,
    modifiedName: string,
    goJsDiagram: go.Diagram
  ): void {
    this.updateAttributeType(idTempEnumeration, modifiedName);

    goJsDiagram.model.nodeDataArray.forEach((nodeData) => {
      if (
        nodeData &&
        nodeData['items'] &&
        (nodeData['category'] == GojsNodeCategory.Class ||
          nodeData['category'] == GojsNodeCategory.AssociativeClass)
      ) {
        nodeData['items'].forEach((attr: GojsDiagramAttributeNode) => {
          if (attr.idTemplateEnumeration == +idTempEnumeration) {
            attr.dataType = idTempEnumeration;
            attr.idTemplateEnumeration = +idTempEnumeration;
          }
        });
        goJsDiagram.model.updateTargetBindings(nodeData);
      }
    });
  }

  /**
   * Initializes a GoJS diagram's model with optional node and link data.
   * @param diagram The GoJS diagram to initialize.
   * @param nodeDataArray An optional array of node data.
   * @param linkDataArray An optional array of link data.
   */
  initializeDiagramModelData(
    diagram: go.Diagram,
    nodeDataArray?: Array<go.ObjectData>,
    linkDataArray?: Array<go.ObjectData>
  ): void {
    diagram.model = new go.GraphLinksModel(nodeDataArray, linkDataArray);
    (diagram.model as go.GraphLinksModel).copiesArrays = true;
    (diagram.model as go.GraphLinksModel).copiesArrayObjects = true;
    (diagram.model as go.GraphLinksModel).linkFromPortIdProperty = 'fromPort';
    (diagram.model as go.GraphLinksModel).linkToPortIdProperty = 'toPort';
    (diagram.model as go.GraphLinksModel).linkLabelKeysProperty = 'labelKeys';
    (diagram.model as go.GraphLinksModel).linkKeyProperty = 'key';
    (diagram.model as go.GraphLinksModel).nodeKeyProperty = 'key';

    // Assign the makeUniqueKeyFunction for node data
    diagram.model.makeUniqueKeyFunction = (model, nodeData) => {
      return this.shared.generateUniqueKey();
    };

    // Assign the makeUniqueLinkKeyFunction
    (diagram.model as go.GraphLinksModel).makeUniqueLinkKeyFunction = (
      model,
      linkData
    ) => {
      return this.shared.generateUniqueKey();
    };
  }

  base64ToBlob(base64: string, mimeType: string = 'image/jpeg'): Blob {
    const byteCharacters = atob(base64.split(',')[1]);
    const byteNumbers = Array.from(byteCharacters, (char) =>
      char.charCodeAt(0)
    );
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }
  getIdFromTag(tag: string): number {
    if (tag) {
      const match = tag.match(/.*_(\d+)$/);
      if (match) {
        const id = match[1]; // Extracted ID
        return parseInt(id);
      }
    }
    return 0;
  }
}
