{"ast": null, "code": "import { computed, signal } from '@angular/core';\nimport { BehaviorSubject, catchError, EMPTY, filter, finalize, forkJoin, of, switchMap, tap } from 'rxjs';\nimport { AccessType } from 'src/app/shared/model/project';\nimport { ProjectDialogComponent } from '../../components/project-dialog/project-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api/project-api.service\";\nimport * as i2 from \"../user/user.service\";\nimport * as i3 from \"../loader/loader.service\";\nimport * as i4 from \"../treeNode/tree-node.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"../snackbar/snack-bar.service\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"src/app/shared/utils/diagram-utils\";\nimport * as i9 from \"../app.service\";\nimport * as i10 from \"../access/access.service\";\nimport * as i11 from \"../errors/error.service\";\nimport * as i12 from \"@ngx-translate/core\";\nexport class ProjectService {\n  constructor(projectApiService, _userService, loaderService, treeNodeService, router, snackBarService, dialog, diagramUtils, appService, accessService, errorService, translateService) {\n    this.projectApiService = projectApiService;\n    this._userService = _userService;\n    this.loaderService = loaderService;\n    this.treeNodeService = treeNodeService;\n    this.router = router;\n    this.snackBarService = snackBarService;\n    this.dialog = dialog;\n    this.diagramUtils = diagramUtils;\n    this.appService = appService;\n    this.accessService = accessService;\n    this.errorService = errorService;\n    this.translateService = translateService;\n    /** The current project details as an observable */\n    this._currentProjectSubject = new BehaviorSubject(null);\n    this.projectSearchTerm = new BehaviorSubject(null);\n    this.projectFilterCriteria = new BehaviorSubject({});\n    this._lockedProjectSubject = new BehaviorSubject(null);\n    /** List of filtered projects based on permissions and search criteria */\n    this.filteredProjects = signal([]);\n    this.projects = signal([]);\n    // State management signals\n    this.isLoading = signal(false);\n    this.totalProjectCount = signal(0);\n    this.currentPage = signal(1);\n    this.pageSize = signal(10);\n    this.currentSort = signal({\n      active: 'lastModifiedDate',\n      direction: 'desc'\n    });\n    // Computed signals for derived state\n    this.sortedProjects = computed(() => {\n      const sort = this.currentSort();\n      if (!sort.active || !sort.direction) {\n        return this.projects();\n      }\n      return [...this.projects()].sort((a, b) => {\n        const isAsc = sort.direction === 'asc';\n        const valueA = this.getSortingValue(a, sort.active);\n        const valueB = this.getSortingValue(b, sort.active);\n        if (valueA < valueB) {\n          return isAsc ? -1 : 1;\n        }\n        if (valueA > valueB) {\n          return isAsc ? 1 : -1;\n        }\n        return 0;\n      });\n    });\n    /** Whether the current project is locked */\n    this._isProjectLocked = false;\n    // Track the last filter criteria to prevent duplicate API calls\n    this.lastFilterCriteria = {};\n  }\n  /**\n   * Gets the lock status of the active project.\n   *\n   * @returns {boolean} - Whether the project is locked.\n   */\n  get isProjectLocked() {\n    return this._isProjectLocked;\n  }\n  /**\n   * Sets the lock status of the active project.\n   *\n   * @param value - The lock status.\n   */\n  set isProjectLocked(value) {\n    this._isProjectLocked = value;\n  }\n  /**\n   * Updates the project search term and notifies subscribers.\n   * @param searchTerm - The new search term for projects, or `null` to clear the search term.\n   */\n  setProjectSearchTerm(searchTerm) {\n    this.projectSearchTerm.next(searchTerm);\n  }\n  /**\n   * Updates the project filter criteria and notifies subscribers.\n   * Prevents duplicate API calls by checking if the criteria have changed.\n   * @param criteria - An object containing the filter criteria, such as `type` or `productLine`.\n   */\n  setProjectFilterCriteria(criteria) {\n    // Create a clean copy of the criteria, removing undefined values\n    const cleanCriteria = {};\n    // Only include properties that have values\n    if (criteria.type && criteria.type.length > 0) {\n      cleanCriteria.type = criteria.type;\n    }\n    if (criteria.productLine && criteria.productLine.length > 0) {\n      cleanCriteria.productLine = criteria.productLine;\n    }\n    // Only include isSelfProject if it's true\n    if (criteria.isSelfProject === true) {\n      cleanCriteria.isSelfProject = true;\n    }\n    // Check if the criteria have changed\n    const currentCriteria = JSON.stringify(this.lastFilterCriteria);\n    const newCriteria = JSON.stringify(cleanCriteria);\n    if (currentCriteria !== newCriteria) {\n      this.lastFilterCriteria = {\n        ...cleanCriteria\n      };\n      this.projectFilterCriteria.next(cleanCriteria);\n      // Reset to first page when filter criteria change\n      this.currentPage.set(1);\n    }\n  }\n  /**\n   * Retrieves an observable for project filter criteria updates.\n   * @returns An observable that emits the current filter criteria.\n   */\n  projectFilterCriteriaChanges() {\n    return this.projectFilterCriteria.asObservable();\n  }\n  getProjectFilterCriteria() {\n    return this.projectFilterCriteria.getValue();\n  }\n  /**\n   * Retrieves an observable for project search term updates.\n   * @returns An observable that emits the current search term.\n   */\n  getProjectSearchTerm() {\n    return this.projectSearchTerm.asObservable();\n  }\n  /**\n   * Sets the current project and updates its diagrams with the project ID.\n   * This method updates the library details for the project using `treeNodeService`\n   * and emits the updated project to subscribers.\n   * @param project - The project details to set as the current project.\n   */\n  setCurrentProject(project) {\n    project.diagrams = project.diagrams.map(diagram => {\n      return {\n        ...diagram,\n        idProject: project.id\n      };\n    });\n    this.appService.lastModifiedDate.set(project.lastModifiedDate);\n    this.treeNodeService.setLibraryDetails(project);\n    this._currentProjectSubject.next(project);\n  }\n  /**\n   * Subscribes to changes in the current project.\n   * @returns An observable that emits updates to the current project or `null` if none is set.\n   */\n  currentProjectChanges() {\n    return this._currentProjectSubject.asObservable();\n  }\n  /**\n   * Sets the lock status of a project and notifies subscribers.\n   * @param lock - An object representing the project lock status, or `null` to clear the lock.\n   */\n  setLockedProject(lock) {\n    this._lockedProjectSubject.next(lock);\n  }\n  /**\n   * Retrieves the current lock status of a project.\n   * @returns The current lock status, or `null` if no lock is set.\n   */\n  getLockedProject() {\n    return this._lockedProjectSubject.getValue();\n  }\n  /**\n   * Creates a new project by invoking the API service.\n   * Updates the projects signal with the new project.\n   *\n   * @param project - The details of the project to create.\n   * @returns An observable that emits the created project.\n   */\n  createNewProject(project) {\n    this.isLoading.set(true);\n    return this.projectApiService.createProject(project).pipe(tap(createdProject => {\n      // Add the new project to the projects signal\n      if (createdProject && createdProject.id) {\n        // We need to cast it to ProjectWithPermission since the API returns Project\n        const newProject = createdProject;\n        // Update the projects signal\n        const currentProjects = this.projects();\n        this.projects.set([newProject, ...currentProjects]);\n        // Update the filtered projects signal\n        const currentFilteredProjects = this.filteredProjects();\n        this.filteredProjects.set([newProject, ...currentFilteredProjects]);\n        // Increment the total count\n        this.totalProjectCount.set(this.totalProjectCount() + 1);\n      }\n    }), finalize(() => {\n      this.isLoading.set(false);\n    }));\n  }\n  /**\n   * Updates an existing project.\n   * Updates the projects signal with the updated project.\n   *\n   * @param project - The updated project data.\n   * @returns {Observable<Project>} - The updated project.\n   */\n  updateProject(project) {\n    this.isLoading.set(true);\n    return this.projectApiService.updateProject(project).pipe(tap(updatedProject => {\n      if (updatedProject && updatedProject.id) {\n        // We need to cast it to ProjectWithPermission since the API returns Project\n        const projectWithPermission = updatedProject;\n        // Update the projects signal\n        const currentProjects = this.projects();\n        const updatedProjects = currentProjects.map(p => p.id === projectWithPermission.id ? {\n          ...p,\n          ...projectWithPermission\n        } : p);\n        this.projects.set(updatedProjects);\n        // Update the filtered projects signal\n        const currentFilteredProjects = this.filteredProjects();\n        const updatedFilteredProjects = currentFilteredProjects.map(p => p.id === projectWithPermission.id ? {\n          ...p,\n          ...projectWithPermission\n        } : p);\n        this.filteredProjects.set(updatedFilteredProjects);\n      }\n    }), finalize(() => {\n      this.isLoading.set(false);\n    }));\n  }\n  /**\n   * Deletes a project by its ID.\n   * Updates the projects signal by removing the deleted project.\n   *\n   * @param projectId - The ID of the project to delete.\n   * @returns {Observable<void>} - An observable that completes when the project is deleted.\n   */\n  deleteProject(projectId) {\n    this.isLoading.set(true);\n    return this.projectApiService.deleteProject(projectId).pipe(tap(() => {\n      // Remove the project from the projects signal\n      const currentProjects = this.projects();\n      const updatedProjects = currentProjects.filter(p => p.id !== projectId);\n      this.projects.set(updatedProjects);\n      // Remove the project from the filtered projects signal\n      const currentFilteredProjects = this.filteredProjects();\n      const updatedFilteredProjects = currentFilteredProjects.filter(p => p.id !== projectId);\n      this.filteredProjects.set(updatedFilteredProjects);\n      // Decrement the total count\n      this.totalProjectCount.set(Math.max(0, this.totalProjectCount() - 1));\n    }), finalize(() => {\n      this.isLoading.set(false);\n    }));\n  }\n  /**\n   * Retrieves all projects with permissions for the logged-in user.\n   * Updates the projects and filteredProjects signals with the results.\n   *\n   * @param {number} pageSize - Number of projects per page\n   * @param {number} currentPage - Current page number\n   * @param {string} [sortBy] - Field to sort by (name, description, type, productLine, lastModifiedDate)\n   * @param {boolean} [sortDescending=true] - Whether to sort in descending order\n   * @param {string} [searchTerm] - Term to search for in project name and description\n   * @param {ProjectFilterCriteria} [filterCriteria] - Additional filter criteria\n   * @param {boolean} [isForPagination=false] - Whether this request is for pagination (append results) or a fresh load\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\n   */\n  getProjects(pageSize, currentPage, sortBy, sortDescending = true, searchTerm, filterCriteria, isForPagination = false) {\n    // Update state signals\n    this.isLoading.set(true);\n    this.pageSize.set(pageSize);\n    this.currentPage.set(currentPage);\n    if (sortBy) {\n      this.currentSort.set({\n        active: sortBy,\n        direction: sortDescending ? 'desc' : 'asc'\n      });\n    }\n    return this.projectApiService.getProjects(pageSize, currentPage, sortBy, sortDescending, searchTerm, filterCriteria).pipe(tap(response => {\n      // Update total count from pagination header\n      const paginationHeader = response.headers.get('x-pagination');\n      if (paginationHeader) {\n        const pagination = JSON.parse(paginationHeader);\n        this.totalProjectCount.set(pagination.TotalRecords);\n      }\n      // Update projects signals\n      if (Array.isArray(response.body)) {\n        const validProjects = response.body.filter(project => project != null);\n        if (isForPagination) {\n          // For pagination, append to existing projects\n          const currentProjects = this.projects();\n          const newProjects = [...currentProjects];\n          // Replace projects at the current page\n          const startIndex = (currentPage - 1) * pageSize;\n          for (let i = 0; i < validProjects.length; i++) {\n            if (startIndex + i < newProjects.length) {\n              newProjects[startIndex + i] = validProjects[i];\n            } else {\n              newProjects.push(validProjects[i]);\n            }\n          }\n          this.projects.set(newProjects);\n          this.filteredProjects.set([...newProjects]);\n        } else {\n          // For fresh load, replace all projects\n          this.projects.set(validProjects);\n          this.filteredProjects.set([...validProjects]);\n        }\n      }\n    }), finalize(() => {\n      this.isLoading.set(false);\n    }));\n  }\n  /**\n   * Retrieves a project by its ID.\n   *\n   * @param idProject - The ID of the project to retrieve.\n   * @returns {Observable<ProjectDetails>} - The project details.\n   */\n  getProjectById(idProject) {\n    return this.projectApiService.getProjectById(idProject);\n  }\n  /**\n   * Filters projects based on the search term.\n   * This is a client-side filter that operates on the existing projects.\n   * For server-side filtering, use getProjects with a searchTerm.\n   *\n   * @param searchTerm - The search term for filtering projects.\n   */\n  filterProjects(searchTerm) {\n    if (searchTerm !== '') {\n      const allProjects = this.projects();\n      const filtered = allProjects.filter(project => {\n        const searchTermLower = searchTerm.toLowerCase();\n        return project.name?.toLowerCase().includes(searchTermLower) || project.description?.toLowerCase().includes(searchTermLower) || project.type?.toLowerCase().includes(searchTermLower) || project.productLine?.toLowerCase().includes(searchTermLower) || project.admin?.toLowerCase().includes(searchTermLower);\n      });\n      this.filteredProjects.set(filtered);\n    } else {\n      // If search term is empty, show all projects\n      this.filteredProjects.set(this.projects());\n    }\n  }\n  /**\n   * Applies sorting to the projects.\n   * Updates the currentSort signal and sorts the projects accordingly.\n   *\n   * @param sort - The sort configuration to apply\n   */\n  applySorting(sort) {\n    this.currentSort.set(sort);\n    // The sortedProjects computed signal will automatically update\n  }\n  /**\n   * Loads the next page of projects.\n   * Increments the current page and loads the projects for that page.\n   *\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\n   */\n  loadNextPage() {\n    const nextPage = this.currentPage() + 1;\n    return this.getProjects(this.pageSize(), nextPage, this.currentSort().active, this.currentSort().direction === 'desc', this.projectSearchTerm.getValue() || undefined, this.getProjectFilterCriteria(), true // isForPagination\n    );\n  }\n  /**\n   * Loads the previous page of projects.\n   * Decrements the current page and loads the projects for that page.\n   *\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\n   */\n  loadPreviousPage() {\n    const prevPage = Math.max(1, this.currentPage() - 1);\n    return this.getProjects(this.pageSize(), prevPage, this.currentSort().active, this.currentSort().direction === 'desc', this.projectSearchTerm.getValue() || undefined, this.getProjectFilterCriteria(), true // isForPagination\n    );\n  }\n  /**\n   * Refreshes the current page of projects.\n   * Reloads the projects for the current page with the current sort and filter settings.\n   *\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\n   */\n  refreshProjects() {\n    // Set loading state\n    this.isLoading.set(true);\n    // Get current search term\n    const searchTerm = this.projectSearchTerm.getValue() || undefined;\n    return this.getProjects(this.pageSize(), this.currentPage(), this.currentSort().active, this.currentSort().direction === 'desc', searchTerm, this.getProjectFilterCriteria(), false // Not for pagination, fresh load\n    );\n  }\n  /**\n   * Locks a project.\n   *\n   * @param lockObj - The lock object to lock the project with.\n   */\n  lockProject(lockObj) {\n    this.projectApiService.lockProject(lockObj).subscribe({\n      next: lockedProject => {\n        this.setLockedProject(lockedProject);\n        this._isProjectLocked = true;\n        if (lockObj.idContact != lockedProject.idContact) this.translateService.get(['errors.lockedProject.header', 'errors.lockedProject.content'], {\n          user: lockedProject.lockedUserName\n        }).subscribe(translations => {\n          this.errorService.addError({\n            errorKey: 1204,\n            type: 'warn',\n            header: translations['errors.lockedProject.header'],\n            content: `${translations['errors.lockedProject.content']} ${lockedProject.lockedUserName}`,\n            isCustomError: true\n          });\n        });\n      },\n      error: () => {\n        this.setLockedProject(null);\n      }\n    });\n  }\n  /**\n   * Unlocks a project by its ID.\n   *\n   * @param idProject - The ID of the project to unlock.\n   */\n  unlockProject(idProject) {\n    this.projectApiService.unlockProject(idProject).subscribe(() => {\n      this._isProjectLocked = false;\n    });\n  }\n  /**\n   * Locks the project on load if the user has access and the project is not locked.\n   *\n   * @param idProject - The ID of the project to lock.\n   * @param idUser - The ID of the user requesting the lock.\n   */\n  lockProjectOnLoad(idProject, idUser) {\n    if (idUser) {\n      this.lockProject({\n        idProject: idProject,\n        idContact: idUser\n      });\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Checks if a project is locked.\n   *\n   * @param idProject - The ID of the project to check.\n   * @returns {Observable<LockResult>} - The result of the lock check.\n   */\n  checkProjectLocked(idProject) {\n    return this.projectApiService.isProjectLocked(idProject);\n  }\n  /**\n   * Opens a project and navigates to the editor view.\n   *\n   * @param idProject - The ID of the project to open.\n   * @param isLock - Whether to lock the project.\n   * @param initialDiagramId - Optional ID of a specific diagram to open.\n   */\n  openProject(idProject, isLock = false, initialDiagramId) {\n    this.loaderService.showLoader();\n    let project$ = this.getProjectById(idProject); // Always fetch project\n    // First get the project to check access type\n    project$.pipe(switchMap(project => {\n      // Always check lock status regardless of user access level\n      const lockStatus$ = this.checkProjectLocked(idProject);\n      // Only attempt to lock if user has edit access (not a Viewer) and isLock parameter is true\n      const shouldLock = isLock && project.accessType !== AccessType.Viewer;\n      return forkJoin({\n        project: of(project),\n        lockStatus: lockStatus$,\n        shouldLock: of(shouldLock)\n      });\n    }), finalize(() => this.loaderService.hideLoader()), catchError(error => {\n      this.handleError(error);\n      return EMPTY;\n    })).subscribe({\n      next: ({\n        project,\n        lockStatus,\n        shouldLock\n      }) => {\n        // Set the access type first\n        this.accessService.setProjectAccess(project.accessType);\n        const loggedInUser = this._userService.getUser();\n        this.saveUserData(loggedInUser);\n        // Always handle project access based on lock status\n        if (lockStatus) {\n          this.handleProjectAccess(lockStatus, project, loggedInUser);\n        }\n        // Only attempt to lock if user has edit access and we should lock\n        if (shouldLock && lockStatus) {\n          this.lockProjectOnLoad(idProject, loggedInUser?.id);\n        }\n        // Always navigate to the route with both project ID and diagram ID\n        // If no specific diagram ID is provided, use the first diagram in the project\n        const diagramId = initialDiagramId || (project.diagrams.length > 0 ? project.diagrams[0].id : null);\n        debugger;\n        if (diagramId) {\n          // Find and set the target diagram as active\n          let targetDiagram = project.diagrams.find(d => d.id === diagramId);\n          // If not found in main diagrams, search in folders\n          if (!targetDiagram && project.folders) {\n            targetDiagram = this.findDiagramInFolders(project.folders, diagramId);\n          }\n          if (targetDiagram) {\n            // We'll set the active diagram after setting the current project\n            setTimeout(() => {\n              this.router.navigate([`/editor/${project.id}/diagram/${diagramId}`]);\n              this.diagramUtils.setActiveDiagram(targetDiagram);\n            }, 0);\n          }\n        } else {\n          // If there are no diagrams in the project, clear the active diagram\n          // and navigate to a placeholder URL\n          this.router.navigate([`/editor/${project.id}/diagram/0`]);\n          this.diagramUtils.setActiveDiagram(null);\n        }\n        this.setCurrentProject(project);\n      }\n    });\n  }\n  handleProjectAccess(lockStatus, project, loggedInUser) {\n    if (!lockStatus.isProjectLocked) {\n      this.accessService.setProjectAccess(project.accessType);\n      return;\n    }\n    // For users with edit access (Editor or Admin), check if they are the ones locking the project\n    const isCurrentUserLocking = loggedInUser && lockStatus.lockingIdContact !== 0 && loggedInUser.id !== lockStatus.lockingIdContact;\n    // If the project is locked by another user, downgrade to Viewer and show a dialog\n    if (isCurrentUserLocking) {\n      this.accessService.setProjectAccess(AccessType.Viewer);\n      // Show dialog message about the project being locked by another user\n      this.translateService.get(['errors.lockedProject.header', 'errors.lockedProject.content'], {\n        user: lockStatus.lockedUserName\n      }).subscribe(translations => {\n        this.errorService.addError({\n          errorKey: 1204,\n          type: 'warn',\n          header: translations['errors.lockedProject.header'],\n          content: `${translations['errors.lockedProject.content']} ${lockStatus.lockedUserName}`,\n          isCustomError: true\n        });\n      });\n    } else {\n      // Otherwise, keep their original access level\n      this.accessService.setProjectAccess(project.accessType);\n    }\n  }\n  saveUserData(user) {\n    if (!localStorage.getItem('userData') && user) {\n      localStorage.setItem('userData', JSON.stringify(user));\n    }\n  }\n  handleError(error) {\n    console.error('Error opening project:', error);\n  }\n  /**\n   * Gets the value to use for sorting based on the property name.\n   * @param project - The project to get the value from\n   * @param property - The property name to get the value for\n   * @returns The value to use for sorting\n   */\n  getSortingValue(project, property) {\n    switch (property) {\n      case 'name':\n      case 'description':\n      case 'type':\n      case 'productLine':\n      case 'admin':\n        return project[property]?.toLowerCase() || '';\n      case 'lastModifiedDate':\n        return new Date(project.lastModifiedDate || 0);\n      default:\n        return project[property];\n    }\n  }\n  /**\n   * Handles unlocking a project if the logged-in user has access to unlock it.\n   *\n   * @param idProject - The ID of the project to unlock.\n   */\n  handleProjectUnlock(idProject) {\n    const lockedProject = this.getLockedProject();\n    const loggedInUser = this._userService.getUser();\n    if (lockedProject && loggedInUser && lockedProject.idContact == loggedInUser.id) {\n      this.unlockProject(idProject);\n    }\n  }\n  exportProject(idProject, formData) {\n    return this.projectApiService.exportProjectDetails(idProject, formData);\n  }\n  exportDiagramImages(formData) {\n    return this.projectApiService.exportImages(formData);\n  }\n  openProjectDialog(project) {\n    const dialogData = this.prepareProjectDialogData(project);\n    const dialogRef = this.openDialog(ProjectDialogComponent, dialogData);\n    this.handleProjectDialogResult(dialogRef, project);\n  }\n  /**\n   * Prepare dialog data for project creation/editing\n   */\n  prepareProjectDialogData(project) {\n    const isEditableProject = project && project.accessType !== AccessType.Viewer;\n    return {\n      title: isEditableProject ? 'dashboard.editProject' : 'dashboard.newProject',\n      btnText: isEditableProject ? 'dialog.save' : 'dialog.create',\n      ...(isEditableProject && {\n        project\n      })\n    };\n  }\n  /**\n   * Open a generic dialog with specified configuration\n   */\n  openDialog(component, data, options = {}) {\n    return this.dialog.open(component, {\n      autoFocus: false,\n      ...options,\n      data\n    });\n  }\n  /**\n   * Handle project dialog result\n   */\n  handleProjectDialogResult(dialogRef, originalProject) {\n    dialogRef.afterClosed().pipe(filter(result => result !== null && result !== undefined), switchMap(result => this.processProjectDialogResult(result, originalProject))\n    // takeUntil(this.destroy$)\n    ).subscribe();\n  }\n  /**\n   * Process project dialog result\n   */\n  processProjectDialogResult(dialogResult, originalProject) {\n    this.diagramUtils.clearEnumTypeOptions();\n    const projectServiceCall = dialogResult.isCreation ? this.createNewProject(dialogResult.project) : this.updateProject(dialogResult.project);\n    return projectServiceCall.pipe(tap(resultProject => {\n      if (resultProject && resultProject.id) {\n        this.handleProjectServiceSuccess(resultProject, dialogResult, originalProject);\n      }\n    }));\n  }\n  /**\n   * Handle successful project service operation\n   */\n  handleProjectServiceSuccess(resultProject, dialogResult, originalProject) {\n    if (dialogResult.isCreation) {\n      // When creating a new project, open it with the first diagram (if any)\n      this.openProject(resultProject.id, true);\n    } else if (originalProject) {\n      const updatedProject = {\n        ...originalProject,\n        ...dialogResult.project\n      };\n      this.updateProjectInList(updatedProject);\n    }\n    this.snackBarService.openSnackbar(dialogResult.isCreation ? 'snackBar.projectCreationMsg' : 'snackBar.projectUpdatedMsg');\n  }\n  /**\n   * Recursively searches for a diagram in project folders by ID\n   * @param folders - Array of folders to search in\n   * @param diagramId - ID of the diagram to find\n   * @returns The found diagram or null if not found\n   */\n  findDiagramInFolders(folders, diagramId) {\n    for (const folder of folders) {\n      // Search in current folder's diagrams\n      if (folder.diagrams) {\n        const foundDiagram = folder.diagrams.find(d => d.id === diagramId);\n        if (foundDiagram) {\n          return foundDiagram;\n        }\n      }\n      // Recursively search in child folders\n      if (folder.childFolders && folder.childFolders.length > 0) {\n        const foundInChild = this.findDiagramInFolders(folder.childFolders, diagramId);\n        if (foundInChild) {\n          return foundInChild;\n        }\n      }\n    }\n    return null;\n  }\n  updateProjectInList(updatedProject) {\n    const projectIndex = this.projects().findIndex(p => p.id === updatedProject.id);\n    const filteredProjectIndex = this.filteredProjects().findIndex(p => p.id === updatedProject.id);\n    if (projectIndex !== -1 && filteredProjectIndex !== -1) {\n      this.projects()[projectIndex] = updatedProject;\n      this.filteredProjects()[filteredProjectIndex] = updatedProject;\n      this.setProjectFilterCriteria(this.getProjectFilterCriteria());\n    }\n  }\n  static #_ = this.ɵfac = function ProjectService_Factory(t) {\n    return new (t || ProjectService)(i0.ɵɵinject(i1.ProjectApiService), i0.ɵɵinject(i2.UserService), i0.ɵɵinject(i3.LoaderService), i0.ɵɵinject(i4.TreeNodeService), i0.ɵɵinject(i5.Router), i0.ɵɵinject(i6.SnackBarService), i0.ɵɵinject(i7.MatDialog), i0.ɵɵinject(i8.DiagramUtils), i0.ɵɵinject(i9.AppService), i0.ɵɵinject(i10.AccessService), i0.ɵɵinject(i11.ErrorService), i0.ɵɵinject(i12.TranslateService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ProjectService,\n    factory: ProjectService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["computed", "signal", "BehaviorSubject", "catchError", "EMPTY", "filter", "finalize", "fork<PERSON><PERSON>n", "of", "switchMap", "tap", "AccessType", "ProjectDialogComponent", "ProjectService", "constructor", "projectApiService", "_userService", "loaderService", "treeNodeService", "router", "snackBarService", "dialog", "diagramUtils", "appService", "accessService", "errorService", "translateService", "_currentProjectSubject", "projectSearchTerm", "projectFilterCriteria", "_lockedProjectSubject", "filteredProjects", "projects", "isLoading", "totalProjectCount", "currentPage", "pageSize", "currentSort", "active", "direction", "sortedProjects", "sort", "a", "b", "isAsc", "valueA", "getSortingValue", "valueB", "_isProjectLocked", "lastFilterCriteria", "isProjectLocked", "value", "setProjectSearchTerm", "searchTerm", "next", "setProjectFilterCriteria", "criteria", "cleanCriteria", "type", "length", "productLine", "isSelfProject", "currentCriteria", "JSON", "stringify", "newCriteria", "set", "projectFilterCriteriaChanges", "asObservable", "getProjectFilterCriteria", "getValue", "getProjectSearchTerm", "setCurrentProject", "project", "diagrams", "map", "diagram", "idProject", "id", "lastModifiedDate", "setLibraryDetails", "currentProjectChanges", "setLockedProject", "lock", "getLockedProject", "createNewProject", "createProject", "pipe", "createdProject", "newProject", "currentProjects", "currentFilteredProjects", "updateProject", "updatedProject", "projectWithPermission", "updatedProjects", "p", "updatedFilteredProjects", "deleteProject", "projectId", "Math", "max", "getProjects", "sortBy", "sortDescending", "filterCriteria", "isForPagination", "response", "paginationHeader", "headers", "get", "pagination", "parse", "TotalRecords", "Array", "isArray", "body", "validProjects", "newProjects", "startIndex", "i", "push", "getProjectById", "filterProjects", "allProjects", "filtered", "searchTermLower", "toLowerCase", "name", "includes", "description", "admin", "applySorting", "loadNextPage", "nextPage", "undefined", "loadPreviousPage", "prevPage", "refreshProjects", "lockProject", "lock<PERSON>bj", "subscribe", "lockedProject", "idContact", "user", "lockedUserName", "translations", "addError", "<PERSON><PERSON><PERSON>", "header", "content", "isCustomError", "error", "unlockProject", "lockProjectOnLoad", "idUser", "navigate", "checkProjectLocked", "openProject", "isLock", "initialDiagramId", "<PERSON><PERSON><PERSON><PERSON>", "project$", "lockStatus$", "shouldLock", "accessType", "Viewer", "lockStatus", "<PERSON><PERSON><PERSON><PERSON>", "handleError", "setProjectAccess", "loggedInUser", "getUser", "saveUserData", "handleProjectAccess", "diagramId", "targetDiagram", "find", "d", "folders", "findDiagramInFolders", "setTimeout", "setActiveDiagram", "isCurrentUserLocking", "lockingIdContact", "localStorage", "getItem", "setItem", "console", "property", "Date", "handleProjectUnlock", "exportProject", "formData", "exportProjectDetails", "exportDiagramImages", "exportImages", "openProjectDialog", "dialogData", "prepareProjectDialogData", "dialogRef", "openDialog", "handleProjectDialogResult", "isEditableProject", "title", "btnText", "component", "data", "options", "open", "autoFocus", "originalProject", "afterClosed", "result", "processProjectDialogResult", "dialogResult", "clearEnumTypeOptions", "projectServiceCall", "isCreation", "resultProject", "handleProjectServiceSuccess", "updateProjectInList", "openSnackbar", "folder", "foundDiagram", "childFolders", "foundInChild", "projectIndex", "findIndex", "filteredProjectIndex", "_", "i0", "ɵɵinject", "i1", "ProjectApiService", "i2", "UserService", "i3", "LoaderService", "i4", "TreeNodeService", "i5", "Router", "i6", "SnackBarService", "i7", "MatDialog", "i8", "DiagramUtils", "i9", "AppService", "i10", "AccessService", "i11", "ErrorService", "i12", "TranslateService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["D:\\GitHub\\Bassetti\\devint-BASSETTI-GROUP-APP\\BassettiUMLWebApp\\UMLApp\\src\\app\\core\\services\\project\\project.service.ts"], "sourcesContent": ["import { ComponentType } from '@angular/cdk/portal';\r\nimport { computed, Injectable, signal } from '@angular/core';\r\nimport { MatDialog, MatDialogRef } from '@angular/material/dialog';\r\nimport { Sort } from '@angular/material/sort';\r\nimport { Router } from '@angular/router';\r\nimport {\r\n  BehaviorSubject,\r\n  catchError,\r\n  EMPTY,\r\n  filter,\r\n  finalize,\r\n  forkJoin,\r\n  Observable,\r\n  of,\r\n  switchMap,\r\n  tap,\r\n} from 'rxjs';\r\nimport {\r\n  ProjectDialogData,\r\n  ProjectDialogResult,\r\n} from 'src/app/shared/model/dialog';\r\nimport {\r\n  AccessType,\r\n  ILockProject,\r\n  LockResult,\r\n  Project,\r\n  ProjectDetails,\r\n  ProjectFilterCriteria,\r\n  ProjectWithPermission,\r\n} from 'src/app/shared/model/project';\r\nimport { IUser } from 'src/app/shared/model/user';\r\nimport { DiagramUtils } from 'src/app/shared/utils/diagram-utils';\r\nimport { ProjectDialogComponent } from '../../components/project-dialog/project-dialog.component';\r\nimport { AccessService } from '../access/access.service';\r\nimport { ProjectApiService } from '../api/project-api.service';\r\nimport { AppService } from '../app.service';\r\nimport { LoaderService } from '../loader/loader.service';\r\nimport { SnackBarService } from '../snackbar/snack-bar.service';\r\nimport { TreeNodeService } from '../treeNode/tree-node.service';\r\nimport { UserService } from '../user/user.service';\r\n\r\nimport { HttpResponse } from '@angular/common/http';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { ErrorService } from '../errors/error.service';\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class ProjectService {\r\n  /** The current project details as an observable */\r\n  private _currentProjectSubject = new BehaviorSubject<ProjectDetails | null>(\r\n    null\r\n  );\r\n  private projectSearchTerm = new BehaviorSubject<string | null>(null);\r\n  private projectFilterCriteria = new BehaviorSubject<ProjectFilterCriteria>(\r\n    {}\r\n  );\r\n  private _lockedProjectSubject = new BehaviorSubject<ILockProject | null>(\r\n    null\r\n  );\r\n\r\n  /** List of filtered projects based on permissions and search criteria */\r\n  filteredProjects = signal<ProjectWithPermission[]>([]);\r\n  projects = signal<ProjectWithPermission[]>([]);\r\n\r\n  // State management signals\r\n  isLoading = signal<boolean>(false);\r\n  totalProjectCount = signal<number>(0);\r\n  currentPage = signal<number>(1);\r\n  pageSize = signal<number>(10);\r\n  currentSort = signal<Sort>({ active: 'lastModifiedDate', direction: 'desc' });\r\n\r\n  // Computed signals for derived state\r\n  sortedProjects = computed(() => {\r\n    const sort = this.currentSort();\r\n    if (!sort.active || !sort.direction) {\r\n      return this.projects();\r\n    }\r\n\r\n    return [...this.projects()].sort((a, b) => {\r\n      const isAsc = sort.direction === 'asc';\r\n      const valueA = this.getSortingValue(a, sort.active);\r\n      const valueB = this.getSortingValue(b, sort.active);\r\n\r\n      if (valueA < valueB) {\r\n        return isAsc ? -1 : 1;\r\n      }\r\n      if (valueA > valueB) {\r\n        return isAsc ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n  });\r\n\r\n  /** Whether the current project is locked */\r\n  private _isProjectLocked: boolean = false;\r\n\r\n  constructor(\r\n    private projectApiService: ProjectApiService,\r\n    private _userService: UserService,\r\n    private loaderService: LoaderService,\r\n    private treeNodeService: TreeNodeService,\r\n    private router: Router,\r\n    private snackBarService: SnackBarService,\r\n    private dialog: MatDialog,\r\n    private diagramUtils: DiagramUtils,\r\n    private appService: AppService,\r\n    private accessService: AccessService,\r\n    private errorService: ErrorService,\r\n    private translateService: TranslateService\r\n  ) {}\r\n\r\n  /**\r\n   * Gets the lock status of the active project.\r\n   *\r\n   * @returns {boolean} - Whether the project is locked.\r\n   */\r\n  get isProjectLocked(): boolean {\r\n    return this._isProjectLocked;\r\n  }\r\n\r\n  /**\r\n   * Sets the lock status of the active project.\r\n   *\r\n   * @param value - The lock status.\r\n   */\r\n  set isProjectLocked(value: boolean) {\r\n    this._isProjectLocked = value;\r\n  }\r\n\r\n  /**\r\n   * Updates the project search term and notifies subscribers.\r\n   * @param searchTerm - The new search term for projects, or `null` to clear the search term.\r\n   */\r\n  setProjectSearchTerm(searchTerm: string | null): void {\r\n    this.projectSearchTerm.next(searchTerm);\r\n  }\r\n\r\n  // Track the last filter criteria to prevent duplicate API calls\r\n  private lastFilterCriteria: ProjectFilterCriteria = {};\r\n\r\n  /**\r\n   * Updates the project filter criteria and notifies subscribers.\r\n   * Prevents duplicate API calls by checking if the criteria have changed.\r\n   * @param criteria - An object containing the filter criteria, such as `type` or `productLine`.\r\n   */\r\n  setProjectFilterCriteria(criteria: ProjectFilterCriteria): void {\r\n    // Create a clean copy of the criteria, removing undefined values\r\n    const cleanCriteria: ProjectFilterCriteria = {};\r\n\r\n    // Only include properties that have values\r\n    if (criteria.type && criteria.type.length > 0) {\r\n      cleanCriteria.type = criteria.type;\r\n    }\r\n\r\n    if (criteria.productLine && criteria.productLine.length > 0) {\r\n      cleanCriteria.productLine = criteria.productLine;\r\n    }\r\n\r\n    // Only include isSelfProject if it's true\r\n    if (criteria.isSelfProject === true) {\r\n      cleanCriteria.isSelfProject = true;\r\n    }\r\n\r\n    // Check if the criteria have changed\r\n    const currentCriteria = JSON.stringify(this.lastFilterCriteria);\r\n    const newCriteria = JSON.stringify(cleanCriteria);\r\n\r\n    if (currentCriteria !== newCriteria) {\r\n      this.lastFilterCriteria = { ...cleanCriteria };\r\n      this.projectFilterCriteria.next(cleanCriteria);\r\n\r\n      // Reset to first page when filter criteria change\r\n      this.currentPage.set(1);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Retrieves an observable for project filter criteria updates.\r\n   * @returns An observable that emits the current filter criteria.\r\n   */\r\n  projectFilterCriteriaChanges(): Observable<ProjectFilterCriteria> {\r\n    return this.projectFilterCriteria.asObservable();\r\n  }\r\n\r\n  getProjectFilterCriteria(): ProjectFilterCriteria {\r\n    return this.projectFilterCriteria.getValue();\r\n  }\r\n\r\n  /**\r\n   * Retrieves an observable for project search term updates.\r\n   * @returns An observable that emits the current search term.\r\n   */\r\n  getProjectSearchTerm(): Observable<string | null> {\r\n    return this.projectSearchTerm.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Sets the current project and updates its diagrams with the project ID.\r\n   * This method updates the library details for the project using `treeNodeService`\r\n   * and emits the updated project to subscribers.\r\n   * @param project - The project details to set as the current project.\r\n   */\r\n  setCurrentProject(project: ProjectDetails): void {\r\n    project.diagrams = project.diagrams.map((diagram) => {\r\n      return { ...diagram, idProject: project.id! };\r\n    });\r\n    this.appService.lastModifiedDate.set(project.lastModifiedDate);\r\n    this.treeNodeService.setLibraryDetails(project);\r\n    this._currentProjectSubject.next(project);\r\n  }\r\n\r\n  /**\r\n   * Subscribes to changes in the current project.\r\n   * @returns An observable that emits updates to the current project or `null` if none is set.\r\n   */\r\n  currentProjectChanges(): Observable<ProjectDetails | null> {\r\n    return this._currentProjectSubject.asObservable();\r\n  }\r\n\r\n  /**\r\n   * Sets the lock status of a project and notifies subscribers.\r\n   * @param lock - An object representing the project lock status, or `null` to clear the lock.\r\n   */\r\n  setLockedProject(lock: ILockProject | null): void {\r\n    this._lockedProjectSubject.next(lock);\r\n  }\r\n\r\n  /**\r\n   * Retrieves the current lock status of a project.\r\n   * @returns The current lock status, or `null` if no lock is set.\r\n   */\r\n  getLockedProject(): ILockProject | null {\r\n    return this._lockedProjectSubject.getValue();\r\n  }\r\n\r\n  /**\r\n   * Creates a new project by invoking the API service.\r\n   * Updates the projects signal with the new project.\r\n   *\r\n   * @param project - The details of the project to create.\r\n   * @returns An observable that emits the created project.\r\n   */\r\n  createNewProject(project: Project): Observable<Project> {\r\n    this.isLoading.set(true);\r\n    return this.projectApiService.createProject(project).pipe(\r\n      tap((createdProject) => {\r\n        // Add the new project to the projects signal\r\n        if (createdProject && createdProject.id) {\r\n          // We need to cast it to ProjectWithPermission since the API returns Project\r\n          const newProject = createdProject as unknown as ProjectWithPermission;\r\n\r\n          // Update the projects signal\r\n          const currentProjects = this.projects();\r\n          this.projects.set([newProject, ...currentProjects]);\r\n\r\n          // Update the filtered projects signal\r\n          const currentFilteredProjects = this.filteredProjects();\r\n          this.filteredProjects.set([newProject, ...currentFilteredProjects]);\r\n\r\n          // Increment the total count\r\n          this.totalProjectCount.set(this.totalProjectCount() + 1);\r\n        }\r\n      }),\r\n      finalize(() => {\r\n        this.isLoading.set(false);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Updates an existing project.\r\n   * Updates the projects signal with the updated project.\r\n   *\r\n   * @param project - The updated project data.\r\n   * @returns {Observable<Project>} - The updated project.\r\n   */\r\n  updateProject(project: Project): Observable<Project> {\r\n    this.isLoading.set(true);\r\n    return this.projectApiService.updateProject(project).pipe(\r\n      tap((updatedProject) => {\r\n        if (updatedProject && updatedProject.id) {\r\n          // We need to cast it to ProjectWithPermission since the API returns Project\r\n          const projectWithPermission =\r\n            updatedProject as unknown as ProjectWithPermission;\r\n\r\n          // Update the projects signal\r\n          const currentProjects = this.projects();\r\n          const updatedProjects = currentProjects.map((p) =>\r\n            p.id === projectWithPermission.id\r\n              ? { ...p, ...projectWithPermission }\r\n              : p\r\n          );\r\n          this.projects.set(updatedProjects);\r\n\r\n          // Update the filtered projects signal\r\n          const currentFilteredProjects = this.filteredProjects();\r\n          const updatedFilteredProjects = currentFilteredProjects.map((p) =>\r\n            p.id === projectWithPermission.id\r\n              ? { ...p, ...projectWithPermission }\r\n              : p\r\n          );\r\n          this.filteredProjects.set(updatedFilteredProjects);\r\n        }\r\n      }),\r\n      finalize(() => {\r\n        this.isLoading.set(false);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Deletes a project by its ID.\r\n   * Updates the projects signal by removing the deleted project.\r\n   *\r\n   * @param projectId - The ID of the project to delete.\r\n   * @returns {Observable<void>} - An observable that completes when the project is deleted.\r\n   */\r\n  deleteProject(projectId: number): Observable<void> {\r\n    this.isLoading.set(true);\r\n    return this.projectApiService.deleteProject(projectId).pipe(\r\n      tap(() => {\r\n        // Remove the project from the projects signal\r\n        const currentProjects = this.projects();\r\n        const updatedProjects = currentProjects.filter(\r\n          (p) => p.id !== projectId\r\n        );\r\n        this.projects.set(updatedProjects);\r\n\r\n        // Remove the project from the filtered projects signal\r\n        const currentFilteredProjects = this.filteredProjects();\r\n        const updatedFilteredProjects = currentFilteredProjects.filter(\r\n          (p) => p.id !== projectId\r\n        );\r\n        this.filteredProjects.set(updatedFilteredProjects);\r\n\r\n        // Decrement the total count\r\n        this.totalProjectCount.set(Math.max(0, this.totalProjectCount() - 1));\r\n      }),\r\n      finalize(() => {\r\n        this.isLoading.set(false);\r\n      })\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retrieves all projects with permissions for the logged-in user.\r\n   * Updates the projects and filteredProjects signals with the results.\r\n   *\r\n   * @param {number} pageSize - Number of projects per page\r\n   * @param {number} currentPage - Current page number\r\n   * @param {string} [sortBy] - Field to sort by (name, description, type, productLine, lastModifiedDate)\r\n   * @param {boolean} [sortDescending=true] - Whether to sort in descending order\r\n   * @param {string} [searchTerm] - Term to search for in project name and description\r\n   * @param {ProjectFilterCriteria} [filterCriteria] - Additional filter criteria\r\n   * @param {boolean} [isForPagination=false] - Whether this request is for pagination (append results) or a fresh load\r\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\r\n   */\r\n  getProjects(\r\n    pageSize: number,\r\n    currentPage: number,\r\n    sortBy?: string,\r\n    sortDescending: boolean = true,\r\n    searchTerm?: string,\r\n    filterCriteria?: ProjectFilterCriteria,\r\n    isForPagination: boolean = false\r\n  ): Observable<HttpResponse<ProjectWithPermission[]>> {\r\n    // Update state signals\r\n    this.isLoading.set(true);\r\n    this.pageSize.set(pageSize);\r\n    this.currentPage.set(currentPage);\r\n\r\n    if (sortBy) {\r\n      this.currentSort.set({\r\n        active: sortBy,\r\n        direction: sortDescending ? 'desc' : 'asc',\r\n      });\r\n    }\r\n\r\n    return this.projectApiService\r\n      .getProjects(\r\n        pageSize,\r\n        currentPage,\r\n        sortBy,\r\n        sortDescending,\r\n        searchTerm,\r\n        filterCriteria\r\n      )\r\n      .pipe(\r\n        tap((response) => {\r\n          // Update total count from pagination header\r\n          const paginationHeader = response.headers.get('x-pagination');\r\n          if (paginationHeader) {\r\n            const pagination = JSON.parse(paginationHeader);\r\n            this.totalProjectCount.set(pagination.TotalRecords);\r\n          }\r\n\r\n          // Update projects signals\r\n          if (Array.isArray(response.body)) {\r\n            const validProjects = response.body.filter(\r\n              (project) => project != null\r\n            );\r\n\r\n            if (isForPagination) {\r\n              // For pagination, append to existing projects\r\n              const currentProjects = this.projects();\r\n              const newProjects = [...currentProjects];\r\n\r\n              // Replace projects at the current page\r\n              const startIndex = (currentPage - 1) * pageSize;\r\n              for (let i = 0; i < validProjects.length; i++) {\r\n                if (startIndex + i < newProjects.length) {\r\n                  newProjects[startIndex + i] = validProjects[i];\r\n                } else {\r\n                  newProjects.push(validProjects[i]);\r\n                }\r\n              }\r\n\r\n              this.projects.set(newProjects);\r\n              this.filteredProjects.set([...newProjects]);\r\n            } else {\r\n              // For fresh load, replace all projects\r\n              this.projects.set(validProjects);\r\n              this.filteredProjects.set([...validProjects]);\r\n            }\r\n          }\r\n        }),\r\n        finalize(() => {\r\n          this.isLoading.set(false);\r\n        })\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Retrieves a project by its ID.\r\n   *\r\n   * @param idProject - The ID of the project to retrieve.\r\n   * @returns {Observable<ProjectDetails>} - The project details.\r\n   */\r\n  getProjectById(idProject: number): Observable<ProjectDetails> {\r\n    return this.projectApiService.getProjectById(idProject);\r\n  }\r\n\r\n  /**\r\n   * Filters projects based on the search term.\r\n   * This is a client-side filter that operates on the existing projects.\r\n   * For server-side filtering, use getProjects with a searchTerm.\r\n   *\r\n   * @param searchTerm - The search term for filtering projects.\r\n   */\r\n  filterProjects(searchTerm: string): void {\r\n    if (searchTerm !== '') {\r\n      const allProjects = this.projects();\r\n      const filtered = allProjects.filter((project) => {\r\n        const searchTermLower = searchTerm.toLowerCase();\r\n        return (\r\n          project.name?.toLowerCase().includes(searchTermLower) ||\r\n          project.description?.toLowerCase().includes(searchTermLower) ||\r\n          project.type?.toLowerCase().includes(searchTermLower) ||\r\n          project.productLine?.toLowerCase().includes(searchTermLower) ||\r\n          project.admin?.toLowerCase().includes(searchTermLower)\r\n        );\r\n      });\r\n      this.filteredProjects.set(filtered);\r\n    } else {\r\n      // If search term is empty, show all projects\r\n      this.filteredProjects.set(this.projects());\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Applies sorting to the projects.\r\n   * Updates the currentSort signal and sorts the projects accordingly.\r\n   *\r\n   * @param sort - The sort configuration to apply\r\n   */\r\n  applySorting(sort: Sort): void {\r\n    this.currentSort.set(sort);\r\n    // The sortedProjects computed signal will automatically update\r\n  }\r\n\r\n  /**\r\n   * Loads the next page of projects.\r\n   * Increments the current page and loads the projects for that page.\r\n   *\r\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\r\n   */\r\n  loadNextPage(): Observable<HttpResponse<ProjectWithPermission[]>> {\r\n    const nextPage = this.currentPage() + 1;\r\n    return this.getProjects(\r\n      this.pageSize(),\r\n      nextPage,\r\n      this.currentSort().active,\r\n      this.currentSort().direction === 'desc',\r\n      this.projectSearchTerm.getValue() || undefined,\r\n      this.getProjectFilterCriteria(),\r\n      true // isForPagination\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Loads the previous page of projects.\r\n   * Decrements the current page and loads the projects for that page.\r\n   *\r\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\r\n   */\r\n  loadPreviousPage(): Observable<HttpResponse<ProjectWithPermission[]>> {\r\n    const prevPage = Math.max(1, this.currentPage() - 1);\r\n    return this.getProjects(\r\n      this.pageSize(),\r\n      prevPage,\r\n      this.currentSort().active,\r\n      this.currentSort().direction === 'desc',\r\n      this.projectSearchTerm.getValue() || undefined,\r\n      this.getProjectFilterCriteria(),\r\n      true // isForPagination\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Refreshes the current page of projects.\r\n   * Reloads the projects for the current page with the current sort and filter settings.\r\n   *\r\n   * @returns {Observable<HttpResponse<ProjectWithPermission[]>>} - The list of projects with HTTP response\r\n   */\r\n  refreshProjects(): Observable<HttpResponse<ProjectWithPermission[]>> {\r\n    // Set loading state\r\n    this.isLoading.set(true);\r\n\r\n    // Get current search term\r\n    const searchTerm = this.projectSearchTerm.getValue() || undefined;\r\n\r\n    return this.getProjects(\r\n      this.pageSize(),\r\n      this.currentPage(),\r\n      this.currentSort().active,\r\n      this.currentSort().direction === 'desc',\r\n      searchTerm,\r\n      this.getProjectFilterCriteria(),\r\n      false // Not for pagination, fresh load\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Locks a project.\r\n   *\r\n   * @param lockObj - The lock object to lock the project with.\r\n   */\r\n  lockProject(lockObj: ILockProject): void {\r\n    this.projectApiService.lockProject(lockObj).subscribe({\r\n      next: (lockedProject) => {\r\n        this.setLockedProject(lockedProject);\r\n        this._isProjectLocked = true;\r\n        if (lockObj.idContact != lockedProject.idContact)\r\n          this.translateService\r\n            .get(\r\n              ['errors.lockedProject.header', 'errors.lockedProject.content'],\r\n              { user: lockedProject.lockedUserName }\r\n            )\r\n            .subscribe((translations) => {\r\n              this.errorService.addError({\r\n                errorKey: 1204,\r\n                type: 'warn',\r\n                header: translations['errors.lockedProject.header'],\r\n                content: `${translations['errors.lockedProject.content']} ${lockedProject.lockedUserName}`,\r\n                isCustomError: true,\r\n              });\r\n            });\r\n      },\r\n      error: () => {\r\n        this.setLockedProject(null);\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Unlocks a project by its ID.\r\n   *\r\n   * @param idProject - The ID of the project to unlock.\r\n   */\r\n  unlockProject(idProject: number): void {\r\n    this.projectApiService.unlockProject(idProject).subscribe(() => {\r\n      this._isProjectLocked = false;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Locks the project on load if the user has access and the project is not locked.\r\n   *\r\n   * @param idProject - The ID of the project to lock.\r\n   * @param idUser - The ID of the user requesting the lock.\r\n   */\r\n  lockProjectOnLoad(idProject: number, idUser?: number): void {\r\n    if (idUser) {\r\n      this.lockProject({\r\n        idProject: idProject,\r\n        idContact: idUser,\r\n      });\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if a project is locked.\r\n   *\r\n   * @param idProject - The ID of the project to check.\r\n   * @returns {Observable<LockResult>} - The result of the lock check.\r\n   */\r\n  checkProjectLocked(idProject: number): Observable<LockResult> {\r\n    return this.projectApiService.isProjectLocked(idProject);\r\n  }\r\n\r\n  /**\r\n   * Opens a project and navigates to the editor view.\r\n   *\r\n   * @param idProject - The ID of the project to open.\r\n   * @param isLock - Whether to lock the project.\r\n   * @param initialDiagramId - Optional ID of a specific diagram to open.\r\n   */\r\n  openProject(\r\n    idProject: number,\r\n    isLock: boolean = false,\r\n    initialDiagramId?: number\r\n  ): void {\r\n    this.loaderService.showLoader();\r\n    let project$ = this.getProjectById(idProject); // Always fetch project\r\n\r\n    // First get the project to check access type\r\n    project$\r\n      .pipe(\r\n        switchMap((project) => {\r\n          // Always check lock status regardless of user access level\r\n          const lockStatus$ = this.checkProjectLocked(idProject);\r\n\r\n          // Only attempt to lock if user has edit access (not a Viewer) and isLock parameter is true\r\n          const shouldLock = isLock && project.accessType !== AccessType.Viewer;\r\n\r\n          return forkJoin({\r\n            project: of(project),\r\n            lockStatus: lockStatus$,\r\n            shouldLock: of(shouldLock),\r\n          });\r\n        }),\r\n        finalize(() => this.loaderService.hideLoader()),\r\n        catchError((error) => {\r\n          this.handleError(error);\r\n          return EMPTY;\r\n        })\r\n      )\r\n      .subscribe({\r\n        next: ({ project, lockStatus, shouldLock }) => {\r\n          // Set the access type first\r\n          this.accessService.setProjectAccess(project.accessType);\r\n\r\n          const loggedInUser = this._userService.getUser();\r\n          this.saveUserData(loggedInUser);\r\n\r\n          // Always handle project access based on lock status\r\n          if (lockStatus) {\r\n            this.handleProjectAccess(lockStatus, project, loggedInUser);\r\n          }\r\n\r\n          // Only attempt to lock if user has edit access and we should lock\r\n          if (shouldLock && lockStatus) {\r\n            this.lockProjectOnLoad(idProject, loggedInUser?.id);\r\n          }\r\n\r\n          // Always navigate to the route with both project ID and diagram ID\r\n          // If no specific diagram ID is provided, use the first diagram in the project\r\n          const diagramId =\r\n            initialDiagramId ||\r\n            (project.diagrams.length > 0 ? project.diagrams[0].id : null);\r\n          debugger;\r\n          if (diagramId) {\r\n            // Find and set the target diagram as active\r\n            let targetDiagram = project.diagrams.find(\r\n              (d) => d.id === diagramId\r\n            );\r\n\r\n            // If not found in main diagrams, search in folders\r\n            if (!targetDiagram && project.folders) {\r\n              targetDiagram = this.findDiagramInFolders(\r\n                project.folders,\r\n                diagramId\r\n              );\r\n            }\r\n\r\n            if (targetDiagram) {\r\n              // We'll set the active diagram after setting the current project\r\n              setTimeout(() => {\r\n                this.router.navigate([\r\n                  `/editor/${project.id}/diagram/${diagramId}`,\r\n                ]);\r\n                this.diagramUtils.setActiveDiagram(targetDiagram);\r\n              }, 0);\r\n            }\r\n          } else {\r\n            // If there are no diagrams in the project, clear the active diagram\r\n            // and navigate to a placeholder URL\r\n            this.router.navigate([`/editor/${project.id}/diagram/0`]);\r\n            this.diagramUtils.setActiveDiagram(null);\r\n          }\r\n\r\n          this.setCurrentProject(project);\r\n        },\r\n      });\r\n  }\r\n\r\n  private handleProjectAccess(\r\n    lockStatus: LockResult,\r\n    project: ProjectDetails,\r\n    loggedInUser: IUser | null\r\n  ): void {\r\n    if (!lockStatus.isProjectLocked) {\r\n      this.accessService.setProjectAccess(project.accessType);\r\n      return;\r\n    }\r\n\r\n    // For users with edit access (Editor or Admin), check if they are the ones locking the project\r\n    const isCurrentUserLocking =\r\n      loggedInUser &&\r\n      lockStatus.lockingIdContact !== 0 &&\r\n      loggedInUser.id !== lockStatus.lockingIdContact;\r\n\r\n    // If the project is locked by another user, downgrade to Viewer and show a dialog\r\n    if (isCurrentUserLocking) {\r\n      this.accessService.setProjectAccess(AccessType.Viewer);\r\n\r\n      // Show dialog message about the project being locked by another user\r\n      this.translateService\r\n        .get(['errors.lockedProject.header', 'errors.lockedProject.content'], {\r\n          user: lockStatus.lockedUserName,\r\n        })\r\n        .subscribe((translations) => {\r\n          this.errorService.addError({\r\n            errorKey: 1204,\r\n            type: 'warn',\r\n            header: translations['errors.lockedProject.header'],\r\n            content: `${translations['errors.lockedProject.content']} ${lockStatus.lockedUserName}`,\r\n            isCustomError: true,\r\n          });\r\n        });\r\n    } else {\r\n      // Otherwise, keep their original access level\r\n      this.accessService.setProjectAccess(project.accessType);\r\n    }\r\n  }\r\n\r\n  private saveUserData(user: IUser | null): void {\r\n    if (!localStorage.getItem('userData') && user) {\r\n      localStorage.setItem('userData', JSON.stringify(user));\r\n    }\r\n  }\r\n\r\n  private handleError(error: any): void {\r\n    console.error('Error opening project:', error);\r\n  }\r\n\r\n  /**\r\n   * Gets the value to use for sorting based on the property name.\r\n   * @param project - The project to get the value from\r\n   * @param property - The property name to get the value for\r\n   * @returns The value to use for sorting\r\n   */\r\n  private getSortingValue(\r\n    project: ProjectWithPermission,\r\n    property: string\r\n  ): any {\r\n    switch (property) {\r\n      case 'name':\r\n      case 'description':\r\n      case 'type':\r\n      case 'productLine':\r\n      case 'admin':\r\n        return project[property]?.toLowerCase() || '';\r\n      case 'lastModifiedDate':\r\n        return new Date(project.lastModifiedDate || 0);\r\n      default:\r\n        return project[property as keyof ProjectWithPermission];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles unlocking a project if the logged-in user has access to unlock it.\r\n   *\r\n   * @param idProject - The ID of the project to unlock.\r\n   */\r\n  handleProjectUnlock(idProject: number): void {\r\n    const lockedProject = this.getLockedProject();\r\n    const loggedInUser = this._userService.getUser();\r\n    if (\r\n      lockedProject &&\r\n      loggedInUser &&\r\n      lockedProject.idContact == loggedInUser.id\r\n    ) {\r\n      this.unlockProject(idProject);\r\n    }\r\n  }\r\n  exportProject(idProject: number, formData: FormData): Observable<File> {\r\n    return this.projectApiService.exportProjectDetails(idProject, formData);\r\n  }\r\n  exportDiagramImages(formData: FormData): Observable<File> {\r\n    return this.projectApiService.exportImages(formData);\r\n  }\r\n\r\n  openProjectDialog(project?: ProjectWithPermission): void {\r\n    const dialogData = this.prepareProjectDialogData(project);\r\n    const dialogRef = this.openDialog<\r\n      ProjectDialogComponent,\r\n      ProjectDialogData,\r\n      ProjectDialogResult\r\n    >(ProjectDialogComponent, dialogData);\r\n\r\n    this.handleProjectDialogResult(dialogRef, project);\r\n  }\r\n  /**\r\n   * Prepare dialog data for project creation/editing\r\n   */\r\n  private prepareProjectDialogData(\r\n    project?: ProjectWithPermission\r\n  ): ProjectDialogData {\r\n    const isEditableProject =\r\n      project && project.accessType !== AccessType.Viewer;\r\n    return {\r\n      title: isEditableProject\r\n        ? 'dashboard.editProject'\r\n        : 'dashboard.newProject',\r\n      btnText: isEditableProject ? 'dialog.save' : 'dialog.create',\r\n      ...(isEditableProject && { project }),\r\n    };\r\n  }\r\n  /**\r\n   * Open a generic dialog with specified configuration\r\n   */\r\n  private openDialog<T, D, R>(\r\n    component: ComponentType<T>,\r\n    data: D,\r\n    options: any = {}\r\n  ): MatDialogRef<T, R> {\r\n    return this.dialog.open<T, D, R>(component, {\r\n      autoFocus: false,\r\n      ...options,\r\n      data,\r\n    });\r\n  }\r\n  /**\r\n   * Handle project dialog result\r\n   */\r\n  private handleProjectDialogResult(\r\n    dialogRef: MatDialogRef<ProjectDialogComponent, ProjectDialogResult>,\r\n    originalProject?: ProjectWithPermission\r\n  ): void {\r\n    dialogRef\r\n      .afterClosed()\r\n      .pipe(\r\n        filter(\r\n          (result): result is ProjectDialogResult =>\r\n            result !== null && result !== undefined\r\n        ),\r\n        switchMap((result) =>\r\n          this.processProjectDialogResult(result, originalProject)\r\n        )\r\n        // takeUntil(this.destroy$)\r\n      )\r\n      .subscribe();\r\n  }\r\n  /**\r\n   * Process project dialog result\r\n   */\r\n  private processProjectDialogResult(\r\n    dialogResult: ProjectDialogResult,\r\n    originalProject?: ProjectWithPermission\r\n  ): Observable<Project | null> {\r\n    this.diagramUtils.clearEnumTypeOptions();\r\n\r\n    const projectServiceCall = dialogResult.isCreation\r\n      ? this.createNewProject(dialogResult.project)\r\n      : this.updateProject(dialogResult.project);\r\n\r\n    return projectServiceCall.pipe(\r\n      tap((resultProject) => {\r\n        if (resultProject && resultProject.id) {\r\n          this.handleProjectServiceSuccess(\r\n            resultProject,\r\n            dialogResult,\r\n            originalProject\r\n          );\r\n        }\r\n      })\r\n    );\r\n  }\r\n  /**\r\n   * Handle successful project service operation\r\n   */\r\n  private handleProjectServiceSuccess(\r\n    resultProject: Project,\r\n    dialogResult: ProjectDialogResult,\r\n    originalProject?: ProjectWithPermission\r\n  ): void {\r\n    if (dialogResult.isCreation) {\r\n      // When creating a new project, open it with the first diagram (if any)\r\n      this.openProject(resultProject.id!, true);\r\n    } else if (originalProject) {\r\n      const updatedProject: ProjectWithPermission = {\r\n        ...originalProject,\r\n        ...dialogResult.project,\r\n      };\r\n      this.updateProjectInList(updatedProject);\r\n    }\r\n\r\n    this.snackBarService.openSnackbar(\r\n      dialogResult.isCreation\r\n        ? 'snackBar.projectCreationMsg'\r\n        : 'snackBar.projectUpdatedMsg'\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Recursively searches for a diagram in project folders by ID\r\n   * @param folders - Array of folders to search in\r\n   * @param diagramId - ID of the diagram to find\r\n   * @returns The found diagram or null if not found\r\n   */\r\n  private findDiagramInFolders(\r\n    folders: FolderDTO[],\r\n    diagramId: number\r\n  ): Diagram | null {\r\n    for (const folder of folders) {\r\n      // Search in current folder's diagrams\r\n      if (folder.diagrams) {\r\n        const foundDiagram = folder.diagrams.find((d) => d.id === diagramId);\r\n        if (foundDiagram) {\r\n          return foundDiagram;\r\n        }\r\n      }\r\n\r\n      // Recursively search in child folders\r\n      if (folder.childFolders && folder.childFolders.length > 0) {\r\n        const foundInChild = this.findDiagramInFolders(\r\n          folder.childFolders,\r\n          diagramId\r\n        );\r\n        if (foundInChild) {\r\n          return foundInChild;\r\n        }\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n  private updateProjectInList(updatedProject: ProjectWithPermission): void {\r\n    const projectIndex = this.projects().findIndex(\r\n      (p) => p.id === updatedProject.id\r\n    );\r\n    const filteredProjectIndex = this.filteredProjects().findIndex(\r\n      (p) => p.id === updatedProject.id\r\n    );\r\n    if (projectIndex !== -1 && filteredProjectIndex !== -1) {\r\n      this.projects()[projectIndex] = updatedProject;\r\n      this.filteredProjects()[filteredProjectIndex] = updatedProject;\r\n      this.setProjectFilterCriteria(this.getProjectFilterCriteria());\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AACA,SAASA,QAAQ,EAAcC,MAAM,QAAQ,eAAe;AAI5D,SACEC,eAAe,EACfC,UAAU,EACVC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EAERC,EAAE,EACFC,SAAS,EACTC,GAAG,QACE,MAAM;AAKb,SACEC,UAAU,QAOL,8BAA8B;AAGrC,SAASC,sBAAsB,QAAQ,0DAA0D;;;;;;;;;;;;;;AAejG,OAAM,MAAOC,cAAc;EAiDzBC,YACUC,iBAAoC,EACpCC,YAAyB,EACzBC,aAA4B,EAC5BC,eAAgC,EAChCC,MAAc,EACdC,eAAgC,EAChCC,MAAiB,EACjBC,YAA0B,EAC1BC,UAAsB,EACtBC,aAA4B,EAC5BC,YAA0B,EAC1BC,gBAAkC;IAXlC,KAAAX,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA5D1B;IACQ,KAAAC,sBAAsB,GAAG,IAAIzB,eAAe,CAClD,IAAI,CACL;IACO,KAAA0B,iBAAiB,GAAG,IAAI1B,eAAe,CAAgB,IAAI,CAAC;IAC5D,KAAA2B,qBAAqB,GAAG,IAAI3B,eAAe,CACjD,EAAE,CACH;IACO,KAAA4B,qBAAqB,GAAG,IAAI5B,eAAe,CACjD,IAAI,CACL;IAED;IACA,KAAA6B,gBAAgB,GAAG9B,MAAM,CAA0B,EAAE,CAAC;IACtD,KAAA+B,QAAQ,GAAG/B,MAAM,CAA0B,EAAE,CAAC;IAE9C;IACA,KAAAgC,SAAS,GAAGhC,MAAM,CAAU,KAAK,CAAC;IAClC,KAAAiC,iBAAiB,GAAGjC,MAAM,CAAS,CAAC,CAAC;IACrC,KAAAkC,WAAW,GAAGlC,MAAM,CAAS,CAAC,CAAC;IAC/B,KAAAmC,QAAQ,GAAGnC,MAAM,CAAS,EAAE,CAAC;IAC7B,KAAAoC,WAAW,GAAGpC,MAAM,CAAO;MAAEqC,MAAM,EAAE,kBAAkB;MAAEC,SAAS,EAAE;IAAM,CAAE,CAAC;IAE7E;IACA,KAAAC,cAAc,GAAGxC,QAAQ,CAAC,MAAK;MAC7B,MAAMyC,IAAI,GAAG,IAAI,CAACJ,WAAW,EAAE;MAC/B,IAAI,CAACI,IAAI,CAACH,MAAM,IAAI,CAACG,IAAI,CAACF,SAAS,EAAE;QACnC,OAAO,IAAI,CAACP,QAAQ,EAAE;;MAGxB,OAAO,CAAC,GAAG,IAAI,CAACA,QAAQ,EAAE,CAAC,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;QACxC,MAAMC,KAAK,GAAGH,IAAI,CAACF,SAAS,KAAK,KAAK;QACtC,MAAMM,MAAM,GAAG,IAAI,CAACC,eAAe,CAACJ,CAAC,EAAED,IAAI,CAACH,MAAM,CAAC;QACnD,MAAMS,MAAM,GAAG,IAAI,CAACD,eAAe,CAACH,CAAC,EAAEF,IAAI,CAACH,MAAM,CAAC;QAEnD,IAAIO,MAAM,GAAGE,MAAM,EAAE;UACnB,OAAOH,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;;QAEvB,IAAIC,MAAM,GAAGE,MAAM,EAAE;UACnB,OAAOH,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;QAEvB,OAAO,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACQ,KAAAI,gBAAgB,GAAY,KAAK;IA2CzC;IACQ,KAAAC,kBAAkB,GAA0B,EAAE;EA7BnD;EAEH;;;;;EAKA,IAAIC,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACF,gBAAgB;EAC9B;EAEA;;;;;EAKA,IAAIE,eAAeA,CAACC,KAAc;IAChC,IAAI,CAACH,gBAAgB,GAAGG,KAAK;EAC/B;EAEA;;;;EAIAC,oBAAoBA,CAACC,UAAyB;IAC5C,IAAI,CAACzB,iBAAiB,CAAC0B,IAAI,CAACD,UAAU,CAAC;EACzC;EAKA;;;;;EAKAE,wBAAwBA,CAACC,QAA+B;IACtD;IACA,MAAMC,aAAa,GAA0B,EAAE;IAE/C;IACA,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7CF,aAAa,CAACC,IAAI,GAAGF,QAAQ,CAACE,IAAI;;IAGpC,IAAIF,QAAQ,CAACI,WAAW,IAAIJ,QAAQ,CAACI,WAAW,CAACD,MAAM,GAAG,CAAC,EAAE;MAC3DF,aAAa,CAACG,WAAW,GAAGJ,QAAQ,CAACI,WAAW;;IAGlD;IACA,IAAIJ,QAAQ,CAACK,aAAa,KAAK,IAAI,EAAE;MACnCJ,aAAa,CAACI,aAAa,GAAG,IAAI;;IAGpC;IACA,MAAMC,eAAe,GAAGC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACf,kBAAkB,CAAC;IAC/D,MAAMgB,WAAW,GAAGF,IAAI,CAACC,SAAS,CAACP,aAAa,CAAC;IAEjD,IAAIK,eAAe,KAAKG,WAAW,EAAE;MACnC,IAAI,CAAChB,kBAAkB,GAAG;QAAE,GAAGQ;MAAa,CAAE;MAC9C,IAAI,CAAC5B,qBAAqB,CAACyB,IAAI,CAACG,aAAa,CAAC;MAE9C;MACA,IAAI,CAACtB,WAAW,CAAC+B,GAAG,CAAC,CAAC,CAAC;;EAE3B;EAEA;;;;EAIAC,4BAA4BA,CAAA;IAC1B,OAAO,IAAI,CAACtC,qBAAqB,CAACuC,YAAY,EAAE;EAClD;EAEAC,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACxC,qBAAqB,CAACyC,QAAQ,EAAE;EAC9C;EAEA;;;;EAIAC,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3C,iBAAiB,CAACwC,YAAY,EAAE;EAC9C;EAEA;;;;;;EAMAI,iBAAiBA,CAACC,OAAuB;IACvCA,OAAO,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAACC,GAAG,CAAEC,OAAO,IAAI;MAClD,OAAO;QAAE,GAAGA,OAAO;QAAEC,SAAS,EAAEJ,OAAO,CAACK;MAAG,CAAE;IAC/C,CAAC,CAAC;IACF,IAAI,CAACvD,UAAU,CAACwD,gBAAgB,CAACb,GAAG,CAACO,OAAO,CAACM,gBAAgB,CAAC;IAC9D,IAAI,CAAC7D,eAAe,CAAC8D,iBAAiB,CAACP,OAAO,CAAC;IAC/C,IAAI,CAAC9C,sBAAsB,CAAC2B,IAAI,CAACmB,OAAO,CAAC;EAC3C;EAEA;;;;EAIAQ,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACtD,sBAAsB,CAACyC,YAAY,EAAE;EACnD;EAEA;;;;EAIAc,gBAAgBA,CAACC,IAAyB;IACxC,IAAI,CAACrD,qBAAqB,CAACwB,IAAI,CAAC6B,IAAI,CAAC;EACvC;EAEA;;;;EAIAC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACtD,qBAAqB,CAACwC,QAAQ,EAAE;EAC9C;EAEA;;;;;;;EAOAe,gBAAgBA,CAACZ,OAAgB;IAC/B,IAAI,CAACxC,SAAS,CAACiC,GAAG,CAAC,IAAI,CAAC;IACxB,OAAO,IAAI,CAACnD,iBAAiB,CAACuE,aAAa,CAACb,OAAO,CAAC,CAACc,IAAI,CACvD7E,GAAG,CAAE8E,cAAc,IAAI;MACrB;MACA,IAAIA,cAAc,IAAIA,cAAc,CAACV,EAAE,EAAE;QACvC;QACA,MAAMW,UAAU,GAAGD,cAAkD;QAErE;QACA,MAAME,eAAe,GAAG,IAAI,CAAC1D,QAAQ,EAAE;QACvC,IAAI,CAACA,QAAQ,CAACkC,GAAG,CAAC,CAACuB,UAAU,EAAE,GAAGC,eAAe,CAAC,CAAC;QAEnD;QACA,MAAMC,uBAAuB,GAAG,IAAI,CAAC5D,gBAAgB,EAAE;QACvD,IAAI,CAACA,gBAAgB,CAACmC,GAAG,CAAC,CAACuB,UAAU,EAAE,GAAGE,uBAAuB,CAAC,CAAC;QAEnE;QACA,IAAI,CAACzD,iBAAiB,CAACgC,GAAG,CAAC,IAAI,CAAChC,iBAAiB,EAAE,GAAG,CAAC,CAAC;;IAE5D,CAAC,CAAC,EACF5B,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2B,SAAS,CAACiC,GAAG,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOA0B,aAAaA,CAACnB,OAAgB;IAC5B,IAAI,CAACxC,SAAS,CAACiC,GAAG,CAAC,IAAI,CAAC;IACxB,OAAO,IAAI,CAACnD,iBAAiB,CAAC6E,aAAa,CAACnB,OAAO,CAAC,CAACc,IAAI,CACvD7E,GAAG,CAAEmF,cAAc,IAAI;MACrB,IAAIA,cAAc,IAAIA,cAAc,CAACf,EAAE,EAAE;QACvC;QACA,MAAMgB,qBAAqB,GACzBD,cAAkD;QAEpD;QACA,MAAMH,eAAe,GAAG,IAAI,CAAC1D,QAAQ,EAAE;QACvC,MAAM+D,eAAe,GAAGL,eAAe,CAACf,GAAG,CAAEqB,CAAC,IAC5CA,CAAC,CAAClB,EAAE,KAAKgB,qBAAqB,CAAChB,EAAE,GAC7B;UAAE,GAAGkB,CAAC;UAAE,GAAGF;QAAqB,CAAE,GAClCE,CAAC,CACN;QACD,IAAI,CAAChE,QAAQ,CAACkC,GAAG,CAAC6B,eAAe,CAAC;QAElC;QACA,MAAMJ,uBAAuB,GAAG,IAAI,CAAC5D,gBAAgB,EAAE;QACvD,MAAMkE,uBAAuB,GAAGN,uBAAuB,CAAChB,GAAG,CAAEqB,CAAC,IAC5DA,CAAC,CAAClB,EAAE,KAAKgB,qBAAqB,CAAChB,EAAE,GAC7B;UAAE,GAAGkB,CAAC;UAAE,GAAGF;QAAqB,CAAE,GAClCE,CAAC,CACN;QACD,IAAI,CAACjE,gBAAgB,CAACmC,GAAG,CAAC+B,uBAAuB,CAAC;;IAEtD,CAAC,CAAC,EACF3F,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2B,SAAS,CAACiC,GAAG,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;EAOAgC,aAAaA,CAACC,SAAiB;IAC7B,IAAI,CAAClE,SAAS,CAACiC,GAAG,CAAC,IAAI,CAAC;IACxB,OAAO,IAAI,CAACnD,iBAAiB,CAACmF,aAAa,CAACC,SAAS,CAAC,CAACZ,IAAI,CACzD7E,GAAG,CAAC,MAAK;MACP;MACA,MAAMgF,eAAe,GAAG,IAAI,CAAC1D,QAAQ,EAAE;MACvC,MAAM+D,eAAe,GAAGL,eAAe,CAACrF,MAAM,CAC3C2F,CAAC,IAAKA,CAAC,CAAClB,EAAE,KAAKqB,SAAS,CAC1B;MACD,IAAI,CAACnE,QAAQ,CAACkC,GAAG,CAAC6B,eAAe,CAAC;MAElC;MACA,MAAMJ,uBAAuB,GAAG,IAAI,CAAC5D,gBAAgB,EAAE;MACvD,MAAMkE,uBAAuB,GAAGN,uBAAuB,CAACtF,MAAM,CAC3D2F,CAAC,IAAKA,CAAC,CAAClB,EAAE,KAAKqB,SAAS,CAC1B;MACD,IAAI,CAACpE,gBAAgB,CAACmC,GAAG,CAAC+B,uBAAuB,CAAC;MAElD;MACA,IAAI,CAAC/D,iBAAiB,CAACgC,GAAG,CAACkC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnE,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC,EACF5B,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2B,SAAS,CAACiC,GAAG,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACH;EACH;EAEA;;;;;;;;;;;;;EAaAoC,WAAWA,CACTlE,QAAgB,EAChBD,WAAmB,EACnBoE,MAAe,EACfC,cAAA,GAA0B,IAAI,EAC9BnD,UAAmB,EACnBoD,cAAsC,EACtCC,eAAA,GAA2B,KAAK;IAEhC;IACA,IAAI,CAACzE,SAAS,CAACiC,GAAG,CAAC,IAAI,CAAC;IACxB,IAAI,CAAC9B,QAAQ,CAAC8B,GAAG,CAAC9B,QAAQ,CAAC;IAC3B,IAAI,CAACD,WAAW,CAAC+B,GAAG,CAAC/B,WAAW,CAAC;IAEjC,IAAIoE,MAAM,EAAE;MACV,IAAI,CAAClE,WAAW,CAAC6B,GAAG,CAAC;QACnB5B,MAAM,EAAEiE,MAAM;QACdhE,SAAS,EAAEiE,cAAc,GAAG,MAAM,GAAG;OACtC,CAAC;;IAGJ,OAAO,IAAI,CAACzF,iBAAiB,CAC1BuF,WAAW,CACVlE,QAAQ,EACRD,WAAW,EACXoE,MAAM,EACNC,cAAc,EACdnD,UAAU,EACVoD,cAAc,CACf,CACAlB,IAAI,CACH7E,GAAG,CAAEiG,QAAQ,IAAI;MACf;MACA,MAAMC,gBAAgB,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAC7D,IAAIF,gBAAgB,EAAE;QACpB,MAAMG,UAAU,GAAGhD,IAAI,CAACiD,KAAK,CAACJ,gBAAgB,CAAC;QAC/C,IAAI,CAAC1E,iBAAiB,CAACgC,GAAG,CAAC6C,UAAU,CAACE,YAAY,CAAC;;MAGrD;MACA,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACS,IAAI,CAAC,EAAE;QAChC,MAAMC,aAAa,GAAGV,QAAQ,CAACS,IAAI,CAAC/G,MAAM,CACvCoE,OAAO,IAAKA,OAAO,IAAI,IAAI,CAC7B;QAED,IAAIiC,eAAe,EAAE;UACnB;UACA,MAAMhB,eAAe,GAAG,IAAI,CAAC1D,QAAQ,EAAE;UACvC,MAAMsF,WAAW,GAAG,CAAC,GAAG5B,eAAe,CAAC;UAExC;UACA,MAAM6B,UAAU,GAAG,CAACpF,WAAW,GAAG,CAAC,IAAIC,QAAQ;UAC/C,KAAK,IAAIoF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,aAAa,CAAC1D,MAAM,EAAE6D,CAAC,EAAE,EAAE;YAC7C,IAAID,UAAU,GAAGC,CAAC,GAAGF,WAAW,CAAC3D,MAAM,EAAE;cACvC2D,WAAW,CAACC,UAAU,GAAGC,CAAC,CAAC,GAAGH,aAAa,CAACG,CAAC,CAAC;aAC/C,MAAM;cACLF,WAAW,CAACG,IAAI,CAACJ,aAAa,CAACG,CAAC,CAAC,CAAC;;;UAItC,IAAI,CAACxF,QAAQ,CAACkC,GAAG,CAACoD,WAAW,CAAC;UAC9B,IAAI,CAACvF,gBAAgB,CAACmC,GAAG,CAAC,CAAC,GAAGoD,WAAW,CAAC,CAAC;SAC5C,MAAM;UACL;UACA,IAAI,CAACtF,QAAQ,CAACkC,GAAG,CAACmD,aAAa,CAAC;UAChC,IAAI,CAACtF,gBAAgB,CAACmC,GAAG,CAAC,CAAC,GAAGmD,aAAa,CAAC,CAAC;;;IAGnD,CAAC,CAAC,EACF/G,QAAQ,CAAC,MAAK;MACZ,IAAI,CAAC2B,SAAS,CAACiC,GAAG,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,CACH;EACL;EAEA;;;;;;EAMAwD,cAAcA,CAAC7C,SAAiB;IAC9B,OAAO,IAAI,CAAC9D,iBAAiB,CAAC2G,cAAc,CAAC7C,SAAS,CAAC;EACzD;EAEA;;;;;;;EAOA8C,cAAcA,CAACtE,UAAkB;IAC/B,IAAIA,UAAU,KAAK,EAAE,EAAE;MACrB,MAAMuE,WAAW,GAAG,IAAI,CAAC5F,QAAQ,EAAE;MACnC,MAAM6F,QAAQ,GAAGD,WAAW,CAACvH,MAAM,CAAEoE,OAAO,IAAI;QAC9C,MAAMqD,eAAe,GAAGzE,UAAU,CAAC0E,WAAW,EAAE;QAChD,OACEtD,OAAO,CAACuD,IAAI,EAAED,WAAW,EAAE,CAACE,QAAQ,CAACH,eAAe,CAAC,IACrDrD,OAAO,CAACyD,WAAW,EAAEH,WAAW,EAAE,CAACE,QAAQ,CAACH,eAAe,CAAC,IAC5DrD,OAAO,CAACf,IAAI,EAAEqE,WAAW,EAAE,CAACE,QAAQ,CAACH,eAAe,CAAC,IACrDrD,OAAO,CAACb,WAAW,EAAEmE,WAAW,EAAE,CAACE,QAAQ,CAACH,eAAe,CAAC,IAC5DrD,OAAO,CAAC0D,KAAK,EAAEJ,WAAW,EAAE,CAACE,QAAQ,CAACH,eAAe,CAAC;MAE1D,CAAC,CAAC;MACF,IAAI,CAAC/F,gBAAgB,CAACmC,GAAG,CAAC2D,QAAQ,CAAC;KACpC,MAAM;MACL;MACA,IAAI,CAAC9F,gBAAgB,CAACmC,GAAG,CAAC,IAAI,CAAClC,QAAQ,EAAE,CAAC;;EAE9C;EAEA;;;;;;EAMAoG,YAAYA,CAAC3F,IAAU;IACrB,IAAI,CAACJ,WAAW,CAAC6B,GAAG,CAACzB,IAAI,CAAC;IAC1B;EACF;EAEA;;;;;;EAMA4F,YAAYA,CAAA;IACV,MAAMC,QAAQ,GAAG,IAAI,CAACnG,WAAW,EAAE,GAAG,CAAC;IACvC,OAAO,IAAI,CAACmE,WAAW,CACrB,IAAI,CAAClE,QAAQ,EAAE,EACfkG,QAAQ,EACR,IAAI,CAACjG,WAAW,EAAE,CAACC,MAAM,EACzB,IAAI,CAACD,WAAW,EAAE,CAACE,SAAS,KAAK,MAAM,EACvC,IAAI,CAACX,iBAAiB,CAAC0C,QAAQ,EAAE,IAAIiE,SAAS,EAC9C,IAAI,CAAClE,wBAAwB,EAAE,EAC/B,IAAI,CAAC;KACN;EACH;EAEA;;;;;;EAMAmE,gBAAgBA,CAAA;IACd,MAAMC,QAAQ,GAAGrC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClE,WAAW,EAAE,GAAG,CAAC,CAAC;IACpD,OAAO,IAAI,CAACmE,WAAW,CACrB,IAAI,CAAClE,QAAQ,EAAE,EACfqG,QAAQ,EACR,IAAI,CAACpG,WAAW,EAAE,CAACC,MAAM,EACzB,IAAI,CAACD,WAAW,EAAE,CAACE,SAAS,KAAK,MAAM,EACvC,IAAI,CAACX,iBAAiB,CAAC0C,QAAQ,EAAE,IAAIiE,SAAS,EAC9C,IAAI,CAAClE,wBAAwB,EAAE,EAC/B,IAAI,CAAC;KACN;EACH;EAEA;;;;;;EAMAqE,eAAeA,CAAA;IACb;IACA,IAAI,CAACzG,SAAS,CAACiC,GAAG,CAAC,IAAI,CAAC;IAExB;IACA,MAAMb,UAAU,GAAG,IAAI,CAACzB,iBAAiB,CAAC0C,QAAQ,EAAE,IAAIiE,SAAS;IAEjE,OAAO,IAAI,CAACjC,WAAW,CACrB,IAAI,CAAClE,QAAQ,EAAE,EACf,IAAI,CAACD,WAAW,EAAE,EAClB,IAAI,CAACE,WAAW,EAAE,CAACC,MAAM,EACzB,IAAI,CAACD,WAAW,EAAE,CAACE,SAAS,KAAK,MAAM,EACvCc,UAAU,EACV,IAAI,CAACgB,wBAAwB,EAAE,EAC/B,KAAK,CAAC;KACP;EACH;EAEA;;;;;EAKAsE,WAAWA,CAACC,OAAqB;IAC/B,IAAI,CAAC7H,iBAAiB,CAAC4H,WAAW,CAACC,OAAO,CAAC,CAACC,SAAS,CAAC;MACpDvF,IAAI,EAAGwF,aAAa,IAAI;QACtB,IAAI,CAAC5D,gBAAgB,CAAC4D,aAAa,CAAC;QACpC,IAAI,CAAC9F,gBAAgB,GAAG,IAAI;QAC5B,IAAI4F,OAAO,CAACG,SAAS,IAAID,aAAa,CAACC,SAAS,EAC9C,IAAI,CAACrH,gBAAgB,CAClBoF,GAAG,CACF,CAAC,6BAA6B,EAAE,8BAA8B,CAAC,EAC/D;UAAEkC,IAAI,EAAEF,aAAa,CAACG;QAAc,CAAE,CACvC,CACAJ,SAAS,CAAEK,YAAY,IAAI;UAC1B,IAAI,CAACzH,YAAY,CAAC0H,QAAQ,CAAC;YACzBC,QAAQ,EAAE,IAAI;YACd1F,IAAI,EAAE,MAAM;YACZ2F,MAAM,EAAEH,YAAY,CAAC,6BAA6B,CAAC;YACnDI,OAAO,EAAE,GAAGJ,YAAY,CAAC,8BAA8B,CAAC,IAAIJ,aAAa,CAACG,cAAc,EAAE;YAC1FM,aAAa,EAAE;WAChB,CAAC;QACJ,CAAC,CAAC;MACR,CAAC;MACDC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACtE,gBAAgB,CAAC,IAAI,CAAC;MAC7B;KACD,CAAC;EACJ;EAEA;;;;;EAKAuE,aAAaA,CAAC5E,SAAiB;IAC7B,IAAI,CAAC9D,iBAAiB,CAAC0I,aAAa,CAAC5E,SAAS,CAAC,CAACgE,SAAS,CAAC,MAAK;MAC7D,IAAI,CAAC7F,gBAAgB,GAAG,KAAK;IAC/B,CAAC,CAAC;EACJ;EAEA;;;;;;EAMA0G,iBAAiBA,CAAC7E,SAAiB,EAAE8E,MAAe;IAClD,IAAIA,MAAM,EAAE;MACV,IAAI,CAAChB,WAAW,CAAC;QACf9D,SAAS,EAAEA,SAAS;QACpBkE,SAAS,EAAEY;OACZ,CAAC;KACH,MAAM;MACL,IAAI,CAACxI,MAAM,CAACyI,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;;;;EAMAC,kBAAkBA,CAAChF,SAAiB;IAClC,OAAO,IAAI,CAAC9D,iBAAiB,CAACmC,eAAe,CAAC2B,SAAS,CAAC;EAC1D;EAEA;;;;;;;EAOAiF,WAAWA,CACTjF,SAAiB,EACjBkF,MAAA,GAAkB,KAAK,EACvBC,gBAAyB;IAEzB,IAAI,CAAC/I,aAAa,CAACgJ,UAAU,EAAE;IAC/B,IAAIC,QAAQ,GAAG,IAAI,CAACxC,cAAc,CAAC7C,SAAS,CAAC,CAAC,CAAC;IAE/C;IACAqF,QAAQ,CACL3E,IAAI,CACH9E,SAAS,CAAEgE,OAAO,IAAI;MACpB;MACA,MAAM0F,WAAW,GAAG,IAAI,CAACN,kBAAkB,CAAChF,SAAS,CAAC;MAEtD;MACA,MAAMuF,UAAU,GAAGL,MAAM,IAAItF,OAAO,CAAC4F,UAAU,KAAK1J,UAAU,CAAC2J,MAAM;MAErE,OAAO/J,QAAQ,CAAC;QACdkE,OAAO,EAAEjE,EAAE,CAACiE,OAAO,CAAC;QACpB8F,UAAU,EAAEJ,WAAW;QACvBC,UAAU,EAAE5J,EAAE,CAAC4J,UAAU;OAC1B,CAAC;IACJ,CAAC,CAAC,EACF9J,QAAQ,CAAC,MAAM,IAAI,CAACW,aAAa,CAACuJ,UAAU,EAAE,CAAC,EAC/CrK,UAAU,CAAEqJ,KAAK,IAAI;MACnB,IAAI,CAACiB,WAAW,CAACjB,KAAK,CAAC;MACvB,OAAOpJ,KAAK;IACd,CAAC,CAAC,CACH,CACAyI,SAAS,CAAC;MACTvF,IAAI,EAAEA,CAAC;QAAEmB,OAAO;QAAE8F,UAAU;QAAEH;MAAU,CAAE,KAAI;QAC5C;QACA,IAAI,CAAC5I,aAAa,CAACkJ,gBAAgB,CAACjG,OAAO,CAAC4F,UAAU,CAAC;QAEvD,MAAMM,YAAY,GAAG,IAAI,CAAC3J,YAAY,CAAC4J,OAAO,EAAE;QAChD,IAAI,CAACC,YAAY,CAACF,YAAY,CAAC;QAE/B;QACA,IAAIJ,UAAU,EAAE;UACd,IAAI,CAACO,mBAAmB,CAACP,UAAU,EAAE9F,OAAO,EAAEkG,YAAY,CAAC;;QAG7D;QACA,IAAIP,UAAU,IAAIG,UAAU,EAAE;UAC5B,IAAI,CAACb,iBAAiB,CAAC7E,SAAS,EAAE8F,YAAY,EAAE7F,EAAE,CAAC;;QAGrD;QACA;QACA,MAAMiG,SAAS,GACbf,gBAAgB,KACfvF,OAAO,CAACC,QAAQ,CAACf,MAAM,GAAG,CAAC,GAAGc,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACI,EAAE,GAAG,IAAI,CAAC;QAC/D;QACA,IAAIiG,SAAS,EAAE;UACb;UACA,IAAIC,aAAa,GAAGvG,OAAO,CAACC,QAAQ,CAACuG,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAKiG,SAAS,CAC1B;UAED;UACA,IAAI,CAACC,aAAa,IAAIvG,OAAO,CAAC0G,OAAO,EAAE;YACrCH,aAAa,GAAG,IAAI,CAACI,oBAAoB,CACvC3G,OAAO,CAAC0G,OAAO,EACfJ,SAAS,CACV;;UAGH,IAAIC,aAAa,EAAE;YACjB;YACAK,UAAU,CAAC,MAAK;cACd,IAAI,CAAClK,MAAM,CAACyI,QAAQ,CAAC,CACnB,WAAWnF,OAAO,CAACK,EAAE,YAAYiG,SAAS,EAAE,CAC7C,CAAC;cACF,IAAI,CAACzJ,YAAY,CAACgK,gBAAgB,CAACN,aAAa,CAAC;YACnD,CAAC,EAAE,CAAC,CAAC;;SAER,MAAM;UACL;UACA;UACA,IAAI,CAAC7J,MAAM,CAACyI,QAAQ,CAAC,CAAC,WAAWnF,OAAO,CAACK,EAAE,YAAY,CAAC,CAAC;UACzD,IAAI,CAACxD,YAAY,CAACgK,gBAAgB,CAAC,IAAI,CAAC;;QAG1C,IAAI,CAAC9G,iBAAiB,CAACC,OAAO,CAAC;MACjC;KACD,CAAC;EACN;EAEQqG,mBAAmBA,CACzBP,UAAsB,EACtB9F,OAAuB,EACvBkG,YAA0B;IAE1B,IAAI,CAACJ,UAAU,CAACrH,eAAe,EAAE;MAC/B,IAAI,CAAC1B,aAAa,CAACkJ,gBAAgB,CAACjG,OAAO,CAAC4F,UAAU,CAAC;MACvD;;IAGF;IACA,MAAMkB,oBAAoB,GACxBZ,YAAY,IACZJ,UAAU,CAACiB,gBAAgB,KAAK,CAAC,IACjCb,YAAY,CAAC7F,EAAE,KAAKyF,UAAU,CAACiB,gBAAgB;IAEjD;IACA,IAAID,oBAAoB,EAAE;MACxB,IAAI,CAAC/J,aAAa,CAACkJ,gBAAgB,CAAC/J,UAAU,CAAC2J,MAAM,CAAC;MAEtD;MACA,IAAI,CAAC5I,gBAAgB,CAClBoF,GAAG,CAAC,CAAC,6BAA6B,EAAE,8BAA8B,CAAC,EAAE;QACpEkC,IAAI,EAAEuB,UAAU,CAACtB;OAClB,CAAC,CACDJ,SAAS,CAAEK,YAAY,IAAI;QAC1B,IAAI,CAACzH,YAAY,CAAC0H,QAAQ,CAAC;UACzBC,QAAQ,EAAE,IAAI;UACd1F,IAAI,EAAE,MAAM;UACZ2F,MAAM,EAAEH,YAAY,CAAC,6BAA6B,CAAC;UACnDI,OAAO,EAAE,GAAGJ,YAAY,CAAC,8BAA8B,CAAC,IAAIqB,UAAU,CAACtB,cAAc,EAAE;UACvFM,aAAa,EAAE;SAChB,CAAC;MACJ,CAAC,CAAC;KACL,MAAM;MACL;MACA,IAAI,CAAC/H,aAAa,CAACkJ,gBAAgB,CAACjG,OAAO,CAAC4F,UAAU,CAAC;;EAE3D;EAEQQ,YAAYA,CAAC7B,IAAkB;IACrC,IAAI,CAACyC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI1C,IAAI,EAAE;MAC7CyC,YAAY,CAACE,OAAO,CAAC,UAAU,EAAE5H,IAAI,CAACC,SAAS,CAACgF,IAAI,CAAC,CAAC;;EAE1D;EAEQyB,WAAWA,CAACjB,KAAU;IAC5BoC,OAAO,CAACpC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;EAChD;EAEA;;;;;;EAMQ1G,eAAeA,CACrB2B,OAA8B,EAC9BoH,QAAgB;IAEhB,QAAQA,QAAQ;MACd,KAAK,MAAM;MACX,KAAK,aAAa;MAClB,KAAK,MAAM;MACX,KAAK,aAAa;MAClB,KAAK,OAAO;QACV,OAAOpH,OAAO,CAACoH,QAAQ,CAAC,EAAE9D,WAAW,EAAE,IAAI,EAAE;MAC/C,KAAK,kBAAkB;QACrB,OAAO,IAAI+D,IAAI,CAACrH,OAAO,CAACM,gBAAgB,IAAI,CAAC,CAAC;MAChD;QACE,OAAON,OAAO,CAACoH,QAAuC,CAAC;;EAE7D;EAEA;;;;;EAKAE,mBAAmBA,CAAClH,SAAiB;IACnC,MAAMiE,aAAa,GAAG,IAAI,CAAC1D,gBAAgB,EAAE;IAC7C,MAAMuF,YAAY,GAAG,IAAI,CAAC3J,YAAY,CAAC4J,OAAO,EAAE;IAChD,IACE9B,aAAa,IACb6B,YAAY,IACZ7B,aAAa,CAACC,SAAS,IAAI4B,YAAY,CAAC7F,EAAE,EAC1C;MACA,IAAI,CAAC2E,aAAa,CAAC5E,SAAS,CAAC;;EAEjC;EACAmH,aAAaA,CAACnH,SAAiB,EAAEoH,QAAkB;IACjD,OAAO,IAAI,CAAClL,iBAAiB,CAACmL,oBAAoB,CAACrH,SAAS,EAAEoH,QAAQ,CAAC;EACzE;EACAE,mBAAmBA,CAACF,QAAkB;IACpC,OAAO,IAAI,CAAClL,iBAAiB,CAACqL,YAAY,CAACH,QAAQ,CAAC;EACtD;EAEAI,iBAAiBA,CAAC5H,OAA+B;IAC/C,MAAM6H,UAAU,GAAG,IAAI,CAACC,wBAAwB,CAAC9H,OAAO,CAAC;IACzD,MAAM+H,SAAS,GAAG,IAAI,CAACC,UAAU,CAI/B7L,sBAAsB,EAAE0L,UAAU,CAAC;IAErC,IAAI,CAACI,yBAAyB,CAACF,SAAS,EAAE/H,OAAO,CAAC;EACpD;EACA;;;EAGQ8H,wBAAwBA,CAC9B9H,OAA+B;IAE/B,MAAMkI,iBAAiB,GACrBlI,OAAO,IAAIA,OAAO,CAAC4F,UAAU,KAAK1J,UAAU,CAAC2J,MAAM;IACrD,OAAO;MACLsC,KAAK,EAAED,iBAAiB,GACpB,uBAAuB,GACvB,sBAAsB;MAC1BE,OAAO,EAAEF,iBAAiB,GAAG,aAAa,GAAG,eAAe;MAC5D,IAAIA,iBAAiB,IAAI;QAAElI;MAAO,CAAE;KACrC;EACH;EACA;;;EAGQgI,UAAUA,CAChBK,SAA2B,EAC3BC,IAAO,EACPC,OAAA,GAAe,EAAE;IAEjB,OAAO,IAAI,CAAC3L,MAAM,CAAC4L,IAAI,CAAUH,SAAS,EAAE;MAC1CI,SAAS,EAAE,KAAK;MAChB,GAAGF,OAAO;MACVD;KACD,CAAC;EACJ;EACA;;;EAGQL,yBAAyBA,CAC/BF,SAAoE,EACpEW,eAAuC;IAEvCX,SAAS,CACNY,WAAW,EAAE,CACb7H,IAAI,CACHlF,MAAM,CACHgN,MAAM,IACLA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK9E,SAAS,CAC1C,EACD9H,SAAS,CAAE4M,MAAM,IACf,IAAI,CAACC,0BAA0B,CAACD,MAAM,EAAEF,eAAe,CAAC;IAE1D;KACD,CACAtE,SAAS,EAAE;EAChB;EACA;;;EAGQyE,0BAA0BA,CAChCC,YAAiC,EACjCJ,eAAuC;IAEvC,IAAI,CAAC7L,YAAY,CAACkM,oBAAoB,EAAE;IAExC,MAAMC,kBAAkB,GAAGF,YAAY,CAACG,UAAU,GAC9C,IAAI,CAACrI,gBAAgB,CAACkI,YAAY,CAAC9I,OAAO,CAAC,GAC3C,IAAI,CAACmB,aAAa,CAAC2H,YAAY,CAAC9I,OAAO,CAAC;IAE5C,OAAOgJ,kBAAkB,CAAClI,IAAI,CAC5B7E,GAAG,CAAEiN,aAAa,IAAI;MACpB,IAAIA,aAAa,IAAIA,aAAa,CAAC7I,EAAE,EAAE;QACrC,IAAI,CAAC8I,2BAA2B,CAC9BD,aAAa,EACbJ,YAAY,EACZJ,eAAe,CAChB;;IAEL,CAAC,CAAC,CACH;EACH;EACA;;;EAGQS,2BAA2BA,CACjCD,aAAsB,EACtBJ,YAAiC,EACjCJ,eAAuC;IAEvC,IAAII,YAAY,CAACG,UAAU,EAAE;MAC3B;MACA,IAAI,CAAC5D,WAAW,CAAC6D,aAAa,CAAC7I,EAAG,EAAE,IAAI,CAAC;KAC1C,MAAM,IAAIqI,eAAe,EAAE;MAC1B,MAAMtH,cAAc,GAA0B;QAC5C,GAAGsH,eAAe;QAClB,GAAGI,YAAY,CAAC9I;OACjB;MACD,IAAI,CAACoJ,mBAAmB,CAAChI,cAAc,CAAC;;IAG1C,IAAI,CAACzE,eAAe,CAAC0M,YAAY,CAC/BP,YAAY,CAACG,UAAU,GACnB,6BAA6B,GAC7B,4BAA4B,CACjC;EACH;EAEA;;;;;;EAMQtC,oBAAoBA,CAC1BD,OAAoB,EACpBJ,SAAiB;IAEjB,KAAK,MAAMgD,MAAM,IAAI5C,OAAO,EAAE;MAC5B;MACA,IAAI4C,MAAM,CAACrJ,QAAQ,EAAE;QACnB,MAAMsJ,YAAY,GAAGD,MAAM,CAACrJ,QAAQ,CAACuG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACpG,EAAE,KAAKiG,SAAS,CAAC;QACpE,IAAIiD,YAAY,EAAE;UAChB,OAAOA,YAAY;;;MAIvB;MACA,IAAID,MAAM,CAACE,YAAY,IAAIF,MAAM,CAACE,YAAY,CAACtK,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMuK,YAAY,GAAG,IAAI,CAAC9C,oBAAoB,CAC5C2C,MAAM,CAACE,YAAY,EACnBlD,SAAS,CACV;QACD,IAAImD,YAAY,EAAE;UAChB,OAAOA,YAAY;;;;IAIzB,OAAO,IAAI;EACb;EACQL,mBAAmBA,CAAChI,cAAqC;IAC/D,MAAMsI,YAAY,GAAG,IAAI,CAACnM,QAAQ,EAAE,CAACoM,SAAS,CAC3CpI,CAAC,IAAKA,CAAC,CAAClB,EAAE,KAAKe,cAAc,CAACf,EAAE,CAClC;IACD,MAAMuJ,oBAAoB,GAAG,IAAI,CAACtM,gBAAgB,EAAE,CAACqM,SAAS,CAC3DpI,CAAC,IAAKA,CAAC,CAAClB,EAAE,KAAKe,cAAc,CAACf,EAAE,CAClC;IACD,IAAIqJ,YAAY,KAAK,CAAC,CAAC,IAAIE,oBAAoB,KAAK,CAAC,CAAC,EAAE;MACtD,IAAI,CAACrM,QAAQ,EAAE,CAACmM,YAAY,CAAC,GAAGtI,cAAc;MAC9C,IAAI,CAAC9D,gBAAgB,EAAE,CAACsM,oBAAoB,CAAC,GAAGxI,cAAc;MAC9D,IAAI,CAACtC,wBAAwB,CAAC,IAAI,CAACc,wBAAwB,EAAE,CAAC;;EAElE;EAAC,QAAAiK,CAAA,G;qBAl5BUzN,cAAc,EAAA0N,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,iBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,MAAA,GAAAX,EAAA,CAAAC,QAAA,CAAAW,EAAA,CAAAC,eAAA,GAAAb,EAAA,CAAAC,QAAA,CAAAa,EAAA,CAAAC,SAAA,GAAAf,EAAA,CAAAC,QAAA,CAAAe,EAAA,CAAAC,YAAA,GAAAjB,EAAA,CAAAC,QAAA,CAAAiB,EAAA,CAAAC,UAAA,GAAAnB,EAAA,CAAAC,QAAA,CAAAmB,GAAA,CAAAC,aAAA,GAAArB,EAAA,CAAAC,QAAA,CAAAqB,GAAA,CAAAC,YAAA,GAAAvB,EAAA,CAAAC,QAAA,CAAAuB,GAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdpP,cAAc;IAAAqP,OAAA,EAAdrP,cAAc,CAAAsP,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}