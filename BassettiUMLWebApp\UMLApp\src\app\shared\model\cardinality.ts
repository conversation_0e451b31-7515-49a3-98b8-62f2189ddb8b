import { DiagramIdentifiable, Identifiable, NamedEntity } from './common';

/**
 * Enum representing different types of link cardinality constraints between entities.
 * Cardinality specifies how many instances of one entity can be associated with instances of another entity.
 */
export enum CardinalityType {
  /**
   * Zero or one instance of the source entity may be associated with zero or one instance of the target entity,
   * and vice versa.
   *
   */
  ZeroOrOneToZeroOrOne,

  /**
   * Zero or one instance of the source entity may be associated with zero or one instance of the target entity,
   * while each instance of the target entity must be associated with exactly one instance of the source entity.
   *
   */
  ZeroOrOneToOne,

  /**
   * Zero or one instance of the source entity may be associated with zero or one instance of the target entity,
   * while the target entity may be associated with multiple instances of the source entity.
   *
   */
  ZeroOrOneToMany,

  /**
   * Zero or one instance of the source entity may be associated with zero or one instance of the target entity,
   * while the target entity may be associated with zero or more instances of the source entity.
   *
   */
  ZeroOrOneToOneOrMany,

  /**
   * Each instance of the source entity must be associated with exactly one instance of the target entity,
   * while the target entity may be associated with zero or one instance of the source entity.
   *
   */
  OneToZeroOrOne,

  /**
   * Each instance of the source entity must be associated with exactly one instance of the target entity,
   * and each instance of the target entity must be associated with exactly one instance of the source entity.
   *
   */
  OneToOne,

  /**
   * Each instance of the source entity must be associated with one or more instances of the target entity,
   * while each instance of the target entity is connected to exactly one instance of the source entity.
   *
   */
  OneToMany,

  /**
   * Each instance of the source entity must be associated with one or more instances of the target entity,
   * and each instance of the target entity may be associated with one or more instances of the source entity.
   *
   */
  OneToOneOrMany,

  /**
   * Multiple instances of the source entity may be associated with zero or one instance of the target entity,
   * while each instance of the target entity is associated with zero or one instance of the source entity.
   *
   */
  ManyToZeroOrOne,

  /**
   * Multiple instances of the source entity must be associated with exactly one instance of the target entity,
   * while each instance of the target entity may be associated with one or more instances of the source entity.
   *
   */
  ManyToOne,

  /**
   * Multiple instances of the source entity may be associated with multiple instances of the target entity,
   * and vice versa.
   *
   */
  ManyToMany,

  /**
   * Multiple instances of the source entity may be associated with one or more instances of the target entity,
   * and the target entity may be associated with one or more instances of the source entity.
   *
   */
  ManyToOneOrMany,

  /**
   * One or more instances of the source entity may be associated with zero or one instance of the target entity,
   * while each instance of the target entity is associated with zero or one instance of the source entity.
   *
   */
  OneOrManyToZeroOrOne,

  /**
   * One or more instances of the source entity must be associated with exactly one instance of the target entity,
   * while each instance of the target entity may be associated with one or more instances of the source entity.
   *
   */
  OneOrManyToOne,

  /**
   * One or more instances of the source entity may be associated with multiple instances of the target entity,
   * and each instance of the target entity may be associated with multiple instances of the source entity.
   *
   */
  OneOrManyToMany,

  /**
   * One or more instances of the source entity may be associated with one or more instances of the target entity,
   * and each instance of the target entity may be associated with one or more instances of the source entity.
   *
   */
  OneOrManyToOneOrMany,
}

/**
 * Data Transfer Object for Cardinality.
 */
export interface CardinalityDTO extends Identifiable {
  /**
   * Code representing the type of cardinality.
   */
  linkTypeCode: CardinalityType;
}

/**
 * Represents a cardinality relationship with additional properties for source and destination classes.
 */
export interface CardinalityDetails
  extends CommonCardinality,
    CardinalityPatch {
  linkPorts: LinkPort[];
  linkToLink?: LinkToLink;
  idFromClass?: number;
  idToClass?: number;
}

export interface CommonCardinality {
  /**
   * Identifier for the source template class.
   */
  idSourceTempClass: number;

  /**
   * Identifier for the destination template class.
   */
  idDestinationTempClass: number;

  color: string;
}

export interface CardinalityCreate
  extends LinkPortPatch,
    CommonCardinality,
    DiagramIdentifiable,
    CardinalityPatch {}

export interface CreatedCardinality
  extends CommonCardinality,
    CardinalityPatch {
  linkPort: LinkPort;
}

/**
 * Represents a patch for cardinality with basic properties.
 */
export interface CardinalityPatch extends NamedEntity {
  /**
   * Identifier for the link type.
   */
  idLinkType: number;

  color: string;

  idSourceTempClass: number;

  idDestinationTempClass: number;

  segmentOffset: string;

  linkPort: LinkPort;

  fromComment: string;

  toComment: string;
}

/**
 * Represents a link that has been deleted.
 */
export interface DeletedLink extends Identifiable, DiagramIdentifiable {
  /**
   * Identifier for the source class of the link.
   */
  idSourceClass: number;

  /**
   * Identifier for the destination class of the link.
   */
  idDestinationClass: number;

  /**
   * Identifier for the link.
   */
  idLink: number;
}

/**
 * Represents the type of a link with 'from' and 'to' properties.
 */
export interface ILinkType {
  /**
   * Source of the link.
   */
  from: string;

  /**
   * Destination of the link.
   */
  to: string;
}

export interface LinkPort extends LinkPortPatch, DiagramIdentifiable {
  idLink: number;
  segmentOffset: string;
}

export interface LinkPortPatch extends Identifiable {
  /**
   * Source port of the cardinality.
   */
  sourcePort: string;

  /**
   * Destination port of the cardinality.
   */
  destinationPort: string;
}

export interface LinkToLink extends Identifiable {
  port: string;
  idAssociativeClass: number;
  idLink: number;
}
